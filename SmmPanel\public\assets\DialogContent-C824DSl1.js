import{r as d,cb as p,j as c,aV as v,c5 as u,c6 as x,cT as g,cd as m}from"./index-CP4gzJXp.js";import{d as f}from"./dialogTitleClasses-XEjvqc9B.js";const C=s=>{const{classes:o,dividers:t}=s;return x({root:["root",t&&"dividers"]},g,o)},D=v("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(s,o)=>{const{ownerState:t}=s;return[o.root,t.dividers&&o.dividers]}})(m(({theme:s})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:o})=>o.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(s.vars||s).palette.divider}`,borderBottom:`1px solid ${(s.vars||s).palette.divider}`}},{props:({ownerState:o})=>!o.dividers,style:{[`.${f.root} + &`]:{paddingTop:0}}}]}))),T=d.forwardRef(function(o,t){const e=p({props:o,name:"MuiDialogContent"}),{className:a,dividers:i=!1,...n}=e,r={...e,dividers:i},l=C(r);return c.jsx(D,{className:u(l.root,a),ownerState:r,ref:t,...n})});export{T as D};
