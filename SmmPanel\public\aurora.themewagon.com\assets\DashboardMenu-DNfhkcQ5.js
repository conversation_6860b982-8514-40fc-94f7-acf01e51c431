import{r as x,j as o,a as b,b3 as j,M as g,b4 as C,b_ as E,b$ as v}from"./index-CP4gzJXp.js";const f=[{label:"Sync"},{label:"Export"},{label:"Remove",sx:{color:"error.main"}}],y=({menuItems:l=f,icon:i=o.jsx(v,{}),size:c="small",variant:u="text",sx:m})=>{const[r,s]=x.useState(null),t=!!r,p=a=>{s(a.currentTarget)},e=()=>{s(null)};return o.jsxs(o.Fragment,{children:[o.jsx(b,{sx:{color:"text.primary",...m},shape:"square",color:"neutral",size:c,variant:u,"aria-label":"more",id:"action-button","aria-controls":t?"actions-menu":void 0,"aria-expanded":t?"true":void 0,"aria-haspopup":"true",onClick:p,children:i}),o.jsx(j,{id:"actions-menu",anchorEl:r,open:t,onClose:e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{[`& .${C.paper}`]:{[`& .${E.root}`]:{minWidth:120}}},slotProps:{list:{"aria-labelledby":"action-button"}},children:l.map(({label:a,onClick:n,...h})=>o.jsx(g,{onClick:d=>{n&&n(d),e()},...h,children:a},a))})]})};export{y as D};
