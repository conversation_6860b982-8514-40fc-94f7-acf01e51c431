function d(a){return{seriesType:a,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(!(!n||!n.length)){var i=t.getData();i.filterSelf(function(o){for(var u=i.getName(o),r=0;r<n.length;r++)if(!n[r].isSelected(u))return!1;return!0})}}}}var s=function(){function a(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return a.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},a.prototype.containName=function(t){var e=this._getRawData();return e.indexOfName(t)>=0},a.prototype.indexOfName=function(t){var e=this._getDataWithEncodedVisual();return e.indexOfName(t)},a.prototype.getItemVisual=function(t,e){var n=this._getDataWithEncodedVisual();return n.getItemVisual(t,e)},a}();export{s as L,d};
