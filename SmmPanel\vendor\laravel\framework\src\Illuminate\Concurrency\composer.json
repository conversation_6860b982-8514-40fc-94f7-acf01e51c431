{"name": "illuminate/concurrency", "description": "The Illuminate Concurrency package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/console": "^12.0", "illuminate/contracts": "^12.0", "illuminate/process": "^12.0", "illuminate/support": "^12.0", "laravel/serializable-closure": "^1.3|^2.0"}, "autoload": {"psr-4": {"Illuminate\\Concurrency\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}