import{r as a,cj as ac,j as C,ck as f,h as hs,cl as lr,cf as uc,cg as dc,cm as Q,k as Qr,aV as Ne,M as ms,F as gc,cn as fc,co as hc,cp as mc,cq as me,cr as Z,cs as $e,w as Cc,n as bc,a as pc,a0 as Cs,au as Yr,ct as wc,cu as Sc,o as xc,bJ as yc,C as bs,cv as at,c5 as Fe,cw as vc,A as Ic,i as Mc,a3 as Pc,cx as Fc,cy as Zr,a7 as Ec,ad as kc,a9 as Tc,cz as Dc,cA as Hc,I as Oc,cB as Gc,cC as Lc,b0 as $c,P as Ac,c2 as Rc,c3 as ps,cD as Gt,cE as ws,cF as nt,c6 as xe,cG as Sn,cH as Ae,cc as Dt,cI as zc,cJ as ke,c1 as Vc,cK as Ss,cL as Zt,aF as jn,ci as xs,a1 as oo,cM as ys,cN as Nc,cO as Bc,cP as jc}from"./index-CP4gzJXp.js";import{P as N}from"./index-YydR91fc.js";import{S as _c}from"./Skeleton-WoLt_I4y.js";import{L as Wc}from"./LinearProgress-CUbfr40f.js";const vs=parseInt(a.version,10),ue=e=>{if(vs>=19){const t=n=>e(n,n.ref??null);return t.displayName=e.displayName??e.name,t}return a.forwardRef(e)},Be=ac,Jr=Be(C.jsx("path",{d:"M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"})),el=Be(C.jsx("path",{d:"M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"})),tl=Be(C.jsx("path",{d:"M8.59 16.59 13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})),nl=Be(C.jsx("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"})),Uc=Be(C.jsx("path",{d:"M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"})),ol=Be(C.jsx("path",{d:"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61z"})),Kc=Be(C.jsx("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}));Be(C.jsx("path",{d:"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"}));Be(C.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}));const qc=Be(C.jsx("path",{d:"M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z"})),Xc=Be(C.jsx("rect",{width:"1",height:"24",x:"11.5",rx:"0.5"})),Qc=Be(C.jsx("path",{d:"M4 15h16v-2H4v2zm0 4h16v-2H4v2zm0-8h16V9H4v2zm0-6v2h16V5H4z"})),Yc=Be(C.jsx("path",{d:"M21,8H3V4h18V8z M21,10H3v4h18V10z M21,16H3v4h18V16z"})),Zc=Be(C.jsx("path",{d:"M4 18h17v-6H4v6zM4 5v6h17V5H4z"})),Jc=Be(C.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),rl=Be(C.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),ll=Be(C.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"})),ea=Be(C.jsx("path",{d:"M19 13H5v-2h14v2z"})),ta=Be(C.jsx("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"})),sl=Be(C.jsx("path",{d:"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),il=Be(C.jsx("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})),na=Be(C.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),oa=Be(C.jsx("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"})),ra=Be(C.jsx("g",{children:C.jsx("path",{d:"M14.67,5v14H9.33V5H14.67z M15.67,19H21V5h-5.33V19z M8.33,19V5H3v14H8.33z"})})),cl=Be(C.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"}));Be(C.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"}));const la=Be(C.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z"})),sa=Be(C.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"})),ia=new TextEncoder;let io=2*1024,Ln=new ArrayBuffer(io),Uo=new Uint8Array(Ln),Qt=new Int32Array(Ln);const ca=aa;function aa(e){const t=e.length*2;t>io&&(io=t+(4-t%4),Ln=new ArrayBuffer(io),Uo=new Uint8Array(Ln),Qt=new Int32Array(Ln));const n=ia.encodeInto(e,Uo).written,o=0,r=n|0;let l=0,s=(o+r|0)+374761393|0;if(r<16)for(;(l+3|0)<r;l=l+4|0)s=Math.imul(pt(s+Math.imul(Qt[l]|0,3266489917)|0,17)|0,668265263);else{let i=o+606290984|0,c=o+2246822519|0,d=o,u=o-2654435761|0;for(;(l+15|0)<r;l=l+16|0)i=Math.imul(pt(i+Math.imul(Qt[l+0|0]|0,2246822519)|0,13)|0,2654435761),c=Math.imul(pt(c+Math.imul(Qt[l+4|0]|0,2246822519)|0,13)|0,2654435761),d=Math.imul(pt(d+Math.imul(Qt[l+8|0]|0,2246822519)|0,13)|0,2654435761),u=Math.imul(pt(u+Math.imul(Qt[l+12|0]|0,2246822519)|0,13)|0,2654435761);for(s=(((pt(i,1)|0+pt(c,7)|0)+pt(d,12)|0)+pt(u,18)|0)+r|0;(l+3|0)<r;l=l+4|0)s=Math.imul(pt(s+Math.imul(Qt[l]|0,3266489917)|0,17)|0,668265263)}for(;l<r;l=l+1|0)s=Math.imul(pt(s+Math.imul(Uo[l]|0,374761393)|0,11)|0,2654435761);return s=Math.imul(s^s>>>15,2246822519),s=Math.imul(s^s>>>13,3266489917),((s^s>>>16)>>>0).toString()}function pt(e,t){return e<<t|e>>>32-t}function ua(e){const t=new WeakSet;return JSON.stringify(e,(n,o)=>{if(o!==null&&typeof o=="object"){if(t.has(o))return null;t.add(o)}return o})}const Is={values:{xs:0,sm:600,md:900,lg:1200,xl:1536},up:e=>{const t=Is.values;return`@media (min-width:${typeof t[e]=="number"?t[e]:e}px)`}},Ms={spacingUnit:"--DataGrid-t-spacing-unit",colors:{border:{base:"--DataGrid-t-color-border-base"},foreground:{base:"--DataGrid-t-color-foreground-base",muted:"--DataGrid-t-color-foreground-muted",accent:"--DataGrid-t-color-foreground-accent",disabled:"--DataGrid-t-color-foreground-disabled",error:"--DataGrid-t-color-foreground-error"},background:{base:"--DataGrid-t-color-background-base",overlay:"--DataGrid-t-color-background-overlay",backdrop:"--DataGrid-t-color-background-backdrop"},interactive:{hover:"--DataGrid-t-color-interactive-hover",hoverOpacity:"--DataGrid-t-color-interactive-hover-opacity",focus:"--DataGrid-t-color-interactive-focus",focusOpacity:"--DataGrid-t-color-interactive-focus-opacity",disabled:"--DataGrid-t-color-interactive-disabled",disabledOpacity:"--DataGrid-t-color-interactive-disabled-opacity",selected:"--DataGrid-t-color-interactive-selected",selectedOpacity:"--DataGrid-t-color-interactive-selected-opacity"}},header:{background:{base:"--DataGrid-t-header-background-base"}},cell:{background:{pinned:"--DataGrid-t-cell-background-pinned"}},radius:{base:"--DataGrid-t-radius-base"},typography:{font:{body:"--DataGrid-t-typography-font-body",small:"--DataGrid-t-typography-font-small",large:"--DataGrid-t-typography-font-large"},fontFamily:{base:"--DataGrid-t-typography-font-family-base"},fontWeight:{light:"--DataGrid-t-typography-font-weight-light",regular:"--DataGrid-t-typography-font-weight-regular",medium:"--DataGrid-t-typography-font-weight-medium",bold:"--DataGrid-t-typography-font-weight-bold"}},transitions:{easing:{easeIn:"--DataGrid-t-transition-easing-ease-in",easeOut:"--DataGrid-t-transition-easing-ease-out",easeInOut:"--DataGrid-t-transition-easing-ease-in-out"},duration:{short:"--DataGrid-t-transition-duration-short",base:"--DataGrid-t-transition-duration-base",long:"--DataGrid-t-transition-duration-long"}},shadows:{base:"--DataGrid-t-shadow-base",overlay:"--DataGrid-t-shadow-overlay"},zIndex:{panel:"--DataGrid-t-z-index-panel",menu:"--DataGrid-t-z-index-menu"}},da=Ps(Ms),ee=f({breakpoints:Is,spacing:ga,transition:fa,keys:Ms},da);function ga(e,t,n,o){return e===void 0?wt(1):t===void 0?wt(e):n===void 0?wt(e)+" "+wt(t):o===void 0?wt(e)+" "+wt(t)+" "+wt(n):wt(e)+" "+wt(t)+" "+wt(n)+" "+wt(o)}function wt(e){return e===0?"0":`calc(var(--DataGrid-t-spacing-unit) * ${e})`}function fa(e,t){const{duration:n=ee.transitions.duration.base,easing:o=ee.transitions.easing.easeInOut,delay:r=0}=t??{};return e.map(l=>`${l} ${n} ${o} ${r}ms`).join(", ")}function Ps(e){if(typeof e=="string")return`var(${e})`;const t={};for(const n in e)Object.hasOwn(e,n)&&(t[n]=Ps(e[n]));return t}function ha(){const e=hs();return a.useMemo(()=>{const t=ca(ua(e)),n=ma(e);return{id:t,variables:n}},[e])}function ma(e){var S,y,p,v,I,D;const t=ba(e),n=(e.vars||e).palette.DataGrid,o=(n==null?void 0:n.bg)??(e.vars||e).palette.background.default,r=(n==null?void 0:n.headerBg)??o,l=(n==null?void 0:n.pinnedBg)??o,s=e.vars?`rgba(${e.vars.palette.background.defaultChannel} / ${e.vars.palette.action.disabledOpacity})`:lr(e.palette.background.default,e.palette.action.disabledOpacity),i=e.palette.mode==="dark"?`color-mix(in srgb, ${(e.vars||e).palette.background.paper} 95%, #fff)`:(e.vars||e).palette.background.paper,c=e.vars?`rgb(${e.vars.palette.primary.mainChannel})`:e.palette.primary.main,d=Ca(e),u=((y=(S=e.vars)==null?void 0:S.font)==null?void 0:y.body2)??Ko(e.typography.body2),g=((v=(p=e.vars)==null?void 0:p.font)==null?void 0:v.caption)??Ko(e.typography.caption),h=((D=(I=e.vars)==null?void 0:I.font)==null?void 0:D.body1)??Ko(e.typography.body1),m=ee.keys;return{[m.spacingUnit]:e.vars?e.vars.spacing??e.spacing(1):e.spacing(1),[m.colors.border.base]:t,[m.colors.background.base]:o,[m.colors.background.overlay]:i,[m.colors.background.backdrop]:s,[m.colors.foreground.base]:(e.vars||e).palette.text.primary,[m.colors.foreground.muted]:(e.vars||e).palette.text.secondary,[m.colors.foreground.accent]:(e.vars||e).palette.primary.dark,[m.colors.foreground.disabled]:(e.vars||e).palette.text.disabled,[m.colors.foreground.error]:(e.vars||e).palette.error.dark,[m.colors.interactive.hover]:(e.vars||e).palette.action.hover,[m.colors.interactive.hoverOpacity]:(e.vars||e).palette.action.hoverOpacity,[m.colors.interactive.focus]:al((e.vars||e).palette.primary.main),[m.colors.interactive.focusOpacity]:(e.vars||e).palette.action.focusOpacity,[m.colors.interactive.disabled]:al((e.vars||e).palette.action.disabled),[m.colors.interactive.disabledOpacity]:(e.vars||e).palette.action.disabledOpacity,[m.colors.interactive.selected]:c,[m.colors.interactive.selectedOpacity]:(e.vars||e).palette.action.selectedOpacity,[m.header.background.base]:r,[m.cell.background.pinned]:l,[m.radius.base]:d,[m.typography.fontFamily.base]:e.typography.fontFamily,[m.typography.fontWeight.light]:e.typography.fontWeightLight,[m.typography.fontWeight.regular]:e.typography.fontWeightRegular,[m.typography.fontWeight.medium]:e.typography.fontWeightMedium,[m.typography.fontWeight.bold]:e.typography.fontWeightBold,[m.typography.font.body]:u,[m.typography.font.small]:g,[m.typography.font.large]:h,[m.transitions.easing.easeIn]:e.transitions.easing.easeIn,[m.transitions.easing.easeOut]:e.transitions.easing.easeOut,[m.transitions.easing.easeInOut]:e.transitions.easing.easeInOut,[m.transitions.duration.short]:`${e.transitions.duration.shorter}ms`,[m.transitions.duration.base]:`${e.transitions.duration.short}ms`,[m.transitions.duration.long]:`${e.transitions.duration.standard}ms`,[m.shadows.base]:(e.vars||e).shadows[2],[m.shadows.overlay]:(e.vars||e).shadows[8],[m.zIndex.panel]:(e.vars||e).zIndex.modal,[m.zIndex.menu]:(e.vars||e).zIndex.modal}}function Ca(e){return e.vars?e.vars.shape.borderRadius:typeof e.shape.borderRadius=="number"?`${e.shape.borderRadius}px`:e.shape.borderRadius}function ba(e){return e.vars?e.vars.palette.TableCell.border:e.palette.mode==="light"?uc(lr(e.palette.divider,1),.88):dc(lr(e.palette.divider,1),.68)}function pa(e,t){return`rgba(from ${e} r g b / ${t})`}function al(e){return pa(e,1)}function Ko(e){if(e)return`${e.fontWeight} ${e.fontSize} / ${e.lineHeight} ${e.fontFamily}`}const wa=["id","label","labelId","material","disabled","slotProps","onChange","onKeyDown","onOpen","onClose","size","style","fullWidth"],Sa=["onRowsPerPageChange","material","disabled"],xa=["material"],ya=["autoFocus","label","fullWidth","slotProps","className","material"],va=["material"],Ia=["material"],Ma=["material"],Pa=["material"],Fa=["material"],Ea=["material"],ka=["material"],Ta=["material"],Da=["material","label","className"],Ha=["material"],Oa=["inert","iconStart","iconEnd","children","material"],Ga=["slotProps","material"],La=["id","multiple","freeSolo","options","getOptionLabel","isOptionEqualToValue","value","onChange","label","placeholder","slotProps","material"],$a=["key"],Aa=["inputProps","InputProps","InputLabelProps"],Ra=["slotProps","material"],za=["ref","open","children","className","clickAwayTouchEvent","clickAwayMouseEvent","flip","focusTrap","onExited","onClickAway","onDidShow","onDidHide","id","target","transition","placement","material"],Va=["native"],ul=Ne(Oc)(({theme:e})=>({[`&.${Gc.positionEnd} .${Lc.sizeSmall}`]:{marginRight:e.spacing(-.75)}})),Fs=Ne(Mc,{shouldForwardProp:e=>e!=="fullWidth"})(({theme:e})=>({gap:e.spacing(.5),margin:0,overflow:"hidden",[`& .${Pc.label}`]:{fontSize:e.typography.pxToRem(14),overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},variants:[{props:{fullWidth:!0},style:{width:"100%"}}]})),dl=Ne(Tc,{shouldForwardProp:e=>e!=="density"})(({theme:e})=>({variants:[{props:{density:"compact"},style:{padding:e.spacing(.5)}}]})),Na=Ne(Ec)({[`& .${kc.primary}`]:{overflowX:"clip",textOverflow:"ellipsis",maxWidth:"300px"}}),Ba=ue(function(t,n){const{id:o,label:r,labelId:l,material:s,disabled:i,slotProps:c,onChange:d,onKeyDown:u,onOpen:g,onClose:h,size:m,style:S,fullWidth:y}=t,p=Q(t,wa),v={PaperProps:{onKeyDown:u}};return h&&(v.onClose=h),C.jsxs(gc,{size:m,fullWidth:y,style:S,disabled:i,ref:n,children:[C.jsx(fc,{id:l,htmlFor:o,shrink:!0,variant:"outlined",children:r}),C.jsx(hc,f({id:o,labelId:l,label:r,displayEmpty:!0,onChange:d},p,{variant:"outlined",notched:!0,inputProps:c==null?void 0:c.htmlInput,onOpen:g,MenuProps:v,size:m},s))]})}),ja=Ne(Fc)(({theme:e})=>({[`& .${Zr.selectLabel}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"block"}},[`& .${Zr.input}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"inline-flex"}}})),_a=ue(function(t,n){const{onRowsPerPageChange:o,material:r,disabled:l}=t,s=Q(t,Sa),i=a.useMemo(()=>{if(l)return{backIconButtonProps:{disabled:!0},nextIconButtonProps:{disabled:!0}}},[l]),c=me(),d=Z(),{estimatedRowCount:u}=d;return C.jsx(ja,f({component:"div",onRowsPerPageChange:$e(g=>{o==null||o(Number(g.target.value))}),labelRowsPerPage:c.current.getLocaleText("paginationRowsPerPage"),labelDisplayedRows:g=>c.current.getLocaleText("paginationDisplayedRows")(f({},g,{estimated:u})),getItemAriaLabel:c.current.getLocaleText("paginationItemAriaLabel")},i,s,r,{ref:n}))}),Wa=ue(function(t,n){const{material:o}=t,r=Q(t,xa);return C.jsx(vc,f({},r,o,{ref:n}))}),Ua=ue(function(t,n){const{autoFocus:o,label:r,fullWidth:l,slotProps:s,className:i,material:c}=t,d=Q(t,ya),u=a.useRef(null),g=at(u,n),h=a.useRef(null);return a.useEffect(()=>{var m;if(o){const S=(m=u.current)==null?void 0:m.querySelector("input");S==null||S.focus({preventScroll:!0})}else o===!1&&h.current&&h.current.stop({})},[o]),r?C.jsx(Fs,{className:i,control:C.jsx(dl,f({},d,c,{inputProps:s==null?void 0:s.htmlInput,ref:g,touchRippleRef:h})),label:r,fullWidth:l}):C.jsx(dl,f({},d,c,{className:Fe(i,c==null?void 0:c.className),inputProps:s==null?void 0:s.htmlInput,ref:g,touchRippleRef:h}))}),Ka=ue(function(t,n){const{material:o}=t,r=Q(t,va);return C.jsx(yc,f({},r,o,{ref:n}))}),qa=ue(function(t,n){const{material:o}=t,r=Q(t,Ia);return C.jsx(xc,f({},r,o,{ref:n}))}),Xa=ue(function(t,n){const{material:o}=t,r=Q(t,Ma);return C.jsx(Wc,f({},r,o,{ref:n}))}),Qa=ue(function(t,n){const{material:o}=t,r=Q(t,Pa);return C.jsx(pc,f({},r,o,{ref:n}))}),Ya=ue(function(t,n){const{material:o}=t,r=Q(t,Fa);return C.jsx(bs,f({},r,o,{ref:n}))}),Za=ue(function(t,n){const{material:o}=t,r=Q(t,Ea);return C.jsx(bc,f({},r,o,{ref:n}))}),Ja=ue(function(t,n){const{material:o}=t,r=Q(t,ka);return C.jsx(Cc,f({},r,o,{ref:n}))}),eu=ue(function(t,n){const{material:o}=t,r=Q(t,Ta);return C.jsx(_c,f({},r,o,{ref:n}))}),tu=ue(function(t,n){const{material:o,label:r,className:l}=t,s=Q(t,Da);return r?C.jsx(Fs,{className:l,control:C.jsx(Qr,f({},s,o,{ref:n})),label:r}):C.jsx(Qr,f({},s,o,{className:l,ref:n}))}),nu=ue(function(t,n){const{material:o}=t,r=Q(t,Ha);return C.jsx(wc,f({},r,o,{ref:n}))});function ou(e){const{inert:t,iconStart:n,iconEnd:o,children:r,material:l}=e,s=Q(e,Oa);return t&&(s.disableRipple=!0),a.createElement(ms,f({},s,l),[n&&C.jsx(Yr,{children:n},"1"),C.jsx(Na,{children:r},"2"),o&&C.jsx(Yr,{children:o},"3")])}function ru(e){const{slotProps:t,material:n}=e,o=Q(e,Ga);return C.jsx(Cs,f({variant:"outlined"},o,n,{inputProps:t==null?void 0:t.htmlInput,InputProps:pr(t==null?void 0:t.input),InputLabelProps:f({shrink:!0},t==null?void 0:t.inputLabel)}))}function lu(e){const t=Z(),{id:n,multiple:o,freeSolo:r,options:l,getOptionLabel:s,isOptionEqualToValue:i,value:c,onChange:d,label:u,placeholder:g,slotProps:h,material:m}=e,S=Q(e,La);return C.jsx(Ic,f({id:n,multiple:o,freeSolo:r,options:l,getOptionLabel:s,isOptionEqualToValue:i,value:c,onChange:d,renderTags:(y,p)=>y.map((v,I)=>{const D=p({index:I}),{key:H}=D,k=Q(D,$a);return C.jsx(bs,f({variant:"outlined",size:"small",label:typeof v=="string"?v:s==null?void 0:s(v)},k),H)}),renderInput:y=>{var H;const{inputProps:p,InputProps:v,InputLabelProps:I}=y,D=Q(y,Aa);return C.jsx(Cs,f({},D,{label:u,placeholder:g,inputProps:p,InputProps:pr(v,!1),InputLabelProps:f({shrink:!0},I)},h==null?void 0:h.textField,(H=t.slotProps)==null?void 0:H.baseTextField))}},S,m))}function su(e){return C.jsx(Sc,f({},pr(e)))}function pr(e,t=!0){if(!e)return;const{slotProps:n,material:o}=e,l=Q(e,Ra);t&&(l.startAdornment&&(l.startAdornment=C.jsx(ul,{position:"start",children:l.startAdornment})),l.endAdornment&&(l.endAdornment=C.jsx(ul,{position:"end",children:l.endAdornment})));for(const s in o)Object.hasOwn(o,s)&&(l[s]=o[s]);return n!=null&&n.htmlInput&&(l.inputProps?l.inputProps=f({},l.inputProps,n==null?void 0:n.htmlInput):l.inputProps=n==null?void 0:n.htmlInput),l}const iu={"bottom-start":"top left","bottom-end":"top right"};function cu(e){const{open:t,children:n,className:o,flip:r,onExited:l,onDidShow:s,onDidHide:i,id:c,target:d,transition:u,placement:g,material:h}=e,m=Q(e,za),S=a.useMemo(()=>{const p=[{name:"preventOverflow",options:{padding:8}}];return r&&p.push({name:"flip",enabled:!0,options:{rootBoundary:"document"}}),(s||i)&&p.push({name:"isPlaced",enabled:!0,phase:"main",fn:()=>{s==null||s()},effect:()=>()=>{i==null||i()}}),p},[r,s,i]);let y;if(!u)y=gl(e,n);else{const p=v=>I=>{v&&v(),l&&l(I)};y=v=>{var I;return gl(e,C.jsx($c,f({},v.TransitionProps,{style:{transformOrigin:iu[v.placement]},onExited:p((I=v.TransitionProps)==null?void 0:I.onExited),children:C.jsx(Ac,{children:n})})))}}return C.jsx(mc,f({id:c,className:o,open:t,anchorEl:d,transition:u,placement:g,modifiers:S},m,h,{children:y}))}function gl(e,t){return uu(e,au(e,t))}function au(e,t){return e.onClickAway===void 0?t:C.jsx(Hc,{onClickAway:e.onClickAway,touchEvent:e.clickAwayTouchEvent,mouseEvent:e.clickAwayMouseEvent,children:t})}function uu(e,t){return e.focusTrap===void 0?t:C.jsx(Dc,{open:!0,disableEnforceFocus:!0,disableAutoFocus:!0,children:C.jsx("div",{tabIndex:-1,children:t})})}function du(e){let{native:t}=e,n=Q(e,Va);return t?C.jsx("option",f({},n)):C.jsx(ms,f({},n))}const gu={booleanCellTrueIcon:il,booleanCellFalseIcon:rl,columnMenuIcon:Jc,openFilterButtonIcon:Uc,filterPanelDeleteIcon:rl,columnFilteredIcon:ol,columnSelectorIcon:qc,columnSortedAscendingIcon:Jr,columnSortedDescendingIcon:el,columnResizeIcon:Xc,densityCompactIcon:Qc,densityStandardIcon:Yc,densityComfortableIcon:Zc,exportIcon:sa,moreActionsIcon:na,treeDataCollapseIcon:nl,treeDataExpandIcon:tl,groupingCriteriaCollapseIcon:nl,groupingCriteriaExpandIcon:tl,detailPanelExpandIcon:ll,detailPanelCollapseIcon:ea,rowReorderIcon:sl,quickFilterIcon:Kc,quickFilterClearIcon:cl,columnMenuHideIcon:oa,columnMenuSortAscendingIcon:Jr,columnMenuSortDescendingIcon:el,columnMenuUnsortIcon:null,columnMenuFilterIcon:ol,columnMenuManageColumnsIcon:ra,columnMenuClearIcon:cl,loadIcon:ta,filterPanelAddIcon:ll,filterPanelRemoveAllIcon:la,columnReorderIcon:sl,menuItemCheckIcon:il},fu={baseAutocomplete:lu,baseBadge:Wa,baseCheckbox:Ua,baseChip:Ya,baseCircularProgress:Ka,baseDivider:qa,baseInput:su,baseLinearProgress:Xa,baseMenuList:nu,baseMenuItem:ou,baseTextField:ru,baseButton:Qa,baseIconButton:Za,baseTooltip:Ja,basePagination:_a,basePopper:cu,baseSelect:Ba,baseSelectOption:du,baseSkeleton:eu,baseSwitch:tu},hu=f({},fu,gu);var ho=Symbol("NOT_FOUND");function mu(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function Cu(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function bu(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(n=>typeof n=="function")){const n=e.map(o=>typeof o=="function"?`function ${o.name||"unnamed"}()`:typeof o).join(", ");throw new TypeError(`${t}[${n}]`)}}var fl=e=>Array.isArray(e)?e:[e];function pu(e){const t=Array.isArray(e[0])?e[0]:e;return bu(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function wu(e,t){const n=[],{length:o}=e;for(let r=0;r<o;r++)n.push(e[r].apply(null,t));return n}function Su(e){let t;return{get(n){return t&&e(t.key,n)?t.value:ho},put(n,o){t={key:n,value:o}},getEntries(){return t?[t]:[]},clear(){t=void 0}}}function xu(e,t){let n=[];function o(i){const c=n.findIndex(d=>t(i,d.key));if(c>-1){const d=n[c];return c>0&&(n.splice(c,1),n.unshift(d)),d.value}return ho}function r(i,c){o(i)===ho&&(n.unshift({key:i,value:c}),n.length>e&&n.pop())}function l(){return n}function s(){n=[]}return{get:o,put:r,getEntries:l,clear:s}}var yu=(e,t)=>e===t;function vu(e){return function(n,o){if(n===null||o===null||n.length!==o.length)return!1;const{length:r}=n;for(let l=0;l<r;l++)if(!e(n[l],o[l]))return!1;return!0}}function Es(e,t){const n=typeof t=="object"?t:{equalityCheck:t},{equalityCheck:o=yu,maxSize:r=1,resultEqualityCheck:l}=n,s=vu(o);let i=0;const c=r<=1?Su(s):xu(r,s);function d(){let u=c.get(arguments);if(u===ho){if(u=e.apply(null,arguments),i++,l){const h=c.getEntries().find(m=>l(m.value,u));h&&(u=h.value,i!==0&&i--)}c.put(arguments,u)}return u}return d.clearCache=()=>{c.clear(),d.resetResultsCount()},d.resultsCount=()=>i,d.resetResultsCount=()=>{i=0},d}var Iu=class{constructor(e){this.value=e}deref(){return this.value}},Mu=typeof WeakRef<"u"?WeakRef:Iu,Pu=0,hl=1;function ro(){return{s:Pu,v:void 0,o:null,p:null}}function ks(e,t={}){let n=ro();const{resultEqualityCheck:o}=t;let r,l=0;function s(){var g;let i=n;const{length:c}=arguments;for(let h=0,m=c;h<m;h++){const S=arguments[h];if(typeof S=="function"||typeof S=="object"&&S!==null){let y=i.o;y===null&&(i.o=y=new WeakMap);const p=y.get(S);p===void 0?(i=ro(),y.set(S,i)):i=p}else{let y=i.p;y===null&&(i.p=y=new Map);const p=y.get(S);p===void 0?(i=ro(),y.set(S,i)):i=p}}const d=i;let u;if(i.s===hl)u=i.v;else if(u=e.apply(null,arguments),l++,o){const h=((g=r==null?void 0:r.deref)==null?void 0:g.call(r))??r;h!=null&&o(h,u)&&(u=h,l!==0&&l--),r=typeof u=="object"&&u!==null||typeof u=="function"?new Mu(u):u}return d.s=hl,d.v=u,u}return s.clearCache=()=>{n=ro(),s.resetResultsCount()},s.resultsCount=()=>l,s.resetResultsCount=()=>{l=0},s}function Ts(e,...t){const n=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,o=(...r)=>{let l=0,s=0,i,c={},d=r.pop();typeof d=="object"&&(c=d,d=r.pop()),mu(d,`createSelector expects an output function after the inputs, but received: [${typeof d}]`);const u={...n,...c},{memoize:g,memoizeOptions:h=[],argsMemoize:m=ks,argsMemoizeOptions:S=[]}=u,y=fl(h),p=fl(S),v=pu(r),I=g(function(){return l++,d.apply(null,arguments)},...y),D=m(function(){s++;const k=wu(v,arguments);return i=I.apply(null,k),i},...p);return Object.assign(D,{resultFunc:d,memoizedResultFunc:I,dependencies:v,dependencyRecomputations:()=>s,resetDependencyRecomputations:()=>{s=0},lastResult:()=>i,recomputations:()=>l,resetRecomputations:()=>{l=0},memoize:g,argsMemoize:m})};return Object.assign(o,{withTypes:()=>o}),o}var Fu=Ts(ks),Eu=Object.assign((e,t=Fu)=>{Cu(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const n=Object.keys(e),o=n.map(l=>e[l]);return t(o,(...l)=>l.reduce((s,i,c)=>(s[n[c]]=i,s),{}))},{withTypes:()=>Eu});const ku=Ts({memoize:Es,memoizeOptions:{maxSize:1,equalityCheck:Object.is}}),Tu=(e,t,n,o,r,l,...s)=>{if(s.length>0)throw new Error("Unsupported number of selectors");let i;if(e&&t&&n&&o&&r&&l)i=(c,d,u,g)=>{const h=e(c,d,u,g),m=t(c,d,u,g),S=n(c,d,u,g),y=o(c,d,u,g),p=r(c,d,u,g);return l(h,m,S,y,p,d,u,g)};else if(e&&t&&n&&o&&r)i=(c,d,u,g)=>{const h=e(c,d,u,g),m=t(c,d,u,g),S=n(c,d,u,g),y=o(c,d,u,g);return r(h,m,S,y,d,u,g)};else if(e&&t&&n&&o)i=(c,d,u,g)=>{const h=e(c,d,u,g),m=t(c,d,u,g),S=n(c,d,u,g);return o(h,m,S,d,u,g)};else if(e&&t&&n)i=(c,d,u,g)=>{const h=e(c,d,u,g),m=t(c,d,u,g);return n(h,m,d,u,g)};else if(e&&t)i=(c,d,u,g)=>{const h=e(c,d,u,g);return t(h,d,u,g)};else if(e)i=e;else throw new Error("Missing arguments");return i},Du=(...e)=>{const t=new WeakMap;let n=1;const o=e[e.length-1],r=e.length-1||1,l=Math.max(o.length-r,0);if(l>3)throw new Error("Unsupported number of arguments");return(i,c,d,u)=>{let g=i.__cacheKey__;g||(g={id:n},i.__cacheKey__=g,n+=1);let h=t.get(g);if(!h){let m=e;const S=[void 0,void 0,void 0];switch(l){case 0:break;case 1:{m=[...e.slice(0,-1),()=>S[0],o];break}case 2:{m=[...e.slice(0,-1),()=>S[0],()=>S[1],o];break}case 3:{m=[...e.slice(0,-1),()=>S[0],()=>S[1],()=>S[2],o];break}default:throw new Error("Unsupported number of arguments")}h=ku(...m),h.selectorArgs=S,t.set(g,h)}switch(l){case 3:h.selectorArgs[2]=u;case 2:h.selectorArgs[1]=d;case 1:h.selectorArgs[0]=c}switch(l){case 0:return h(i);case 1:return h(i,c);case 2:return h(i,c,d);case 3:return h(i,c,d,u);default:throw new Error("unreachable")}}};class wr{static create(t){return new wr(t)}constructor(t){this.state=void 0,this.listeners=void 0,this.subscribe=n=>(this.listeners.add(n),()=>{this.listeners.delete(n)}),this.getSnapshot=()=>this.state,this.update=n=>{this.state!==n&&(this.state=n,this.listeners.forEach(o=>o(n)))},this.state=t,this.listeners=new Set}apply(t){for(const n in t)if(!Object.is(this.state[n],t[n])){this.update(f({},this.state,t));return}}set(t,n){Object.is(this.state[t],n)||this.update(f({},this.state,{[t]:n}))}}const oe=(...e)=>{const t=Tu(...e);return(o,r,l,s)=>t(Sr(o),r,l,s)},Te=(...e)=>{const t=Du(...e);return(o,r,l,s)=>t(Sr(o),r,l,s)},je=e=>(t,n)=>e(Sr(t),n);function Sr(e){return"current"in e?e.current.state:e}const Re=je(e=>e.dimensions),Mo=oe(Re,e=>e.columnsTotalWidth),xr=oe(Re,e=>e.rowHeight),Hu=oe(Re,e=>e.contentSize.height),Ou=oe(Re,e=>e.hasScrollX),Gu=oe(Re,e=>e.hasScrollY),Ds=oe(Re,e=>e.columnsTotalWidth<e.viewportOuterSize.width),Lu=oe(Re,e=>e.headerHeight),$u=oe(Re,e=>e.groupHeaderHeight);oe(Re,e=>e.headerFilterHeight);const Au=oe(Re,e=>e.hasScrollX?e.scrollbarSize:0),Hs=oe(Re,e=>e.hasScrollY?e.scrollbarSize:0),Ru=oe(Re,Au,(e,t)=>{const n=e.viewportOuterSize.height-e.minimumSize.height>0;return!(t===0&&!n)}),zu=Object.is;function Os(e,t){if(e===t)return!0;if(!(e instanceof Object)||!(t instanceof Object))return!1;let n=0,o=0;for(const r in e)if(n+=1,!zu(e[r],t[r])||!(r in t))return!1;for(const r in t)o+=1;return n===o}function Lt(e){return a.memo(e,Os)}const Vu={noRowsLabel:"No rows",noResultsOverlayLabel:"No results found.",noColumnsOverlayLabel:"No columns",noColumnsOverlayManageColumns:"Manage columns",emptyPivotOverlayLabel:"Add fields to rows, columns, and values to create a pivot table",toolbarDensity:"Density",toolbarDensityLabel:"Density",toolbarDensityCompact:"Compact",toolbarDensityStandard:"Standard",toolbarDensityComfortable:"Comfortable",toolbarColumns:"Columns",toolbarColumnsLabel:"Select columns",toolbarFilters:"Filters",toolbarFiltersLabel:"Show filters",toolbarFiltersTooltipHide:"Hide filters",toolbarFiltersTooltipShow:"Show filters",toolbarFiltersTooltipActive:e=>e!==1?`${e} active filters`:`${e} active filter`,toolbarQuickFilterPlaceholder:"Search…",toolbarQuickFilterLabel:"Search",toolbarQuickFilterDeleteIconLabel:"Clear",toolbarExport:"Export",toolbarExportLabel:"Export",toolbarExportCSV:"Download as CSV",toolbarExportPrint:"Print",toolbarExportExcel:"Download as Excel",toolbarPivot:"Pivot",toolbarAssistant:"AI Assistant",columnsManagementSearchTitle:"Search",columnsManagementNoColumns:"No columns",columnsManagementShowHideAllText:"Show/Hide All",columnsManagementReset:"Reset",columnsManagementDeleteIconLabel:"Clear",filterPanelAddFilter:"Add filter",filterPanelRemoveAll:"Remove all",filterPanelDeleteIconLabel:"Delete",filterPanelLogicOperator:"Logic operator",filterPanelOperator:"Operator",filterPanelOperatorAnd:"And",filterPanelOperatorOr:"Or",filterPanelColumns:"Columns",filterPanelInputLabel:"Value",filterPanelInputPlaceholder:"Filter value",filterOperatorContains:"contains",filterOperatorDoesNotContain:"does not contain",filterOperatorEquals:"equals",filterOperatorDoesNotEqual:"does not equal",filterOperatorStartsWith:"starts with",filterOperatorEndsWith:"ends with",filterOperatorIs:"is",filterOperatorNot:"is not",filterOperatorAfter:"is after",filterOperatorOnOrAfter:"is on or after",filterOperatorBefore:"is before",filterOperatorOnOrBefore:"is on or before",filterOperatorIsEmpty:"is empty",filterOperatorIsNotEmpty:"is not empty",filterOperatorIsAnyOf:"is any of","filterOperator=":"=","filterOperator!=":"!=","filterOperator>":">","filterOperator>=":">=","filterOperator<":"<","filterOperator<=":"<=",headerFilterOperatorContains:"Contains",headerFilterOperatorDoesNotContain:"Does not contain",headerFilterOperatorEquals:"Equals",headerFilterOperatorDoesNotEqual:"Does not equal",headerFilterOperatorStartsWith:"Starts with",headerFilterOperatorEndsWith:"Ends with",headerFilterOperatorIs:"Is",headerFilterOperatorNot:"Is not",headerFilterOperatorAfter:"Is after",headerFilterOperatorOnOrAfter:"Is on or after",headerFilterOperatorBefore:"Is before",headerFilterOperatorOnOrBefore:"Is on or before",headerFilterOperatorIsEmpty:"Is empty",headerFilterOperatorIsNotEmpty:"Is not empty",headerFilterOperatorIsAnyOf:"Is any of","headerFilterOperator=":"Equals","headerFilterOperator!=":"Not equals","headerFilterOperator>":"Greater than","headerFilterOperator>=":"Greater than or equal to","headerFilterOperator<":"Less than","headerFilterOperator<=":"Less than or equal to",headerFilterClear:"Clear filter",filterValueAny:"any",filterValueTrue:"true",filterValueFalse:"false",columnMenuLabel:"Menu",columnMenuAriaLabel:e=>`${e} column menu`,columnMenuShowColumns:"Show columns",columnMenuManageColumns:"Manage columns",columnMenuFilter:"Filter",columnMenuHideColumn:"Hide column",columnMenuUnsort:"Unsort",columnMenuSortAsc:"Sort by ASC",columnMenuSortDesc:"Sort by DESC",columnMenuManagePivot:"Manage pivot",columnHeaderFiltersTooltipActive:e=>e!==1?`${e} active filters`:`${e} active filter`,columnHeaderFiltersLabel:"Show filters",columnHeaderSortIconLabel:"Sort",footerRowSelected:e=>e!==1?`${e.toLocaleString()} rows selected`:`${e.toLocaleString()} row selected`,footerTotalRows:"Total Rows:",footerTotalVisibleRows:(e,t)=>`${e.toLocaleString()} of ${t.toLocaleString()}`,checkboxSelectionHeaderName:"Checkbox selection",checkboxSelectionSelectAllRows:"Select all rows",checkboxSelectionUnselectAllRows:"Unselect all rows",checkboxSelectionSelectRow:"Select row",checkboxSelectionUnselectRow:"Unselect row",booleanCellTrueLabel:"yes",booleanCellFalseLabel:"no",actionsCellMore:"more",pinToLeft:"Pin to left",pinToRight:"Pin to right",unpin:"Unpin",treeDataGroupingHeaderName:"Group",treeDataExpand:"see children",treeDataCollapse:"hide children",groupingColumnHeaderName:"Group",groupColumn:e=>`Group by ${e}`,unGroupColumn:e=>`Stop grouping by ${e}`,detailPanelToggle:"Detail panel toggle",expandDetailPanel:"Expand",collapseDetailPanel:"Collapse",paginationRowsPerPage:"Rows per page:",paginationDisplayedRows:({from:e,to:t,count:n,estimated:o})=>{if(!o)return`${e}–${t} of ${n!==-1?n:`more than ${t}`}`;const r=o&&o>t?`around ${o}`:`more than ${t}`;return`${e}–${t} of ${n!==-1?n:r}`},paginationItemAriaLabel:e=>e==="first"?"Go to first page":e==="last"?"Go to last page":e==="next"?"Go to next page":"Go to previous page",rowReorderingHeaderName:"Row reordering",aggregationMenuItemHeader:"Aggregation",aggregationFunctionLabelSum:"sum",aggregationFunctionLabelAvg:"avg",aggregationFunctionLabelMin:"min",aggregationFunctionLabelMax:"max",aggregationFunctionLabelSize:"size",pivotToggleLabel:"Pivot",pivotRows:"Rows",pivotColumns:"Columns",pivotValues:"Values",pivotCloseButton:"Close pivot settings",pivotSearchButton:"Search fields",pivotSearchControlPlaceholder:"Search fields",pivotSearchControlLabel:"Search fields",pivotSearchControlClear:"Clear search",pivotNoFields:"No fields",pivotMenuMoveUp:"Move up",pivotMenuMoveDown:"Move down",pivotMenuMoveToTop:"Move to top",pivotMenuMoveToBottom:"Move to bottom",pivotMenuRows:"Rows",pivotMenuColumns:"Columns",pivotMenuValues:"Values",pivotMenuOptions:"Field options",pivotMenuAddToRows:"Add to Rows",pivotMenuAddToColumns:"Add to Columns",pivotMenuAddToValues:"Add to Values",pivotMenuRemove:"Remove",pivotDragToRows:"Drag here to create rows",pivotDragToColumns:"Drag here to create columns",pivotDragToValues:"Drag here to create values",pivotYearColumnHeaderName:"(Year)",pivotQuarterColumnHeaderName:"(Quarter)",aiAssistantPanelTitle:"AI Assistant",aiAssistantPanelClose:"Close AI Assistant",aiAssistantPanelNewConversation:"New conversation",aiAssistantPanelConversationHistory:"Conversation history",aiAssistantPanelEmptyConversation:"No prompt history",aiAssistantSuggestions:"Suggestions",promptFieldLabel:"Prompt",promptFieldPlaceholder:"Type a prompt…",promptFieldPlaceholderWithRecording:"Type or record a prompt…",promptFieldPlaceholderListening:"Listening for prompt…",promptFieldSpeechRecognitionNotSupported:"Speech recognition is not supported in this browser",promptFieldSend:"Send",promptFieldRecord:"Record",promptFieldStopRecording:"Stop recording",promptRerun:"Run again",promptProcessing:"Processing…",promptAppliedChanges:"Applied changes",promptChangeGroupDescription:e=>`Group by ${e}`,promptChangeAggregationLabel:(e,t)=>`${e} (${t})`,promptChangeAggregationDescription:(e,t)=>`Aggregate ${e} (${t})`,promptChangeFilterLabel:(e,t,n)=>t==="is any of"?`${e} is any of: ${n}`:`${e} ${t} ${n}`,promptChangeFilterDescription:(e,t,n)=>t==="is any of"?`Filter where ${e} is any of: ${n}`:`Filter where ${e} ${t} ${n}`,promptChangeSortDescription:(e,t)=>`Sort by ${e} (${t})`,promptChangePivotEnableLabel:"Pivot",promptChangePivotEnableDescription:"Enable pivot",promptChangePivotColumnsLabel:e=>`Columns (${e})`,promptChangePivotColumnsDescription:(e,t)=>`${e}${t?` (${t})`:""}`,promptChangePivotRowsLabel:e=>`Rows (${e})`,promptChangePivotValuesLabel:e=>`Values (${e})`,promptChangePivotValuesDescription:(e,t)=>`${e} (${t})`};function ye(e){return Rc("MuiDataGrid",e)}const b=ps("MuiDataGrid",["aiAssistantPanel","aiAssistantPanelHeader","aiAssistantPanelTitleContainer","aiAssistantPanelTitle","aiAssistantPanelBody","aiAssistantPanelEmptyText","aiAssistantPanelFooter","aiAssistantPanelConversation","aiAssistantPanelConversationList","aiAssistantPanelConversationTitle","aiAssistantPanelSuggestions","aiAssistantPanelSuggestionsList","aiAssistantPanelSuggestionsItem","aiAssistantPanelSuggestionsLabel","actionsCell","aggregationColumnHeader","aggregationColumnHeader--alignLeft","aggregationColumnHeader--alignCenter","aggregationColumnHeader--alignRight","aggregationColumnHeaderLabel","aggregationRowOverlayWrapper","autoHeight","autosizing","mainContent","withSidePanel","booleanCell","cell--editable","cell--editing","cell--flex","cell--textCenter","cell--textLeft","cell--textRight","cell--rangeTop","cell--rangeBottom","cell--rangeLeft","cell--rangeRight","cell--pinnedLeft","cell--pinnedRight","cell--selectionMode","cell","cellCheckbox","cellEmpty","cellSkeleton","cellOffsetLeft","checkboxInput","collapsible","collapsibleTrigger","collapsibleIcon","collapsiblePanel","columnHeader","columnHeader--alignCenter","columnHeader--alignLeft","columnHeader--alignRight","columnHeader--dragging","columnHeader--moving","columnHeader--numeric","columnHeader--sortable","columnHeader--sorted","columnHeader--filtered","columnHeader--pinnedLeft","columnHeader--pinnedRight","columnHeader--last","columnHeader--lastUnpinned","columnHeader--siblingFocused","columnHeader--filter","columnHeaderFilterInput","columnHeaderFilterOperatorLabel","columnHeaderCheckbox","columnHeaderDraggableContainer","columnHeaderTitle","columnHeaderTitleContainer","columnHeaderTitleContainerContent","columnHeader--filledGroup","columnHeader--emptyGroup","columnHeaders","columnSeparator--resizable","columnSeparator--resizing","columnSeparator--sideLeft","columnSeparator--sideRight","columnSeparator","columnsManagement","columnsManagementRow","columnsManagementHeader","columnsManagementSearchInput","columnsManagementFooter","columnsManagementScrollArea","columnsManagementEmptyText","container--top","container--bottom","detailPanel","detailPanelToggleCell","detailPanelToggleCell--expanded","footerCell","panel","panelHeader","panelWrapper","panelContent","panelFooter","paper","editBooleanCell","editInputCell","filler","filler--borderBottom","filler--pinnedLeft","filler--pinnedRight","filterForm","filterFormDeleteIcon","filterFormLogicOperatorInput","filterFormColumnInput","filterFormOperatorInput","filterFormValueInput","filterIcon","footerContainer","headerFilterRow","iconButtonContainer","iconSeparator","main","main--hasPinnedRight","main--hiddenContent","menu","menuIcon","menuIconButton","menuOpen","menuList","overlay","overlayWrapper","overlayWrapperInner","root","root--densityStandard","root--densityComfortable","root--densityCompact","root--disableUserSelection","root--noToolbar","row","row--editable","row--editing","row--firstVisible","row--lastVisible","row--dragging","row--dynamicHeight","row--detailPanelExpanded","row--borderBottom","rowReorderCellPlaceholder","rowCount","rowReorderCellContainer","rowReorderCell","rowReorderCell--draggable","rowReorderIcon","rowSkeleton","scrollArea--left","scrollArea--right","scrollArea","scrollbar","scrollbar--vertical","scrollbar--horizontal","scrollbarFiller","scrollbarFiller--header","scrollbarFiller--borderTop","scrollbarFiller--borderBottom","scrollbarFiller--pinnedRight","selectedRowCount","sortButton","sortIcon","shadowScrollArea","sidebar","sidebarHeader","toolbarContainer","toolbar","toolbarLabel","toolbarDivider","toolbarFilterList","toolbarQuickFilter","toolbarQuickFilterTrigger","toolbarQuickFilterControl","virtualScroller","virtualScroller--hasScrollX","virtualScrollerContent","virtualScrollerContent--overflowed","virtualScrollerRenderZone","withVerticalBorder","withBorderColor","cell--withRightBorder","cell--withLeftBorder","columnHeader--withRightBorder","columnHeader--withLeftBorder","treeDataGroupingCell","treeDataGroupingCellToggle","treeDataGroupingCellLoadingContainer","groupingCriteriaCell","groupingCriteriaCellToggle","groupingCriteriaCellLoadingContainer","pinnedRows","pinnedRows--top","pinnedRows--bottom","pivotPanelAvailableFields","pivotPanelField","pivotPanelField--sorted","pivotPanelFieldActionContainer","pivotPanelFieldCheckbox","pivotPanelFieldDragIcon","pivotPanelFieldList","pivotPanelFieldName","pivotPanelHeader","pivotPanelPlaceholder","pivotPanelScrollArea","pivotPanelSearchContainer","pivotPanelSection","pivotPanelSectionTitle","pivotPanelSections","pivotPanelSwitch","pivotPanelSwitchLabel","prompt","promptContent","promptText","promptFeedback","promptChangeList","promptChangesToggle","promptChangesToggleIcon","promptIcon","promptIconContainer","promptError","promptAction"]);let vt=function(e){return e.DataGrid="DataGrid",e.DataGridPro="DataGridPro",e.DataGridPremium="DataGridPremium",e}({});const ml=1e3;class Nu{constructor(t=ml){this.timeouts=new Map,this.cleanupTimeout=ml,this.cleanupTimeout=t}register(t,n,o){this.timeouts||(this.timeouts=new Map);const r=setTimeout(()=>{typeof n=="function"&&n(),this.timeouts.delete(o.cleanupToken)},this.cleanupTimeout);this.timeouts.set(o.cleanupToken,r)}unregister(t){const n=this.timeouts.get(t.cleanupToken);n&&(this.timeouts.delete(t.cleanupToken),clearTimeout(n))}reset(){this.timeouts&&(this.timeouts.forEach((t,n)=>{this.unregister({cleanupToken:n})}),this.timeouts=void 0)}}class Bu{constructor(){this.registry=new FinalizationRegistry(t=>{typeof t=="function"&&t()})}register(t,n,o){this.registry.register(t,n,o)}unregister(t){this.registry.unregister(t)}reset(){}}class yr{static create(){return new yr}}const lo={current:_u()};let Cl=0;function le(e,t,n,o){const r=a.useState(yr.create)[0],l=a.useRef(null),s=a.useRef(null);s.current=n;const i=a.useRef(null);if(!l.current&&s.current){const c=(d,u,g)=>{var h;u.defaultMuiPrevented||(h=s.current)==null||h.call(s,d,u,g)};l.current=e.current.subscribeEvent(t,c,o),Cl+=1,i.current={cleanupToken:Cl},lo.current.register(r,()=>{var d;(d=l.current)==null||d.call(l),l.current=null,i.current=null},i.current)}else!s.current&&l.current&&(l.current(),l.current=null,i.current&&(lo.current.unregister(i.current),i.current=null));a.useEffect(()=>{if(!l.current&&s.current){const c=(d,u,g)=>{var h;u.defaultMuiPrevented||(h=s.current)==null||h.call(s,d,u,g)};l.current=e.current.subscribeEvent(t,c,o)}return i.current&&lo.current&&(lo.current.unregister(i.current),i.current=null),()=>{var c;(c=l.current)==null||c.call(l),l.current=null}},[e,t,o])}const ju={isFirst:!0};function Le(e,t,n){le(e,t,n,ju)}function _u(){return typeof FinalizationRegistry<"u"?new Bu:new Nu}const Wu=Object.is,Gs=Os,Uu=(e,t)=>e===t?!0:e.length===t.length&&e.every((n,o)=>n===t[o]),Ku=(e,t)=>{let n=Object.is;return t instanceof Array?n=Uu:t instanceof Object&&(n=Gs),n(e,t)},qu=()=>({state:null,equals:null,selector:null,args:void 0}),bl=[],Xu=()=>null;function W(e,t,n=void 0,o=Wu){const r=Gt(qu),l=r.current.selector!==null,[s,i]=a.useState(l?null:t(e,n));r.current.state=s,r.current.equals=o,r.current.selector=t;const c=r.current.args;if(r.current.args=n,l&&!Ku(c,n)){const g=r.current.selector(e,r.current.args);r.current.equals(r.current.state,g)||(r.current.state=g,i(g))}const d=a.useCallback(()=>(r.current.subscription||(r.current.subscription=e.current.store.subscribe(()=>{const g=r.current.selector(e,r.current.args);r.current.equals(r.current.state,g)||(r.current.state=g,i(g))})),null),bl),u=a.useCallback(()=>()=>{r.current.subscription&&(r.current.subscription(),r.current.subscription=void 0)},bl);return ws.useSyncExternalStore(u,d,Xu),s}const Qu=.7,Yu=1.3,Zu={compact:Qu,comfortable:Yu,standard:1},dn=je(e=>e.density),xn=oe(dn,e=>Zu[e]);function $t(e){throw new Error("Failed assertion: should not be rendered")}const Ju=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","isValidating","debounceMs","isProcessingProps","onValueChange","slotProps"],ed=e=>{const{classes:t}=e;return xe({root:["editInputCell"]},ye,t)},td=Ne($t,{name:"MuiDataGrid",slot:"EditInputCell"})({font:ee.typography.font.body,padding:"1px 0","& input":{padding:"0 16px",height:"100%"}}),nd=ue((e,t)=>{const n=Z(),{id:o,value:r,field:l,colDef:s,hasFocus:i,debounceMs:c=200,isProcessingProps:d,onValueChange:u,slotProps:g}=e,h=Q(e,Ju),m=me(),S=a.useRef(null),[y,p]=a.useState(r),v=ed(n),I=a.useCallback(async H=>{const k=H.target.value,$=m.current.getColumn(l);let L=k;$.valueParser&&(L=$.valueParser(k,m.current.getRow(o),$,m)),p(L),m.current.setEditCellValue({id:o,field:l,value:L,debounceMs:c,unstable_skipValueParser:!0},H),u&&await u(H,k)},[m,c,l,o,u]),D=m.current.unstable_getEditCellMeta(o,l);return a.useEffect(()=>{(D==null?void 0:D.changeReason)!=="debouncedSetEditCellValue"&&p(r)},[D,r]),nt(()=>{i&&S.current.focus()},[i]),C.jsx(td,f({as:n.slots.baseInput,inputRef:S,className:v.root,ownerState:n,fullWidth:!0,type:s.type==="number"?s.type:"text",value:y??"",onChange:I,endAdornment:d?C.jsx(n.slots.loadIcon,{fontSize:"small",color:"action"}):void 0},h,g==null?void 0:g.root,{ref:t}))}),od=e=>C.jsx(nd,f({},e)),Pt=je(e=>e.rows),_n=oe(Pt,e=>e.totalRowCount),rd=oe(Pt,e=>e.loading),ld=oe(Pt,e=>e.totalTopLevelRowCount),Ct=oe(Pt,e=>e.dataRowIdToModelLookup);oe(Ct,(e,t)=>e[t]);const tt=oe(Pt,e=>e.tree),Et=oe(tt,(e,t)=>e[t]),sd=oe(Pt,e=>e.groupsToFetch),id=oe(Pt,e=>e.groupingName),pl=oe(Pt,e=>e.treeDepths),yn=Te(Pt,e=>{const t=Object.entries(e.treeDepths);return t.length===0?1:(t.filter(([,n])=>n>0).map(([n])=>Number(n)).sort((n,o)=>o-n)[0]??0)+1}),Yt=oe(Pt,e=>e.dataRowIds),cd=Te(Yt,Ct,(e,t)=>e.reduce((n,o)=>(t[o]&&n.push(t[o]),n),[])),ad=oe(Pt,e=>e==null?void 0:e.additionalRowGroups),vn=Te(ad,e=>{var n,o;const t=e==null?void 0:e.pinnedRows;return{bottom:((n=t==null?void 0:t.bottom)==null?void 0:n.map(r=>({id:r.id,model:r.model??{}})))??[],top:((o=t==null?void 0:t.top)==null?void 0:o.map(r=>({id:r.id,model:r.model??{}})))??[]}}),Ls=oe(vn,e=>{var t,n;return(((t=e==null?void 0:e.top)==null?void 0:t.length)||0)+(((n=e==null?void 0:e.bottom)==null?void 0:n.length)||0)}),$s=(e,t)=>t&&e.length>1?[e[0]]:e,wl=(e,t)=>n=>f({},n,{sorting:f({},n.sorting,{sortModel:$s(e,t)})}),ud=e=>e==="desc",dd=(e,t)=>{const n=t.current.getColumn(e.field);if(!n||e.sort===null)return null;let o;return n.getSortComparator?o=n.getSortComparator(e.sort):o=ud(e.sort)?(...l)=>-1*n.sortComparator(...l):n.sortComparator,o?{getSortCellParams:l=>({id:l,field:n.field,rowNode:Et(t,l),value:t.current.getCellValue(l,n.field),api:t.current}),comparator:o}:null},gd=(e,t,n)=>e.reduce((o,r,l)=>{if(o!==0)return o;const s=t.params[l],i=n.params[l];return o=r.comparator(s.value,i.value,s,i),o},0),fd=(e,t)=>{const n=e.map(o=>dd(o,t)).filter(o=>!!o);return n.length===0?null:o=>o.map(r=>({node:r,params:n.map(l=>l.getSortCellParams(r.id))})).sort((r,l)=>gd(n,r,l)).map(r=>r.node.id)},Sl=(e,t)=>{const n=e.indexOf(t);return!t||n===-1||n+1===e.length?e[0]:e[n+1]},vr=(e,t)=>e==null&&t!=null?-1:t==null&&e!=null?1:e==null&&t==null?0:null,hd=new Intl.Collator,md=(e,t)=>{const n=vr(e,t);return n!==null?n:typeof e=="string"?hd.compare(e.toString(),t.toString()):e-t},As=(e,t)=>{const n=vr(e,t);return n!==null?n:Number(e)-Number(t)},Rs=(e,t)=>{const n=vr(e,t);return n!==null?n:e>t?1:e<t?-1:0},Cd=["item","applyValue","type","apiRef","focusElementRef","tabIndex","disabled","isFilterActive","slotProps","clearButton","headerFilterMenu"];function mt(e){var k,$,L;const{item:t,applyValue:n,type:o,apiRef:r,focusElementRef:l,tabIndex:s,disabled:i,slotProps:c,clearButton:d,headerFilterMenu:u}=e,g=Q(e,Cd),h=c==null?void 0:c.root,m=Sn(),[S,y]=a.useState(qo(t.value)),[p,v]=a.useState(!1),I=Ae(),D=Z(),H=a.useCallback(x=>{const w=qo(x.target.value);y(w),v(!0),m.start(D.filterDebounceMs,()=>{const T=f({},t,{value:o==="number"&&!Number.isNaN(Number(w))?Number(w):w,fromInput:I});n(T),v(!1)})},[m,D.filterDebounceMs,t,o,I,n]);return a.useEffect(()=>{(t.fromInput!==I||t.value==null)&&y(qo(t.value))},[I,t]),C.jsxs(a.Fragment,{children:[C.jsx(D.slots.baseTextField,f({id:I,label:r.current.getLocaleText("filterPanelInputLabel"),placeholder:r.current.getLocaleText("filterPanelInputPlaceholder"),value:S??"",onChange:H,type:o||"text",disabled:i,slotProps:f({},h==null?void 0:h.slotProps,{input:f({endAdornment:p?C.jsx(D.slots.loadIcon,{fontSize:"small",color:"action"}):null},(k=h==null?void 0:h.slotProps)==null?void 0:k.input),htmlInput:f({tabIndex:s},($=h==null?void 0:h.slotProps)==null?void 0:$.htmlInput)}),inputRef:l},(L=D.slotProps)==null?void 0:L.baseTextField,g,h)),u,d]})}function qo(e){if(!(e==null||e===""))return String(e)}function bd(e){return typeof e=="number"&&!Number.isNaN(e)}function pd(e){return typeof e=="function"}function Ir(e){return typeof e=="object"&&e!==null}function wd(){try{const e="__some_random_key_you_are_not_going_to_use__";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch{return!1}}function mo(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}const it=(e,t,n)=>Math.max(t,Math.min(n,e));function xl(e,t){return Array.from({length:t-e}).map((n,o)=>e+o)}function Sd(e){return()=>{let t=e+=1831565813;return t=Math.imul(t^t>>>15,t|1),t^=t+Math.imul(t^t>>>7,t|61),((t^t>>>14)>>>0)/4294967296}}function xd(e){const t=Sd(e);return(n,o)=>n+(o-n)*t()}function zs(e){return typeof structuredClone=="function"?structuredClone(e):JSON.parse(JSON.stringify(e))}const jt=(e,t)=>n=>{e&&t(n)};function Vs(e){const{item:t,applyValue:n,type:o,apiRef:r,focusElementRef:l,slotProps:s}=e,i=Ae(),[c,d]=a.useState([]),[u,g]=a.useState(t.value||[]),h=Z();a.useEffect(()=>{const p=t.value??[];g(p.map(String))},[t.value]);const m=a.useCallback((p,v)=>{g(v.map(String)),n(f({},t,{value:[...v.map(I=>o==="number"?Number(I):I)]}))},[n,t,o]),S=a.useCallback((p,v)=>{d(v===""?[]:[v])},[d]),y=h.slots.baseAutocomplete;return C.jsx(y,f({multiple:!0,freeSolo:!0,options:c,id:i,value:u,onChange:m,onInputChange:S,label:r.current.getLocaleText("filterPanelInputLabel"),placeholder:r.current.getLocaleText("filterPanelInputPlaceholder"),slotProps:{textField:{type:o||"text",inputRef:l}}},s==null?void 0:s.root))}var Tt=function(e){return e.Cell="cell",e.Row="row",e}(Tt||{}),Oe=function(e){return e.Edit="edit",e.View="view",e}(Oe||{}),Ve=function(e){return e.Edit="edit",e.View="view",e}(Ve||{}),rt=function(e){return e.And="and",e.Or="or",e}(rt||{});class yd{constructor(t){this.data=void 0,this.data=t.ids}has(t){return this.data.has(t)}select(t){this.data.add(t)}unselect(t){this.data.delete(t)}}class vd{constructor(t){this.data=void 0,this.data=t.ids}has(t){return!this.data.has(t)}select(t){this.data.delete(t)}unselect(t){this.data.add(t)}}const _t=e=>e.type==="include"?new yd(e):new vd(e);var Ot=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e.pasteKeyDown="pasteKeyDown",e}(Ot||{}),St=function(e){return e.cellFocusOut="cellFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(St||{}),Vt=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e}(Vt||{}),Ft=function(e){return e.rowFocusOut="rowFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(Ft||{});function Ns(e){return e.field!==void 0}const Bs={filteredRowsLookup:{},filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}},Rn=()=>({items:[],logicOperator:rt.And,quickFilterValues:[],quickFilterLogicOperator:rt.And});function js(e){return{current:e.current.getPublicApi()}}let Nt=function(e){return e.LEFT="left",e.RIGHT="right",e}({});const Co={left:[],right:[]},_s=je(e=>e.isRtl),It=je(e=>e.columns),kt=oe(It,e=>e.orderedFields),At=oe(It,e=>e.lookup),bt=Te(kt,At,(e,t)=>e.map(n=>t[n])),yt=oe(It,e=>e.columnVisibilityModel),Id=oe(It,e=>e.initialColumnVisibilityModel),We=Te(bt,yt,(e,t)=>e.filter(n=>t[n.field]!==!1)),In=Te(We,e=>e.map(t=>t.field)),Ws=je(e=>e.pinnedColumns);Te(Ws,kt,_s,(e,t,n)=>Us(e,t,n));const Mn=Te(It,Ws,In,_s,(e,t,n,o)=>{const r=Us(t,n,o);return{left:r.left.map(s=>e.lookup[s]),right:r.right.map(s=>e.lookup[s])}});function Us(e,t,n){var i,c;if(!Array.isArray(e.left)&&!Array.isArray(e.right)||((i=e.left)==null?void 0:i.length)===0&&((c=e.right)==null?void 0:c.length)===0)return Co;const o=(d,u)=>Array.isArray(d)?d.filter(g=>u.includes(g)):[],r=o(e.left,t),l=t.filter(d=>!r.includes(d)),s=o(e.right,l);return n?{left:s,right:r}:{left:r,right:s}}const qt=Te(We,e=>{const t=[];let n=0;for(let o=0;o<e.length;o+=1)t.push(n),n+=e[o].computedWidth;return t}),Ks=Te(bt,e=>e.filter(t=>t.filterable)),Md=Te(bt,e=>e.reduce((t,n)=>(n.filterable&&(t[n.field]=n),t),{})),Pd=Te(bt,e=>e.some(t=>t.colSpan!==void 0));let kn;function Fd(){if(kn!==void 0)return kn;try{kn=new Function("return true")()}catch{kn=!1}return kn}const sr=(e,t)=>{const n=f({},e);if(n.id==null&&(n.id=Math.round(Math.random()*1e5)),n.operator==null){const o=At(t)[n.field];n.operator=o&&o.filterOperators[0].value}return n},Mr=(e,t,n)=>{const o=e.items.length>1;let r;o&&t?r=[e.items[0]]:r=e.items;const l=o&&r.some(i=>i.id==null);return r.some(i=>i.operator==null)||l?f({},e,{items:r.map(i=>sr(i,n))}):e.items!==r?f({},e,{items:r}):e},yl=(e,t,n)=>o=>f({},o,{filterModel:Mr(e,t,n)}),zn=e=>typeof e=="string"?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):e,qs=(e,t)=>{var u;if(!e.field||!e.operator)return null;const n=t.current.getColumn(e.field);if(!n)return null;let o;if(n.valueParser){const g=n.valueParser;o=Array.isArray(e.value)?(u=e.value)==null?void 0:u.map(h=>g(h,void 0,n,t)):g(e.value,void 0,n,t)}else o=e.value;const{ignoreDiacritics:r}=t.current.rootProps;r&&(o=zn(o));const l=f({},e,{value:o}),s=n.filterOperators;if(!(s!=null&&s.length))throw new Error(`MUI X: No filter operators found for column '${n.field}'.`);const i=s.find(g=>g.value===l.operator);if(!i)throw new Error(`MUI X: No filter operator found for column '${n.field}' and operator value '${l.operator}'.`);const c=js(t),d=i.getApplyFilterFn(l,n);return typeof d!="function"?null:{item:l,fn:g=>{let h=t.current.getRowValue(g,n);return r&&(h=zn(h)),d(h,g,n,c)}}};let vl=1;const Ed=(e,t,n)=>{const{items:o}=e,r=o.map(i=>qs(i,t)).filter(i=>!!i);if(r.length===0)return null;if(n||!Fd())return(i,c)=>{const d={};for(let u=0;u<r.length;u+=1){const g=r[u];(!c||c(g.item.field))&&(d[g.item.id]=g.fn(i))}return d};const l=new Function("appliers","row","shouldApplyFilter",`"use strict";
${r.map((i,c)=>`const shouldApply${c} = !shouldApplyFilter || shouldApplyFilter(${JSON.stringify(i.item.field)});`).join(`
`)}

const result$$ = {
${r.map((i,c)=>`  ${JSON.stringify(String(i.item.id))}: !shouldApply${c} ? false : appliers[${c}].fn(row),`).join(`
`)}
};

return result$$;`.replaceAll("$$",String(vl)));return vl+=1,(i,c)=>l(r,i,c)},Xs=e=>e.quickFilterExcludeHiddenColumns??!0,kd=(e,t)=>{var i;const n=((i=e.quickFilterValues)==null?void 0:i.filter(Boolean))??[];if(n.length===0)return null;const o=Xs(e)?In(t):kt(t),r=[],{ignoreDiacritics:l}=t.current.rootProps,s=js(t);return o.forEach(c=>{const d=t.current.getColumn(c),u=d==null?void 0:d.getApplyQuickFilterFn;u&&r.push({column:d,appliers:n.map(g=>{const h=l?zn(g):g;return{fn:u(h,d,s)}})})}),function(d,u){const g={};e:for(let h=0;h<n.length;h+=1){const m=n[h];for(let S=0;S<r.length;S+=1){const{column:y,appliers:p}=r[S],{field:v}=y;if(u&&!u(v))continue;const I=p[h];let D=t.current.getRowValue(d,y);if(I.fn===null)continue;if(l&&(D=zn(D)),I.fn(D,d,y,s)){g[m]=!0;continue e}}g[m]=!1}return g}},Td=(e,t,n)=>{const o=Ed(e,t,n),r=kd(e,t);return function(s,i,c){c.passingFilterItems=(o==null?void 0:o(s,i))??null,c.passingQuickFilterValues=(r==null?void 0:r(s,i))??null}},Il=e=>e!=null,Dd=(e,t,n)=>(e.cleanedFilterItems||(e.cleanedFilterItems=n.filter(o=>qs(o,t)!==null)),e.cleanedFilterItems),Hd=(e,t,n,o,r)=>{const l=Dd(r,o,n.items),s=e.filter(Il),i=t.filter(Il);if(s.length>0){const c=u=>s.some(g=>g[u.id]);if((n.logicOperator??Rn().logicOperator)===rt.And){if(!l.every(c))return!1}else if(!l.some(c))return!1}if(i.length>0&&n.quickFilterValues!=null){const c=u=>i.some(g=>g[u]);if((n.quickFilterLogicOperator??Rn().quickFilterLogicOperator)===rt.And){if(!n.quickFilterValues.every(c))return!1}else if(!n.quickFilterValues.some(c))return!1}return!0},Od=e=>{if(!e)return null;const t=new RegExp(mo(e),"i");return(n,o,r,l)=>{let s=l.current.getRowFormattedValue(o,r);return l.current.ignoreDiacritics&&(s=zn(s)),s!=null?t.test(s.toString()):!1}},Ml=(e,t)=>n=>{if(!n.value)return null;const o=e?n.value:n.value.trim(),r=new RegExp(mo(o),"i");return l=>{if(l==null)return t;const s=r.test(String(l));return t?!s:s}},Pl=(e,t)=>n=>{if(!n.value)return null;const o=e?n.value:n.value.trim(),r=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return l=>{if(l==null)return t;const s=r.compare(o,l.toString())===0;return t?!s:s}},Fl=e=>()=>t=>{const n=t===""||t==null;return e?!n:n},Gd=(e=!1)=>[{value:"contains",getApplyFilterFn:Ml(e,!1),InputComponent:mt},{value:"doesNotContain",getApplyFilterFn:Ml(e,!0),InputComponent:mt},{value:"equals",getApplyFilterFn:Pl(e,!1),InputComponent:mt},{value:"doesNotEqual",getApplyFilterFn:Pl(e,!0),InputComponent:mt},{value:"startsWith",getApplyFilterFn:t=>{if(!t.value)return null;const n=e?t.value:t.value.trim(),o=new RegExp(`^${mo(n)}.*$`,"i");return r=>r!=null?o.test(r.toString()):!1},InputComponent:mt},{value:"endsWith",getApplyFilterFn:t=>{if(!t.value)return null;const n=e?t.value:t.value.trim(),o=new RegExp(`.*${mo(n)}$`,"i");return r=>r!=null?o.test(r.toString()):!1},InputComponent:mt},{value:"isEmpty",getApplyFilterFn:Fl(!1),requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:Fl(!0),requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:t=>{if(!Array.isArray(t.value)||t.value.length===0)return null;const n=e?t.value:t.value.map(r=>r.trim()),o=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return r=>r!=null?n.some(l=>o.compare(l,r.toString()||"")===0):!1},InputComponent:Vs}],Mt={width:100,minWidth:50,maxWidth:1/0,hideable:!0,sortable:!0,resizable:!0,filterable:!0,groupable:!0,pinnable:!0,aggregable:!0,editable:!1,sortComparator:md,type:"string",align:"left",filterOperators:Gd(),renderEditCell:od,getApplyQuickFilterFn:Od},Qs=a.createContext(void 0),Po=()=>{const e=a.useContext(Qs);if(e===void 0)throw new Error(["MUI X: Could not find the Data Grid configuration context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join(`
`));return e},Ld="MuiDataGridVariables",Pr=a.createContext({className:"unset",tag:C.jsx("style",{href:"/unset"})});function Ys(){return a.useContext(Pr).className}function $d(){return a.useContext(Pr)}function Ad(e){const t=Po(),n=Z(),o=t.hooks.useCSSVariables(),r=a.useMemo(()=>{const l=`${Ld}-${o.id}`,s=`.${l}{${Rd(o.variables)}}`,i=C.jsx("style",{href:`/${l}`,nonce:n.nonce,children:s});return{className:l,tag:i}},[n.nonce,o]);return C.jsx(Pr.Provider,{value:r,children:e.children})}function Rd(e){let t="";for(const n in e)Object.hasOwn(e,n)&&e[n]!==void 0&&(t+=`${n}:${e[n]};`);return t}const zd=["open","target","onClose","children","position","className","onExited"],Vd=e=>{const{classes:t}=e;return xe({root:["menu"]},ye,t)},Nd=Ne($t,{name:"MuiDataGrid",slot:"Menu"})({zIndex:ee.zIndex.menu,[`& .${b.menuList}`]:{outline:0}});function Fo(e){var y;const{open:t,target:n,onClose:o,children:r,position:l,className:s,onExited:i}=e,c=Q(e,zd),d=me(),u=Z(),g=Vd(u),h=Ys(),m=a.useRef(null);nt(()=>{var p,v;t?m.current=document.activeElement instanceof HTMLElement?document.activeElement:null:((v=(p=m.current)==null?void 0:p.focus)==null||v.call(p),m.current=null)},[t]),a.useEffect(()=>{const p=t?"menuOpen":"menuClose";d.current.publishEvent(p,{target:n})},[d,t,n]);const S=p=>{p.target&&(n===p.target||n!=null&&n.contains(p.target))||o(p)};return C.jsx(Nd,f({as:u.slots.basePopper,className:Fe(g.root,s,h),ownerState:u,open:t,target:n,transition:!0,placement:l,onClickAway:S,onExited:i,clickAwayMouseEvent:"onMouseDown"},c,(y=u.slotProps)==null?void 0:y.basePopper,{children:r}))}const Bd=["api","colDef","id","hasFocus","isEditable","field","value","formattedValue","row","rowNode","cellMode","tabIndex","position","focusElementRef"],jd=e=>typeof e.getActions=="function";function _d(e){var E;const{colDef:t,id:n,hasFocus:o,tabIndex:r,position:l="bottom-end",focusElementRef:s}=e,i=Q(e,Bd),[c,d]=a.useState(-1),[u,g]=a.useState(!1),h=me(),m=a.useRef(null),S=a.useRef(null),y=a.useRef(!1),p=a.useRef({}),v=Dt(),I=Ae(),D=Ae(),H=Z();if(!jd(t))throw new Error("MUI X: Missing the `getActions` property in the `GridColDef`.");const k=t.getActions(h.current.getRowParams(n)),$=k.filter(P=>!P.props.showInMenu),L=k.filter(P=>P.props.showInMenu),x=$.length+(L.length?1:0);a.useLayoutEffect(()=>{o||Object.entries(p.current).forEach(([P,z])=>{z==null||z.stop({},()=>{delete p.current[P]})})},[o]),a.useEffect(()=>{if(c<0||!m.current||c>=m.current.children.length)return;m.current.children[c].focus({preventScroll:!0})},[c]),a.useEffect(()=>{o||(d(-1),y.current=!1)},[o]),a.useImperativeHandle(s,()=>({focus(){if(!y.current){const P=k.findIndex(z=>!z.props.disabled);d(P)}}}),[k]),a.useEffect(()=>{c>=x&&d(x-1)},[c,x]);const w=()=>{g(!0),d(x-1),y.current=!0},T=()=>{g(!1)},M=P=>{P.stopPropagation(),P.preventDefault(),u?T():w()},O=P=>z=>{p.current[P]=z},G=(P,z)=>_=>{d(P),y.current=!0,z&&z(_)},A=P=>{if(x<=1)return;const z=(V,R)=>{var X;if(V<0||V>k.length)return V;const q=(R==="left"?-1:1)*(v?-1:1);return(X=k[V+q])!=null&&X.props.disabled?z(V+q,R):V+q};let _=c;P.key==="ArrowRight"?_=z(c,"right"):P.key==="ArrowLeft"&&(_=z(c,"left")),!(_<0||_>=x)&&_!==c&&(P.preventDefault(),P.stopPropagation(),d(_))},F=P=>{P.key==="Tab"&&P.preventDefault(),["Tab","Escape"].includes(P.key)&&T()};return C.jsxs("div",f({role:"menu",ref:m,tabIndex:-1,className:b.actionsCell,onKeyDown:A},i,{children:[$.map((P,z)=>a.cloneElement(P,{key:z,touchRippleRef:O(z),onClick:G(z,P.props.onClick),tabIndex:c===z?r:-1})),L.length>0&&D&&C.jsx(H.slots.baseIconButton,f({ref:S,id:D,"aria-label":h.current.getLocaleText("actionsCellMore"),"aria-haspopup":"menu","aria-expanded":u,"aria-controls":u?I:void 0,role:"menuitem",size:"small",onClick:M,touchRippleRef:O(D),tabIndex:c===$.length?r:-1},(E=H.slotProps)==null?void 0:E.baseIconButton,{children:C.jsx(H.slots.moreActionsIcon,{fontSize:"small"})})),L.length>0&&C.jsx(Fo,{open:u,target:S.current,position:l,onClose:T,children:C.jsx(H.slots.baseMenuList,{id:I,className:b.menuList,onKeyDown:F,"aria-labelledby":D,autoFocusItem:!0,children:L.map((P,z)=>a.cloneElement(P,{key:z,closeMenu:T}))})})]}))}const Wd=e=>C.jsx(_d,f({},e)),Eo="actions",Ud=f({},Mt,{sortable:!1,filterable:!1,aggregable:!1,width:100,display:"flex",align:"center",headerAlign:"center",headerName:"",disableColumnMenu:!0,disableExport:!0,renderCell:Wd,getApplyQuickFilterFn:()=>null}),et="auto-generated-group-node-root",Cn=Symbol("mui.id_autogenerated"),Kd=()=>({type:"group",id:et,depth:-1,groupingField:null,groupingKey:null,isAutoGenerated:!0,children:[],childrenFromPath:{},childrenExpanded:!0,parent:null});function qd(e,t,n="A row was provided without id in the rows prop:"){if(e==null)throw new Error(["MUI X: The Data Grid component requires all rows to have a unique `id` property.","Alternatively, you can use the `getRowId` prop to specify a custom id for each row.",n,JSON.stringify(t)].join(`
`))}const ko=(e,t,n)=>{const o=t?t(e):e.id;return qd(o,e,n),o},Xd=(e,t,n)=>{const o=t.field;if(!t||!t.valueGetter)return e[o];const r=e[t.field];return t.valueGetter(r,e,t,n)},co=({rows:e,getRowId:t,loading:n,rowCount:o})=>{const r={type:"full",rows:[]},l={};for(let s=0;s<e.length;s+=1){const i=e[s],c=ko(i,t);l[c]=i,r.rows.push(c)}return{rowsBeforePartialUpdates:e,loadingPropBeforePartialUpdates:n,rowCountPropBeforePartialUpdates:o,updates:r,dataRowIdToModelLookup:l}},Zs=({tree:e,rowCountProp:t=0})=>{const n=e[et];return Math.max(t,n.children.length+(n.footerId==null?0:1))},Js=({apiRef:e,rowCountProp:t=0,loadingProp:n,previousTree:o,previousTreeDepths:r,previousGroupsToFetch:l})=>{const s=e.current.caches.rows,{tree:i,treeDepths:c,dataRowIds:d,groupingName:u,groupsToFetch:g=[]}=e.current.applyStrategyProcessor("rowTreeCreation",{previousTree:o,previousTreeDepths:r,updates:s.updates,dataRowIdToModelLookup:s.dataRowIdToModelLookup,previousGroupsToFetch:l}),h=e.current.unstable_applyPipeProcessors("hydrateRows",{tree:i,treeDepths:c,dataRowIds:d,dataRowIdToModelLookup:s.dataRowIdToModelLookup});return e.current.caches.rows.updates={type:"partial",actions:{insert:[],modify:[],remove:[]},idToActionLookup:{}},f({},h,{totalRowCount:Math.max(t,h.dataRowIds.length),totalTopLevelRowCount:Zs({tree:h.tree,rowCountProp:t}),groupingName:u,loading:n,groupsToFetch:g})},bn=e=>e.type==="skeletonRow"||e.type==="footer"||e.type==="group"&&e.isAutoGenerated||e.type==="pinnedRow"&&e.isAutoGenerated,Fr=(e,t,n)=>{const o=e[t];if(o.type!=="group")return[];const r=[];for(let l=0;l<o.children.length;l+=1){const s=o.children[l];(!n||!bn(e[s]))&&r.push(s);const i=Fr(e,s,n);for(let c=0;c<i.length;c+=1)r.push(i[c])}return!n&&o.footerId!=null&&r.push(o.footerId),r},El=({previousCache:e,getRowId:t,updates:n,groupKeys:o})=>{if(e.updates.type==="full")throw new Error("MUI X: Unable to prepare a partial update if a full update is not applied yet.");const r=new Map;n.forEach(d=>{const u=ko(d,t,"A row was provided without id when calling updateRows():");r.has(u)?r.set(u,f({},r.get(u),d)):r.set(u,d)});const l={type:"partial",actions:{insert:[...e.updates.actions.insert??[]],modify:[...e.updates.actions.modify??[]],remove:[...e.updates.actions.remove??[]]},idToActionLookup:f({},e.updates.idToActionLookup),groupKeys:o},s=f({},e.dataRowIdToModelLookup),i={insert:{},modify:{},remove:{}};r.forEach((d,u)=>{const g=l.idToActionLookup[u];if(d._action==="delete"){if(g==="remove"||!s[u])return;g!=null&&(i[g][u]=!0),l.actions.remove.push(u),delete s[u];return}const h=s[u];if(h){g==="remove"?(i.remove[u]=!0,l.actions.modify.push(u)):g==null&&l.actions.modify.push(u),s[u]=f({},h,d);return}g==="remove"?(i.remove[u]=!0,l.actions.insert.push(u)):g==null&&l.actions.insert.push(u),s[u]=d});const c=Object.keys(i);for(let d=0;d<c.length;d+=1){const u=c[d],g=i[u];Object.keys(g).length>0&&(l.actions[u]=l.actions[u].filter(h=>!g[h]))}return{dataRowIdToModelLookup:s,updates:l,rowsBeforePartialUpdates:e.rowsBeforePartialUpdates,loadingPropBeforePartialUpdates:e.loadingPropBeforePartialUpdates,rowCountPropBeforePartialUpdates:e.rowCountPropBeforePartialUpdates}},ei="var(--DataGrid-overlayHeight, calc(var(--height) * 2))";function kl(e,t,n){const o=[];return t.forEach(r=>{const l=ko(r,n,"A row was provided without id when calling updateRows():"),s=Et(e,l);if((s==null?void 0:s.type)==="pinnedRow"){const i=e.current.caches.pinnedRows,c=i.idLookup[l];c&&(i.idLookup[l]=f({},c,r))}else o.push(r)}),o}const ti=(e,t,n)=>typeof e=="number"&&e>0?e:t,Qd="__tree_data_group__",ni="__row_group_by_columns_group__",To="__detail_panel_toggle__";let ve=function(e){return e[e.NONE=0]="NONE",e[e.LEFT=1]="LEFT",e[e.RIGHT=2]="RIGHT",e[e.VIRTUAL=3]="VIRTUAL",e}({});const Yd=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","hasFocus","tabIndex","hideDescendantCount"],Zd=e=>{const{classes:t}=e;return xe({root:["booleanCell"]},ye,t)};function Jd(e){const{value:t,rowNode:n}=e,o=Q(e,Yd),r=me(),l=Z(),s={classes:l.classes},i=Zd(s),d=W(r,yn)>0&&n.type==="group"&&l.treeData===!1,u=a.useMemo(()=>t?l.slots.booleanCellTrueIcon:l.slots.booleanCellFalseIcon,[l.slots.booleanCellFalseIcon,l.slots.booleanCellTrueIcon,t]);return d&&t===void 0?null:C.jsx(u,f({fontSize:"small",className:i.root,titleAccess:r.current.getLocaleText(t?"booleanCellTrueLabel":"booleanCellFalseLabel"),"data-value":!!t},o))}const eg=a.memo(Jd),tg=e=>e.field!==ni&&bn(e.rowNode)?"":C.jsx(eg,f({},e)),ng=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange"],og=e=>{const{classes:t}=e;return xe({root:["editBooleanCell"]},ye,t)};function rg(e){var v;const{id:t,value:n,field:o,className:r,hasFocus:l,onValueChange:s}=e,i=Q(e,ng),c=me(),d=a.useRef(null),u=Ae(),[g,h]=a.useState(n),m=Z(),S={classes:m.classes},y=og(S),p=a.useCallback(async I=>{const D=I.target.checked;s&&await s(I,D),h(D),await c.current.setEditCellValue({id:t,field:o,value:D},I)},[c,o,t,s]);return a.useEffect(()=>{h(n)},[n]),nt(()=>{l&&d.current.focus()},[l]),C.jsx("label",f({htmlFor:u,className:Fe(y.root,r)},i,{children:C.jsx(m.slots.baseCheckbox,f({id:u,inputRef:d,checked:!!g,onChange:p,size:"small"},(v=m.slotProps)==null?void 0:v.baseCheckbox))}))}const lg=e=>C.jsx(rg,f({},e)),sg=["item","applyValue","apiRef","focusElementRef","isFilterActive","headerFilterMenu","clearButton","tabIndex","slotProps"];function ig(e){var k,$;const{item:t,applyValue:n,apiRef:o,focusElementRef:r,headerFilterMenu:l,clearButton:s,tabIndex:i,slotProps:c}=e,d=Q(e,sg),[u,g]=a.useState(ao(t.value)),h=Z(),m=Ae(),S=Ae(),y=((k=h.slotProps)==null?void 0:k.baseSelect)||{},p=y.native??!1,v=(($=h.slotProps)==null?void 0:$.baseSelectOption)||{},I=a.useCallback(L=>{const x=ao(L.target.value);g(x),n(f({},t,{value:x}))},[n,t]);a.useEffect(()=>{g(ao(t.value))},[t.value]);const D=(c==null?void 0:c.root.label)??o.current.getLocaleText("filterPanelInputLabel"),H=c==null?void 0:c.root.slotProps;return C.jsxs(a.Fragment,{children:[C.jsxs(h.slots.baseSelect,f({fullWidth:!0,labelId:m,id:S,label:D,value:u===void 0?"":String(u),onChange:I,native:p,slotProps:{htmlInput:f({ref:r,tabIndex:i},H==null?void 0:H.htmlInput)}},y,d,c==null?void 0:c.root,{children:[C.jsx(h.slots.baseSelectOption,f({},v,{native:p,value:"",children:o.current.getLocaleText("filterValueAny")})),C.jsx(h.slots.baseSelectOption,f({},v,{native:p,value:"true",children:o.current.getLocaleText("filterValueTrue")})),C.jsx(h.slots.baseSelectOption,f({},v,{native:p,value:"false",children:o.current.getLocaleText("filterValueFalse")}))]})),l,s]})}function ao(e){if(String(e).toLowerCase()==="true")return!0;if(String(e).toLowerCase()==="false")return!1}const cg=()=>[{value:"is",getApplyFilterFn:e=>{const t=ao(e.value);return t===void 0?null:n=>!!n===t},InputComponent:ig}],ag=(e,t,n,o)=>e?o.current.getLocaleText("booleanCellTrueLabel"):o.current.getLocaleText("booleanCellFalseLabel"),ug=e=>{switch(e.toLowerCase().trim()){case"true":case"yes":case"1":return!0;case"false":case"no":case"0":case"null":case"undefined":return!1;default:return}},oi=f({},Mt,{type:"boolean",display:"flex",align:"center",headerAlign:"center",renderCell:tg,renderEditCell:lg,sortComparator:As,valueFormatter:ag,filterOperators:cg(),getApplyQuickFilterFn:()=>null,aggregable:!1,pastedValueParser:e=>ug(e)});function Do(e){for(const t in e)return!1;return!0}const ri=je(e=>e.sorting),Wn=oe(ri,e=>e.sortedRows),Er=Te(Wn,Ct,tt,(e,t,n)=>e.reduce((o,r)=>{const l=t[r];if(l)o.push({id:r,model:l});else{const s=n[r];s&&bn(s)&&o.push({id:r,model:{[Cn]:r}})}return o},[])),ht=oe(ri,e=>e.sortModel),dg=Te(ht,e=>e.reduce((n,o,r)=>(n[o.field]={sortDirection:o.sort,sortIndex:e.length>1?r+1:void 0},n),{}));Te(Wn,e=>e.reduce((t,n,o)=>(t[n]=o,t),Object.create(null)));const Ho=je(e=>e.filter),Qe=oe(Ho,e=>e.filterModel),li=oe(Qe,e=>e.quickFilterValues),gg=je(e=>e.visibleRowsLookup),Pn=oe(Ho,e=>e.filteredRowsLookup);oe(Ho,e=>e.filteredChildrenCountLookup);oe(Ho,e=>e.filteredDescendantCountLookup);const Xt=Te(gg,Er,(e,t)=>Do(e)?t:t.filter(n=>e[n.id]!==!1)),mn=Te(Xt,e=>e.map(t=>t.id)),si=Te(Pn,Er,(e,t)=>Do(e)?t:t.filter(n=>e[n.id]!==!1)),ii=Te(si,e=>e.map(t=>t.id));Te(mn,tt,(e,t)=>{const n={};let o=0;return e.reduce((r,l)=>{const s=t[l];return n[s.depth]||(n[s.depth]=0),s.depth>o&&(n[s.depth]=0),o=s.depth,n[s.depth]+=1,r[l]=n[s.depth],r},{})});const ci=Te(Xt,tt,yn,(e,t,n)=>n<2?e:e.filter(o=>{var r;return((r=t[o.id])==null?void 0:r.depth)===0})),kr=oe(Xt,e=>e.length),Tr=oe(ci,e=>e.length),ai=oe(si,e=>e.length);oe(ai,Tr,(e,t)=>e-t);const Dr=Te(Qe,At,(e,t)=>{var n;return(n=e.items)==null?void 0:n.filter(o=>{var s,i;if(!o.field)return!1;const r=t[o.field];if(!(r!=null&&r.filterOperators)||((s=r==null?void 0:r.filterOperators)==null?void 0:s.length)===0)return!1;const l=r.filterOperators.find(c=>c.value===o.operator);return l?!l.InputComponent||o.value!=null&&((i=o.value)==null?void 0:i.toString())!=="":!1})}),fg=Te(Dr,e=>e.reduce((n,o)=>(n[o.field]?n[o.field].push(o):n[o.field]=[o],n),{})),xt=je(e=>e.rowSelection),Un=Te(xt,_t),Oo=oe(xt,ai,(e,t)=>e.type==="include"?e.ids.size:t-e.ids.size),ui=Te(xt,Ct,Yt,(e,t,n)=>{const o=new Map;if(e.type==="include")for(const r of e.ids)o.set(r,t[r]);else for(let r=0;r<n.length;r+=1){const l=n[r];e.ids.has(l)||o.set(l,t[l])}return o});function di(e,t){var c;const n=tt(e),o=Wn(e),r=Pn(e),l=n[t];if(!l||l.type!=="group")return[];const s=[],i=o.findIndex(d=>d===t)+1;for(let d=i;d<o.length&&((c=n[o[d]])==null?void 0:c.depth)>l.depth;d+=1){const u=o[d];r[u]!==!1&&e.current.isRowSelectable(u)&&s.push(u)}return s}const hg=oe(tt,Pn,Un,(e,t,n,{groupId:o,autoSelectParents:r})=>{const l=e[o];if(!l||l.type!=="group"||n.has(o))return{isIndeterminate:!1,isChecked:n.has(o)};let s=!1,i=!1;const c=d=>{if(t[d]===!1||s&&i)return;const u=e[d];(u==null?void 0:u.type)==="group"&&u.children.forEach(c),n.has(d)?s=!0:i=!0};return c(o),{isIndeterminate:s&&i,isChecked:r?s&&!i:!1}});function Hr(e){return e.signature===vt.DataGrid?e.checkboxSelection&&e.disableMultipleRowSelection!==!0:!e.disableMultipleRowSelection}const mg=(e,t)=>{const n=[];let o=t;for(;o!=null&&o!==et;){const r=e[o];if(!r)return n;n.push(o),o=r.parent}return n},Cg=(e,t,n)=>{const o=e[n];if(!o)return[];const r=o.parent;return r==null?[]:e[r].children.filter(s=>s!==n&&t[s]!==!1)},Tn=(e,t,n,o,r,l,s=Un(e))=>{const i=Pn(e),c=new Set([]);if(!(!o&&!r||i[n]===!1)){if(o){const d=t[n];(d==null?void 0:d.type)==="group"&&di(e,n).forEach(g=>{l(g),c.add(g)})}if(r){const d=g=>{if(!s.has(g)&&!c.has(g))return!1;const h=t[g];return h?h.type!=="group"?!0:h.children.every(d):!1},u=g=>{const h=Cg(t,i,g);if(h.length===0||h.every(d)){const m=t[g],S=m==null?void 0:m.parent;S!=null&&S!==et&&e.current.isRowSelectable(S)&&(l(S),c.add(S),u(S))}};u(n)}}},Tl=(e,t,n,o,r,l)=>{const s=Un(e);if(!(!r&&!o)&&(r&&mg(t,n).forEach(c=>{s.has(c)&&l(c)}),o)){const i=t[n];(i==null?void 0:i.type)==="group"&&di(e,n).forEach(d=>{l(d)})}},bg=["field","id","formattedValue","row","rowNode","colDef","isEditable","cellMode","hasFocus","tabIndex","api"],pg=e=>{const{classes:t}=e;return xe({root:["checkboxInput"]},ye,t)},wg=ue(function(t,n){var I,D;const{field:o,id:r,rowNode:l,tabIndex:s}=t,i=Q(t,bg),c=me(),d=Z(),u={classes:d.classes},g=pg(u),h=H=>{const k={value:H.target.checked,id:r};c.current.publishEvent("rowSelectionCheckboxChange",k,H)};a.useLayoutEffect(()=>{if(s===0){const H=c.current.getCellElement(r,o);H&&(H.tabIndex=-1)}},[c,s,r,o]);const m=a.useCallback(H=>{H.key===" "&&H.stopPropagation()},[]),S=c.current.isRowSelectable(r),{isIndeterminate:y,isChecked:p}=W(c,hg,{groupId:r,autoSelectParents:((I=d.rowSelectionPropagation)==null?void 0:I.parents)??!1});if(l.type==="footer"||l.type==="pinnedRow")return null;const v=c.current.getLocaleText(p&&!y?"checkboxSelectionUnselectRow":"checkboxSelectionSelectRow");return C.jsx(d.slots.baseCheckbox,f({tabIndex:s,checked:p&&!y,onChange:h,className:g.root,slotProps:{htmlInput:{"aria-label":v,name:"select_row"}},onKeyDown:m,indeterminate:y,disabled:!S},(D=d.slotProps)==null?void 0:D.baseCheckbox,i,{ref:n}))}),Sg=wg,Go=je(e=>e.focus),st=oe(Go,e=>e.cell),xg=oe(Go,e=>e.columnHeader);oe(Go,e=>e.columnHeaderFilter);const bo=oe(Go,e=>e.columnGroupHeader),Lo=je(e=>e.tabIndex),Or=oe(Lo,e=>e.cell),gi=oe(Lo,e=>e.columnHeader);oe(Lo,e=>e.columnHeaderFilter);const yg=oe(Lo,e=>e.columnGroupHeader),vg=100,Ig=e=>e?0:100,fi=(e,t,n)=>t>0&&e>0?Math.ceil(e/t):e===-1?n+2:0,hi=e=>({page:0,pageSize:e?0:100}),Mg=(e,t=0)=>t===0?e:Math.max(Math.min(e,t-1),0),mi=(e,t)=>{if(t===vt.DataGrid&&e>vg)throw new Error(["MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join(`
`))},Pg=-1,Kn=je(e=>e.pagination),Ci=oe(Kn,e=>e.enabled&&e.paginationMode==="client"),Je=oe(Kn,e=>e.paginationModel),gn=oe(Kn,e=>e.rowCount),On=oe(Kn,e=>e.meta),Fg=oe(Je,e=>e.page),bi=oe(Je,e=>e.pageSize),pi=oe(Je,gn,(e,t)=>fi(t,e.pageSize,e.page)),Gr=Te(Ci,Je,tt,yn,Xt,ci,(e,t,n,o,r,l)=>{var S;if(!e)return null;const s=l.length,i=Math.min(t.pageSize*t.page,s-1),c=t.pageSize===Pg?s-1:Math.min(i+t.pageSize-1,s-1);if(i===-1||c===-1)return null;if(o<2)return{firstRowIndex:i,lastRowIndex:c};const d=l[i],u=c-i+1,g=r.findIndex(y=>y.id===d.id);let h=g,m=0;for(;h<r.length&&m<=u;){const y=r[h],p=(S=n[y.id])==null?void 0:S.depth;p===void 0?h+=1:((m<u||p>0)&&(h+=1),p===0&&(m+=1))}return{firstRowIndex:g,lastRowIndex:h-1}}),Eg=Te(Xt,Gr,(e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[]),wi=Te(mn,Gr,(e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[]),qn=Te(Ci,Gr,Eg,Xt,(e,t,n,o)=>e?{rows:n,range:t,rowIdToIndexMap:n.reduce((r,l,s)=>(r.set(l.id,s),r),new Map)}:{rows:o,range:o.length===0?null:{firstRowIndex:0,lastRowIndex:o.length-1},rowIdToIndexMap:o.reduce((r,l,s)=>(r.set(l.id,s),r),new Map)}),kg=["field","colDef"],Tg=e=>{const{classes:t}=e;return xe({root:["checkboxInput"]},ye,t)},Dg=ue(function(t,n){var L;const o=Q(t,kg),[,r]=a.useState(!1),l=me(),s=Z(),i={classes:s.classes},c=Tg(i),d=W(l,gi),u=W(l,xt),g=W(l,mn),h=W(l,wi),m=a.useMemo(()=>{const x=s.isRowSelectable;if(typeof x!="function"||u.type==="exclude")return u;const w={type:"include",ids:new Set};for(const T of u.ids)s.keepNonExistentRowsSelected&&w.ids.add(T),l.current.getRow(T)&&x(l.current.getRowParams(T))&&w.ids.add(T);return w},[l,s.isRowSelectable,s.keepNonExistentRowsSelected,u]),S=a.useMemo(()=>{const x=!s.pagination||!s.checkboxSelectionVisibleOnly||s.paginationMode==="server"?g:h,w=new Set;for(let T=0;T<x.length;T+=1){const M=x[T];l.current.getRow(M)&&l.current.isRowSelectable(M)&&w.add(M)}return w},[l,s.pagination,s.paginationMode,s.checkboxSelectionVisibleOnly,h,g]),y=a.useMemo(()=>{const x=_t(m);let w=0;for(const T of S)x.has(T)&&(w+=1);return w},[m,S]),p=a.useMemo(()=>{if(m.ids.size===0)return!1;const x=_t(m);for(const w of S)if(!x.has(w))return!0;return!1},[m,S]),v=y>0,I=x=>{const w={value:x.target.checked};l.current.publishEvent("headerSelectionCheckboxChange",w)},D=d!==null&&d.field===t.field?0:-1;a.useLayoutEffect(()=>{const x=l.current.getColumnHeaderElement(t.field);D===0&&x&&(x.tabIndex=-1)},[D,l,t.field]);const H=a.useCallback(x=>{x.key===" "&&l.current.publishEvent("headerSelectionCheckboxChange",{value:!v})},[l,v]),k=a.useCallback(()=>{r(x=>!x)},[]);a.useEffect(()=>l.current.subscribeEvent("rowSelectionChange",k),[l,k]);const $=l.current.getLocaleText(v&&!p?"checkboxSelectionUnselectAllRows":"checkboxSelectionSelectAllRows");return C.jsx(s.slots.baseCheckbox,f({indeterminate:p,checked:v&&!p,onChange:I,className:c.root,slotProps:{htmlInput:{"aria-label":$,name:"select_all_rows"}},tabIndex:D,onKeyDown:H,disabled:!Hr(s)},(L=s.slotProps)==null?void 0:L.baseCheckbox,o,{ref:n}))}),Xn=je((e,t)=>Cn in t?t[Cn]:e.props.getRowId?e.props.getRowId(t):t.id),ft="__check__",Fn=f({},oi,{type:"custom",field:ft,width:50,resizable:!1,sortable:!1,filterable:!1,aggregable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,getApplyQuickFilterFn:()=>null,display:"flex",valueGetter:(e,t,n,o)=>{const r=Xn(o,t);return o.current.isRowSelected(r)},renderHeader:e=>C.jsx(Dg,f({},e)),renderCell:e=>C.jsx(Sg,f({},e))}),Hg=["item","applyValue","type","apiRef","focusElementRef","slotProps","isFilterActive","headerFilterMenu","clearButton","tabIndex","disabled"];function Dl(e,t){if(e==null)return"";const n=new Date(e);return Number.isNaN(n.getTime())?"":t==="date"?n.toISOString().substring(0,10):t==="datetime-local"?(n.setMinutes(n.getMinutes()-n.getTimezoneOffset()),n.toISOString().substring(0,19)):n.toISOString().substring(0,10)}function on(e){var k;const{item:t,applyValue:n,type:o,apiRef:r,focusElementRef:l,slotProps:s,headerFilterMenu:i,clearButton:c,tabIndex:d,disabled:u}=e,g=Q(e,Hg),h=s==null?void 0:s.root.slotProps,m=Sn(),[S,y]=a.useState(()=>Dl(t.value,o)),[p,v]=a.useState(!1),I=Ae(),D=Z(),H=a.useCallback($=>{m.clear();const L=$.target.value;y(L),v(!0),m.start(D.filterDebounceMs,()=>{const x=new Date(L);n(f({},t,{value:Number.isNaN(x.getTime())?void 0:x})),v(!1)})},[n,t,D.filterDebounceMs,m]);return a.useEffect(()=>{const $=Dl(t.value,o);y($)},[t.value,o]),C.jsxs(a.Fragment,{children:[C.jsx(D.slots.baseTextField,f({fullWidth:!0,id:I,label:r.current.getLocaleText("filterPanelInputLabel"),placeholder:r.current.getLocaleText("filterPanelInputPlaceholder"),value:S,onChange:H,type:o||"text",disabled:u,inputRef:l,slotProps:f({},h,{input:f({endAdornment:p?C.jsx(D.slots.loadIcon,{fontSize:"small",color:"action"}):null},h==null?void 0:h.input),htmlInput:f({max:o==="datetime-local"?"9999-12-31T23:59":"9999-12-31",tabIndex:d},h==null?void 0:h.htmlInput)})},(k=D.slotProps)==null?void 0:k.baseTextField,g,s==null?void 0:s.root)),i,c]})}function rn(e,t,n,o){if(!e.value)return null;const r=new Date(e.value);n?r.setSeconds(0,0):(r.setMinutes(r.getMinutes()+r.getTimezoneOffset()),r.setHours(0,0,0,0));const l=r.getTime();return s=>{if(!s)return!1;if(o)return t(s.getTime(),l);const i=new Date(s);return n?i.setSeconds(0,0):i.setHours(0,0,0,0),t(i.getTime(),l)}}const Si=e=>[{value:"is",getApplyFilterFn:t=>rn(t,(n,o)=>n===o,e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"not",getApplyFilterFn:t=>rn(t,(n,o)=>n!==o,e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"after",getApplyFilterFn:t=>rn(t,(n,o)=>n>o,e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrAfter",getApplyFilterFn:t=>rn(t,(n,o)=>n>=o,e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"before",getApplyFilterFn:t=>rn(t,(n,o)=>n<o,e,!e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrBefore",getApplyFilterFn:t=>rn(t,(n,o)=>n<=o,e),InputComponent:on,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"isEmpty",getApplyFilterFn:()=>t=>t==null,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>t=>t!=null,requiresFilterValue:!1}],Og=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","inputProps","isValidating","isProcessingProps","onValueChange","slotProps"],Gg=Ne($t)({fontSize:"inherit"}),Lg=e=>{const{classes:t}=e;return xe({root:["editInputCell"]},ye,t)};function $g(e){var H,k;const{id:t,value:n,field:o,colDef:r,hasFocus:l,onValueChange:s,slotProps:i}=e,c=Q(e,Og),d=r.type==="dateTime",u=me(),g=a.useRef(null),h=a.useMemo(()=>{let $;n==null?$=null:n instanceof Date?$=n:$=new Date((n??"").toString());let L;return $==null||Number.isNaN($.getTime())?L="":L=new Date($.getTime()-$.getTimezoneOffset()*60*1e3).toISOString().substr(0,d?16:10),{parsed:$,formatted:L}},[n,d]),[m,S]=a.useState(h),y=Z(),p={classes:y.classes},v=Lg(p),I=a.useCallback($=>{if($==="")return null;const[L,x]=$.split("T"),[w,T,M]=L.split("-"),O=new Date;if(O.setFullYear(Number(w),Number(T)-1,Number(M)),O.setHours(0,0,0,0),x){const[G,A]=x.split(":");O.setHours(Number(G),Number(A),0,0)}return O},[]),D=a.useCallback(async $=>{const L=$.target.value,x=I(L);s&&await s($,x),S({parsed:x,formatted:L}),u.current.setEditCellValue({id:t,field:o,value:x},$)},[u,o,t,s,I]);return a.useEffect(()=>{S($=>{var L,x;return h.parsed!==$.parsed&&((L=h.parsed)==null?void 0:L.getTime())!==((x=$.parsed)==null?void 0:x.getTime())?h:$})},[h]),nt(()=>{l&&g.current.focus()},[l]),C.jsx(Gg,f({as:y.slots.baseInput,inputRef:g,fullWidth:!0,className:v.root,type:d?"datetime-local":"date",value:m.formatted,onChange:D},c,i==null?void 0:i.root,{slotProps:{htmlInput:f({max:d?"9999-12-31T23:59":"9999-12-31"},(k=(H=i==null?void 0:i.root)==null?void 0:H.slotProps)==null?void 0:k.htmlInput)}}))}const xi=e=>C.jsx($g,f({},e));function yi({value:e,columnType:t,rowId:n,field:o}){if(!(e instanceof Date))throw new Error([`MUI X: \`${t}\` column type only accepts \`Date\` objects as values.`,"Use `valueGetter` to transform the value into a `Date` object.",`Row ID: ${n}, field: "${o}".`].join(`
`))}const Ag=(e,t,n,o)=>{if(!e)return"";const r=Xn(o,t);return yi({value:e,columnType:"date",rowId:r,field:n.field}),e.toLocaleDateString()},Rg=(e,t,n,o)=>{if(!e)return"";const r=Xn(o,t);return yi({value:e,columnType:"dateTime",rowId:r,field:n.field}),e.toLocaleString()},zg=f({},Mt,{type:"date",sortComparator:Rs,valueFormatter:Ag,filterOperators:Si(),renderEditCell:xi,pastedValueParser:e=>new Date(e)}),Vg=f({},Mt,{type:"dateTime",sortComparator:Rs,valueFormatter:Rg,filterOperators:Si(!0),renderEditCell:xi,pastedValueParser:e=>new Date(e)}),Bt=e=>e==null?null:Number(e),Ng=e=>e==null||Number.isNaN(e)||e===""?null:t=>Bt(t)===Bt(e),Bg=()=>[{value:"=",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>Bt(t)===e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:"!=",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>Bt(t)!==e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:">",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>t==null?!1:Bt(t)>e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:">=",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>t==null?!1:Bt(t)>=e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:"<",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>t==null?!1:Bt(t)<e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:"<=",getApplyFilterFn:e=>e.value==null||Number.isNaN(e.value)?null:t=>t==null?!1:Bt(t)<=e.value,InputComponent:mt,InputComponentProps:{type:"number"}},{value:"isEmpty",getApplyFilterFn:()=>e=>e==null,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>e=>e!=null,requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:e=>!Array.isArray(e.value)||e.value.length===0?null:t=>t!=null&&e.value.includes(Number(t)),InputComponent:Vs,InputComponentProps:{type:"number"}}],jg=f({},Mt,{type:"number",align:"right",headerAlign:"right",sortComparator:As,valueParser:e=>e===""?null:Number(e),valueFormatter:e=>bd(e)?e.toLocaleString():e||"",filterOperators:Bg(),getApplyQuickFilterFn:Ng});function pn(e){return(e==null?void 0:e.type)==="singleSelect"}function Jt(e,t){if(e)return typeof e.valueOptions=="function"?e.valueOptions(f({field:e.field},t)):e.valueOptions}function po(e,t,n){if(t===void 0)return;const o=t.find(r=>{const l=n(r);return String(l)===String(e)});return n(o)}const _g=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange","initialOpen","slotProps"];function Wg(e){return!!e.key}function Ug(e){var T,M;const t=Z(),{id:n,value:o,field:r,row:l,colDef:s,hasFocus:i,error:c,onValueChange:d,initialOpen:u=t.editMode===Tt.Cell,slotProps:g}=e,h=Q(e,_g),m=me(),S=a.useRef(null),y=a.useRef(null),[p,v]=a.useState(u),D=(((T=t.slotProps)==null?void 0:T.baseSelect)||{}).native??!1;if(nt(()=>{var O;i&&((O=y.current)==null||O.focus())},[i]),!pn(s))return null;const H=Jt(s,{id:n,row:l});if(!H)return null;const k=s.getOptionValue,$=s.getOptionLabel,L=async O=>{if(!pn(s)||!H)return;v(!1);const G=O.target,A=po(G.value,H,k);d&&await d(O,A),await m.current.setEditCellValue({id:n,field:r,value:A},O)},x=(O,G)=>{if(t.editMode===Tt.Row){v(!1);return}if(G==="backdropClick"||O.key==="Escape"){const A=m.current.getCellParams(n,r);m.current.publishEvent("cellEditStop",f({},A,{reason:O.key==="Escape"?St.escapeKeyDown:St.cellFocusOut}))}},w=O=>{Wg(O)&&O.key==="Enter"||v(!0)};return!H||!s?null:C.jsx(t.slots.baseSelect,f({ref:S,value:o,onChange:L,open:p,onOpen:w,onClose:x,error:c,native:D,fullWidth:!0,slotProps:{htmlInput:{ref:y}}},h,g==null?void 0:g.root,(M=t.slotProps)==null?void 0:M.baseSelect,{children:H.map(O=>{var A;const G=k(O);return a.createElement(t.slots.baseSelectOption,f({},((A=t.slotProps)==null?void 0:A.baseSelectOption)||{},{native:D,key:G,value:G}),$(O))})}))}const Kg=e=>C.jsx(Ug,f({},e)),qg=["item","applyValue","type","apiRef","focusElementRef","tabIndex","isFilterActive","clearButton","headerFilterMenu","slotProps"],Xg=({column:e,OptionComponent:t,getOptionLabel:n,getOptionValue:o,isSelectNative:r,baseSelectOptionProps:l})=>["",...Jt(e)||[]].map(i=>{const c=o(i);let d=n(i);return d===""&&(d=" "),a.createElement(t,f({},l,{native:r,key:c,value:c}),d)});function Hl(e){var $,L,x,w,T;const{item:t,applyValue:n,type:o,apiRef:r,focusElementRef:l,tabIndex:s,clearButton:i,headerFilterMenu:c,slotProps:d}=e,u=Q(e,qg),g=t.value??"",h=Ae(),m=Ae(),S=Z(),y=((L=($=S.slotProps)==null?void 0:$.baseSelect)==null?void 0:L.native)??!1;let p=null;if(t.field){const M=r.current.getColumn(t.field);pn(M)&&(p=M)}const v=p==null?void 0:p.getOptionValue,I=p==null?void 0:p.getOptionLabel,D=a.useMemo(()=>Jt(p),[p]),H=a.useCallback(M=>{let O=M.target.value;O=po(O,D,v),n(f({},t,{value:O}))},[D,v,n,t]);if(!pn(p))return null;const k=(d==null?void 0:d.root.label)??r.current.getLocaleText("filterPanelInputLabel");return C.jsxs(a.Fragment,{children:[C.jsx(S.slots.baseSelect,f({fullWidth:!0,id:h,label:k,labelId:m,value:g,onChange:H,slotProps:{htmlInput:f({tabIndex:s,ref:l,type:o||"text",placeholder:(d==null?void 0:d.root.placeholder)??r.current.getLocaleText("filterPanelInputPlaceholder")},(x=d==null?void 0:d.root.slotProps)==null?void 0:x.htmlInput)},native:y},(w=S.slotProps)==null?void 0:w.baseSelect,u,d==null?void 0:d.root,{children:Xg({column:p,OptionComponent:S.slots.baseSelectOption,getOptionLabel:I,getOptionValue:v,isSelectNative:y,baseSelectOptionProps:(T=S.slotProps)==null?void 0:T.baseSelectOption})})),c,i]})}const Qg=["item","applyValue","type","apiRef","focusElementRef","slotProps"];function Yg(e){const{item:t,applyValue:n,type:o,apiRef:r,focusElementRef:l,slotProps:s}=e,i=Q(e,Qg),c=Ae(),d=Z();let u=null;if(t.field){const I=r.current.getColumn(t.field);pn(I)&&(u=I)}const g=u==null?void 0:u.getOptionValue,h=u==null?void 0:u.getOptionLabel,m=a.useCallback((I,D)=>g(I)===g(D),[g]),S=a.useMemo(()=>Jt(u)||[],[u]),y=a.useMemo(()=>Array.isArray(t.value)?t.value.reduce((I,D)=>{const H=S.find(k=>g(k)===D);return H!=null&&I.push(H),I},[]):[],[g,t.value,S]),p=a.useCallback((I,D)=>{n(f({},t,{value:D.map(g)}))},[n,t,g]),v=d.slots.baseAutocomplete;return C.jsx(v,f({multiple:!0,options:S,isOptionEqualToValue:m,id:c,value:y,onChange:p,getOptionLabel:h,label:r.current.getLocaleText("filterPanelInputLabel"),placeholder:r.current.getLocaleText("filterPanelInputPlaceholder"),slotProps:{textField:{type:o||"text",inputRef:l}}},i,s==null?void 0:s.root))}const ln=e=>e==null||!Ir(e)?e:e.value,Zg=()=>[{value:"is",getApplyFilterFn:e=>e.value==null||e.value===""?null:t=>ln(t)===ln(e.value),InputComponent:Hl},{value:"not",getApplyFilterFn:e=>e.value==null||e.value===""?null:t=>ln(t)!==ln(e.value),InputComponent:Hl},{value:"isAnyOf",getApplyFilterFn:e=>{if(!Array.isArray(e.value)||e.value.length===0)return null;const t=e.value.map(ln);return n=>t.includes(ln(n))},InputComponent:Yg}],Jg=e=>typeof e[0]=="object",ef=e=>Ir(e)?e.value:e,tf=e=>Ir(e)?e.label:String(e),nf=f({},Mt,{type:"singleSelect",getOptionLabel:tf,getOptionValue:ef,valueFormatter(e,t,n,o){const r=Xn(o,t);if(!pn(n))return"";const l=Jt(n,{id:r,row:t});if(e==null)return"";if(!l)return e;if(!Jg(l))return n.getOptionLabel(e);const s=l.find(i=>n.getOptionValue(i)===e);return s?n.getOptionLabel(s):""},renderEditCell:Kg,filterOperators:Zg(),pastedValueParser:(e,t,n)=>{const o=n,r=Jt(o)||[],l=o.getOptionValue;if(r.find(i=>l(i)===e))return e}}),of="string",rf=()=>({string:Mt,number:jg,date:zg,dateTime:Vg,boolean:oi,singleSelect:nf,[Eo]:Ud,custom:Mt}),Lr=je(e=>e.headerFiltering),lf=oe(Lr,e=>(e==null?void 0:e.enabled)??!1),sf=oe(Lr,e=>e.editing),cf=oe(Lr,e=>e.menuOpen),$o=je(e=>e.columnGrouping),vi=Te($o,e=>(e==null?void 0:e.unwrappedGroupingModel)??{}),Ii=Te($o,e=>(e==null?void 0:e.lookup)??{}),af=Te($o,e=>(e==null?void 0:e.headerStructure)??[]),Qn=oe($o,e=>(e==null?void 0:e.maxDepth)??0),Mi=["maxWidth","minWidth","width","flex"],Xo=rf();function uf({initialFreeSpace:e,totalFlexUnits:t,flexColumns:n}){const o=new Set(n.map(s=>s.field)),r={all:{},frozenFields:[],freeze:s=>{const i=r.all[s];i&&i.frozen!==!0&&(r.all[s].frozen=!0,r.frozenFields.push(s))}};function l(){if(r.frozenFields.length===o.size)return;const s={min:{},max:{}};let i=e,c=t,d=0;r.frozenFields.forEach(u=>{i-=r.all[u].computedWidth,c-=r.all[u].flex});for(let u=0;u<n.length;u+=1){const g=n[u];if(r.all[g.field]&&r.all[g.field].frozen===!0)continue;let m=i/c*g.flex;m<g.minWidth?(d+=g.minWidth-m,m=g.minWidth,s.min[g.field]=!0):m>g.maxWidth&&(d+=g.maxWidth-m,m=g.maxWidth,s.max[g.field]=!0),r.all[g.field]={frozen:!1,computedWidth:m,flex:g.flex}}d<0?Object.keys(s.max).forEach(u=>{r.freeze(u)}):d>0?Object.keys(s.min).forEach(u=>{r.freeze(u)}):n.forEach(({field:u})=>{r.freeze(u)}),l()}return l(),r.all}const ir=(e,t)=>{const n={};let o=0,r=0;const l=[];e.orderedFields.forEach(c=>{let d=e.lookup[c],u=0,g=!1;e.columnVisibilityModel[c]!==!1&&(d.flex&&d.flex>0?(o+=d.flex,g=!0):u=it(d.width||Mt.width,d.minWidth||Mt.minWidth,d.maxWidth||Mt.maxWidth),r+=u),d.computedWidth!==u&&(d=f({},d,{computedWidth:u})),g&&l.push(d),n[c]=d});const s=t===void 0?0:t.viewportOuterSize.width-(t.hasScrollY?t.scrollbarSize:0),i=Math.max(s-r,0);if(o>0&&s>0){const c=uf({initialFreeSpace:i,totalFlexUnits:o,flexColumns:l});Object.keys(c).forEach(d=>{n[d]=f({},n[d],{computedWidth:c[d].computedWidth})})}return f({},e,{lookup:n})},df=(e,t)=>{if(!t)return e;const{orderedFields:n=[],dimensions:o={}}=t,r=Object.keys(o);if(r.length===0&&n.length===0)return e;const l={},s=[];for(let u=0;u<n.length;u+=1){const g=n[u];e.lookup[g]&&(l[g]=!0,s.push(g))}const i=s.length===0?e.orderedFields:[...s,...e.orderedFields.filter(u=>!l[u])],c=f({},e.lookup);for(let u=0;u<r.length;u+=1){const g=r[u],h=f({},c[g],{hasBeenResized:!0});Object.entries(o[g]).forEach(([m,S])=>{h[m]=S===-1?1/0:S}),c[g]=h}return f({},e,{orderedFields:i,lookup:c})};function Qo(e){let t=Xo[of];return e&&Xo[e]&&(t=Xo[e]),t}const an=({apiRef:e,columnsToUpsert:t,initialState:n,columnVisibilityModel:o=yt(e),keepOnlyColumnsToUpsert:r=!1,updateInitialVisibilityModel:l=!1})=>{var g,h;const s=!e.current.state.columns;let i;if(s)i={orderedFields:[],lookup:{},columnVisibilityModel:o,initialColumnVisibilityModel:o};else{const m=It(e);i={orderedFields:r?[]:[...m.orderedFields],lookup:f({},m.lookup),columnVisibilityModel:o,initialColumnVisibilityModel:l?o:m.initialColumnVisibilityModel}}let c={};r&&!s&&(c=Object.keys(i.lookup).reduce((m,S)=>f({},m,{[S]:!1}),{})),t.forEach(m=>{const{field:S}=m;c[S]=!0;let y=i.lookup[S];y==null?(y=f({},Qo(m.type),{field:S,hasBeenResized:!1}),i.orderedFields.push(S)):r&&i.orderedFields.push(S),y&&y.type!==m.type&&(y=f({},Qo(m.type),{field:S}));let p=y.hasBeenResized;Mi.forEach(v=>{m[v]!==void 0&&(p=!0,m[v]===-1&&(m[v]=1/0))}),i.lookup[S]=zc(y,f({},Qo(m.type),m,{hasBeenResized:p}))}),r&&!s&&Object.keys(i.lookup).forEach(m=>{c[m]||delete i.lookup[m]});const d=e.current.unstable_applyPipeProcessors("hydrateColumns",i),u=df(d,n);return ir(u,((h=(g=e.current).getRootDimensions)==null?void 0:h.call(g))??void 0)};function gf({firstColumnToRender:e,apiRef:t,firstRowToRender:n,lastRowToRender:o,visibleRows:r}){let l=e,s=!1;for(;!s&&l>=0;){s=!0;for(let i=n;i<o;i+=1)if(r[i]){const d=r[i].id,u=t.current.unstable_getCellColSpanInfo(d,l);if(u&&u.spannedByColSpan&&u.leftVisibleCellIndex<l){l=u.leftVisibleCellIndex,s=!1;break}}}return l}function $r(e,t){if(t.listView)return 0;const n=xn(e),o=Qn(e),r=lf(e),l=Math.floor(t.columnHeaderHeight*n),s=Math.floor((t.columnGroupHeaderHeight??t.columnHeaderHeight)*n),i=r?Math.floor((t.headerFilterHeight??t.columnHeaderHeight)*n):0;return l+s*o+i}const Ol=1,ff=1.5,hf=e=>{const{scrollDirection:t,classes:n}=e,o={root:["scrollArea",`scrollArea--${t}`]};return xe(o,ye,n)},mf=ke("div",{name:"MuiDataGrid",slot:"ScrollArea",overridesResolver:(e,t)=>[{[`&.${b["scrollArea--left"]}`]:t["scrollArea--left"]},{[`&.${b["scrollArea--right"]}`]:t["scrollArea--right"]},t.scrollArea]})(()=>({position:"absolute",top:0,zIndex:101,width:20,bottom:0,[`&.${b["scrollArea--left"]}`]:{left:0},[`&.${b["scrollArea--right"]}`]:{right:0}})),Cf=oe(Re,(e,t)=>t==="left"?e.leftPinnedWidth:t==="right"?e.rightPinnedWidth+(e.hasScrollX?e.scrollbarSize:0):0);function bf(e){const t=me(),[n,o]=a.useState(!1);return le(t,"columnHeaderDragStart",()=>o(!0)),le(t,"columnHeaderDragEnd",()=>o(!1)),n?C.jsx(pf,f({},e)):null}function pf(e){const{scrollDirection:t,scrollPosition:n}=e,o=a.useRef(null),r=me(),l=Sn(),s=W(r,xn),i=W(r,Mo),c=W(r,Cf,t),d=()=>{const H=Re(r);if(t==="left")return n.current.left>0;if(t==="right"){const k=i-H.viewportInnerSize.width;return n.current.left<k}return!1},[u,g]=a.useState(d),h=Z(),m=f({},h,{scrollDirection:t}),S=hf(m),y=$r(r,h),p=Math.floor(h.columnHeaderHeight*s),v={height:p,top:y-p};t==="left"?v.left=c:t==="right"&&(v.right=c);const I=()=>{g(d)},D=$e(H=>{let k;if(H.preventDefault(),t==="left")k=H.clientX-o.current.getBoundingClientRect().right;else if(t==="right")k=Math.max(1,H.clientX-o.current.getBoundingClientRect().left);else throw new Error("MUI X: Wrong drag direction");k=(k-Ol)*ff+Ol,l.start(0,()=>{r.current.scroll({left:n.current.left+k,top:n.current.top})})});return le(r,"scrollPositionChange",I),u?C.jsx(mf,{ref:o,className:S.root,ownerState:m,onDragOver:D,style:v}):null}const Gl=Lt(bf),Pi=a.createContext(void 0);function ut(){const e=a.useContext(Pi);if(e===void 0)throw new Error(["MUI X: Could not find the Data Grid private context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join(`
`));return e}const wf=()=>{},Sf=(e,t)=>{const n=a.useRef(!1);nt(()=>n.current||!e?wf:(n.current=!0,t()),[n.current||e])};function we(e,t,n){const o=a.useRef(!0);nt(()=>{o.current=!1,e.current.register(n,t)},[e,n,t]),o.current&&e.current.register(n,t)}function Ze(e,t){const n=a.useRef(null);if(n.current)return n.current;const o=e.current.getLogger(t);return n.current=o,o}const Fi=(e,t,n,o,r)=>{const l=Ze(e,"useNativeEventListener");Le(e,"rootMount",()=>{const s=t();if(!(!s||!n))return l.debug(`Binding native ${n} event`),s.addEventListener(n,o,r),()=>{l.debug(`Clearing native ${n} event`),s.removeEventListener(n,o,r)}})},Yn=e=>{const t=a.useRef(!0);t.current&&(t.current=!1,e())},Ut=(e,t)=>qn(e),Ar=(e,t)=>W(e,qn),xf=typeof navigator<"u"?navigator.userAgent.toLowerCase():"empty",yf=xf.includes("firefox"),Zn=je(e=>e.rowsMeta),Ao=je(e=>e.virtualization);oe(Ao,e=>e.enabled);const Ei=oe(Ao,e=>e.enabledForColumns),vf=oe(Ao,e=>e.enabledForRows),Jn=oe(Ao,e=>e.renderContext),If=je(e=>e.virtualization.renderContext.firstColumnIndex),Mf=je(e=>e.virtualization.renderContext.lastColumnIndex),Pf=Te(If,Mf,(e,t)=>({firstColumnIndex:e,lastColumnIndex:t})),cr={firstRowIndex:0,lastRowIndex:0,firstColumnIndex:0,lastColumnIndex:0},Ff=(e,t)=>{const{disableVirtualization:n,autoHeight:o}=t;return f({},e,{virtualization:{enabled:!n,enabledForColumns:!n,enabledForRows:!n&&!o,renderContext:cr}})};function Ef(e,t){const n=l=>{e.current.setState(s=>f({},s,{virtualization:f({},s.virtualization,{enabled:l,enabledForColumns:l,enabledForRows:l&&!t.autoHeight})}))};we(e,{unstable_setVirtualization:n,unstable_setColumnVirtualization:l=>{e.current.setState(s=>f({},s,{virtualization:f({},s.virtualization,{enabledForColumns:l})}))}},"public"),a.useEffect(()=>{n(!t.disableVirtualization)},[t.disableVirtualization,t.autoHeight])}const Rr=je(e=>e.rowSpanning),ki=oe(Rr,e=>e.hiddenCells),kf=oe(Rr,e=>e.spannedCells),Tf=oe(Rr,e=>e.hiddenCellOriginMap),wn=je(e=>e.listViewColumn),Df=oe(st,Jn,qn,We,(e,t,n,o)=>{if(!e)return!1;const r=n.rowIdToIndexMap.get(e.id),l=o.slice(t.firstColumnIndex,t.lastColumnIndex).findIndex(i=>i.field===e.field);return!(r!==void 0&&l!==-1&&r>=t.firstRowIndex&&r<=t.lastRowIndex)}),Ti=Te(Df,We,qn,st,(e,t,n,o)=>{if(!e)return null;const r=n.rowIdToIndexMap.get(o.id);if(r===void 0)return null;const l=t.findIndex(s=>s.field===o.field);return l===-1?null:f({},o,{rowIndex:r,columnIndex:l})});function fn(e,t){return Math.round(e*10**t)/10**t}const wo=typeof window<"u"&&/jsdom|HappyDOM/.test(window.navigator.userAgent),Yo=50;var qe=function(e){return e[e.NONE=0]="NONE",e[e.UP=1]="UP",e[e.DOWN=2]="DOWN",e[e.LEFT=3]="LEFT",e[e.RIGHT=4]="RIGHT",e}(qe||{});const Ll={top:0,left:0},Hf=Object.freeze(new Map),Of=(e,t,n,o,r)=>({direction:qe.NONE,buffer:Hi(e,qe.NONE,t,n,o,r)}),Gf=()=>{var ne;const e=ut(),t=Z(),{listView:n}=t,o=W(e,()=>n?[wn(e)]:We(e)),r=W(e,vf)&&!wo,l=W(e,Ei)&&!wo,s=W(e,vn),i=Mn(e),c=n?Co:i,d=s.bottom.length>0,[u,g]=a.useState(Hf),h=Dt(),m=W(e,Un),S=Ar(e),y=e.current.mainElementRef,p=e.current.virtualScrollerRef,v=e.current.virtualScrollbarVerticalRef,I=e.current.virtualScrollbarHorizontalRef,D=W(e,Pd),H=a.useRef(!1),k=W(e,xr),$=W(e,Hu),L=W(e,Mo),x=W(e,Lf),w=W(e,Hs),T=W(e,Ds),M=a.useRef(null),O=a.useCallback(U=>{if(y.current=U,!U)return;const J=U.getBoundingClientRect();let ie={width:fn(J.width,1),height:fn(J.height,1)};if((!M.current||ie.width!==M.current.width&&ie.height!==M.current.height)&&(M.current=ie,e.current.publishEvent("resize",ie)),typeof ResizeObserver>"u")return;const fe=new ResizeObserver(he=>{const ge=he[0];if(!ge)return;const be={width:fn(ge.contentRect.width,1),height:fn(ge.contentRect.height,1)};be.width===ie.width&&be.height===ie.height||(e.current.publishEvent("resize",be),ie=be)});if(fe.observe(U),vs>=19)return()=>{y.current=null,fe.disconnect()}},[e,y]),G=a.useRef(((ne=t.initialState)==null?void 0:ne.scroll)??Ll),A=a.useRef(!1),F=a.useRef(Ll),E=a.useRef(cr),P=W(e,Jn),z=W(e,Ti),_=Sn(),V=a.useRef(void 0),R=Gt(()=>Of(h,t.rowBufferPx,t.columnBufferPx,k*15,Yo*6)).current,B=a.useCallback(U=>{if(zl(U,e.current.state.virtualization.renderContext))return;const J=U.firstRowIndex!==E.current.firstRowIndex||U.lastRowIndex!==E.current.lastRowIndex;e.current.setState(fe=>f({},fe,{virtualization:f({},fe.virtualization,{renderContext:U})})),Re(e).isReady&&J&&(E.current=U,e.current.publishEvent("renderedRowsIntervalChange",U)),F.current=G.current},[e]),q=$e(()=>{const U=p.current;if(!U)return;const J=Re(e),ie=Math.ceil(J.minimumSize.height-J.viewportOuterSize.height),fe=Math.ceil(J.minimumSize.width-J.viewportInnerSize.width),he={top:it(U.scrollTop,0,ie),left:h?it(U.scrollLeft,-fe,0):it(U.scrollLeft,0,fe)},ge=he.left-G.current.left,be=he.top-G.current.top,Me=ge!==0||be!==0;G.current=he;const De=Me?Rf(ge,be):qe.NONE,se=Math.abs(G.current.top-F.current.top),Ce=Math.abs(G.current.left-F.current.left),Se=se>=k||Ce>=Yo,Pe=R.direction!==De;if(!(Se||Pe))return P;if(Pe)switch(De){case qe.NONE:case qe.LEFT:case qe.RIGHT:V.current=void 0;break;default:V.current=P;break}R.direction=De,R.buffer=Hi(h,De,t.rowBufferPx,t.columnBufferPx,k*15,Yo*6);const ze=$l(e,t,r,l),Ie=Al(ze,G.current,R);return zl(Ie,P)||(Vc.flushSync(()=>{B(Ie)}),_.start(1e3,q)),Ie}),X=()=>{if(!Re(e).isReady&&(r||l))return;const U=$l(e,t,r,l),J=Al(U,G.current,R);V.current=void 0,B(J)},re=$e(()=>{if(A.current){A.current=!1;return}const U=q();e.current.publishEvent("scrollPositionChange",{top:G.current.top,left:G.current.left,renderContext:U})}),Y=$e(U=>{e.current.publishEvent("virtualScrollerWheel",{},U)}),ae=$e(U=>{e.current.publishEvent("virtualScrollerTouchMove",{},U)}),ce=(U={})=>{var ze;if(!U.rows&&!S.range)return[];const J=tt(e);let ie=P;U.renderContext&&(ie=U.renderContext,ie.firstColumnIndex=P.firstColumnIndex,ie.lastColumnIndex=P.lastColumnIndex);const fe=!d&&U.position===void 0||d&&U.position==="bottom",he=U.position!==void 0;let ge;switch(U.position){case"top":ge=0;break;case"bottom":ge=s.top.length+S.rows.length;break;case void 0:ge=s.top.length;break}const be=U.rows??S.rows,Me=ie.firstRowIndex,De=Math.min(ie.lastRowIndex,be.length),se=U.rows?xl(0,U.rows.length):xl(Me,De);let Ce=-1;!he&&z&&(z.rowIndex<Me&&(se.unshift(z.rowIndex),Ce=z.rowIndex),z.rowIndex>De&&(se.push(z.rowIndex),Ce=z.rowIndex));const Se=[],Pe=(ze=t.slotProps)==null?void 0:ze.row,Ge=qt(e);return se.forEach(Ie=>{var Ur,Kr,qr;const{id:de,model:He}=be[Ie];if(!J[de])return;const Ee=(((Ur=S==null?void 0:S.range)==null?void 0:Ur.firstRowIndex)||0)+ge+Ie;if(D){const Wo=c.left.length,Xr=o.length-c.right.length;e.current.calculateColSpan({rowId:de,minFirstColumn:Wo,maxLastColumn:Xr,columns:o}),c.left.length>0&&e.current.calculateColSpan({rowId:de,minFirstColumn:0,maxLastColumn:c.left.length,columns:o}),c.right.length>0&&e.current.calculateColSpan({rowId:de,minFirstColumn:o.length-c.right.length,maxLastColumn:o.length,columns:o})}const ot=e.current.rowHasAutoHeight(de)?"auto":e.current.unstable_getRowHeight(de),dt=m.has(de)&&e.current.isRowSelectable(de);let pe=!1;U.position===void 0&&(pe=Ie===0);let Ue=!1;const Ke=Ie===be.length-1;if(fe)if(he)Ue=Ke;else{const Wo=S.rows.length-1;Ie===Wo&&(Ue=!0)}let gt=ie;V.current&&Ie>=V.current.firstRowIndex&&Ie<V.current.lastRowIndex&&(gt=V.current);const to=Ie===Ce,Vo=(z==null?void 0:z.rowIndex)===Ee,No=Di(Ge,gt,c.left.length),Bo=Ke&&U.position==="top",jo=gt.firstColumnIndex,_o=gt.lastColumnIndex;if(Se.push(C.jsx(t.slots.row,f({row:He,rowId:de,index:Ee,selected:dt,offsetLeft:No,columnsTotalWidth:L,rowHeight:ot,pinnedColumns:c,visibleColumns:o,firstColumnIndex:jo,lastColumnIndex:_o,focusedColumnIndex:Vo?z.columnIndex:void 0,isFirstVisible:pe,isLastVisible:Ue,isNotVisible:to,showBottomBorder:Bo,scrollbarWidth:w,gridHasFiller:T},Pe),de)),to)return;const no=u.get(de);no&&Se.push(no),U.position===void 0&&Ke&&Se.push((qr=(Kr=e.current).getInfiniteLoadingTriggerElement)==null?void 0:qr.call(Kr,{lastRowId:de}))}),Se},j=a.useMemo(()=>({overflowX:!x||n?"hidden":void 0,overflowY:t.autoHeight?"hidden":void 0}),[x,t.autoHeight,n]),K=a.useMemo(()=>{const U={width:x?L:"auto",flexBasis:$,flexShrink:0};return U.flexBasis===0&&(U.flexBasis=ei),U},[L,$,x]),te=a.useCallback(U=>{U&&e.current.publishEvent("virtualScrollerContentSizeChange",{columnsTotalWidth:L,contentHeight:$})},[e,L,$]);return nt(()=>{var U,J;H.current&&((J=(U=e.current).updateRenderContext)==null||J.call(U))},[e,l,r]),nt(()=>{n&&(p.current.scrollLeft=0)},[n,p]),Sf(P!==cr,()=>{var U;if(e.current.publishEvent("scrollPositionChange",{top:G.current.top,left:G.current.left,renderContext:P}),H.current=!0,(U=t.initialState)!=null&&U.scroll&&p.current){const J=p.current,{top:ie,left:fe}=t.initialState.scroll,he={top:!(ie>0),left:!(fe>0)};if(!he.left&&L&&(J.scrollLeft=fe,A.current=!0,he.left=!0),!he.top&&$&&(J.scrollTop=ie,A.current=!0,he.top=!0),!he.top||!he.left){const ge=e.current.subscribeEvent("virtualScrollerContentSizeChange",be=>{!he.left&&be.columnsTotalWidth&&(J.scrollLeft=fe,A.current=!0,he.left=!0),!he.top&&be.contentHeight&&(J.scrollTop=ie,A.current=!0,he.top=!0),he.left&&he.top&&ge()});return ge}}}),e.current.register("private",{updateRenderContext:X}),Le(e,"sortedRowsSet",X),Le(e,"paginationModelChange",X),Le(e,"columnsChange",X),{renderContext:P,setPanels:g,getRows:ce,getContainerProps:()=>({ref:O}),getScrollerProps:()=>({ref:p,onScroll:re,onWheel:Y,onTouchMove:ae,style:j,role:"presentation",tabIndex:yf?-1:void 0}),getContentProps:()=>({style:K,role:"presentation",ref:te}),getRenderZoneProps:()=>({role:"rowgroup"}),getScrollbarVerticalProps:()=>({ref:v,scrollPosition:G}),getScrollbarHorizontalProps:()=>({ref:I,scrollPosition:G}),getScrollAreaProps:()=>({scrollPosition:G})}};function Lf(e){return e.current.state.dimensions.viewportOuterSize.width>0&&e.current.state.dimensions.columnsTotalWidth>e.current.state.dimensions.viewportOuterSize.width}function $l(e,t,n,o){const r=Re(e),l=Ut(e),s=t.listView?[wn(e)]:We(e),i=Tf(e),c=e.current.state.rows.dataRowIds.at(-1),d=s.at(-1);return{enabledForRows:n,enabledForColumns:o,apiRef:e,autoHeight:t.autoHeight,rowBufferPx:t.rowBufferPx,columnBufferPx:t.columnBufferPx,leftPinnedWidth:r.leftPinnedWidth,columnsTotalWidth:r.columnsTotalWidth,viewportInnerWidth:r.viewportInnerSize.width,viewportInnerHeight:r.viewportInnerSize.height,lastRowHeight:c!==void 0?e.current.unstable_getRowHeight(c):0,lastColumnWidth:(d==null?void 0:d.computedWidth)??0,rowsMeta:Zn(e),columnPositions:qt(e),rows:l.rows,range:l.range,pinnedColumns:Mn(e),visibleColumns:s,hiddenCellsOriginMap:i,listView:t.listView??!1,virtualizeColumnsWithAutoRowHeight:t.virtualizeColumnsWithAutoRowHeight}}function Al(e,t,n){const o={firstRowIndex:0,lastRowIndex:e.rows.length,firstColumnIndex:0,lastColumnIndex:e.visibleColumns.length},{top:r,left:l}=t,s=Math.abs(l)+e.leftPinnedWidth;if(e.enabledForRows){let c=Math.min(Rl(e,r,{atStart:!0,lastPosition:e.rowsMeta.positions[e.rowsMeta.positions.length-1]+e.lastRowHeight}),e.rowsMeta.positions.length-1);const d=e.hiddenCellsOriginMap[c];if(d){const g=Math.min(...Object.values(d));c=Math.min(c,g)}const u=e.autoHeight?c+e.rows.length:Rl(e,r+e.viewportInnerHeight);o.firstRowIndex=c,o.lastRowIndex=u}if(e.listView)return f({},o,{lastColumnIndex:1});if(e.enabledForColumns){let c=0,d=e.columnPositions.length,u=!1;const[g,h]=ar({firstIndex:o.firstRowIndex,lastIndex:o.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:n.buffer.rowBefore,bufferAfter:n.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight});if(!e.virtualizeColumnsWithAutoRowHeight)for(let m=g;m<h&&!u;m+=1){const S=e.rows[m];u=e.apiRef.current.rowHasAutoHeight(S.id)}(!u||e.virtualizeColumnsWithAutoRowHeight)&&(c=Kt(s,e.columnPositions,{atStart:!0,lastPosition:e.columnsTotalWidth}),d=Kt(s+e.viewportInnerWidth,e.columnPositions)),o.firstColumnIndex=c,o.lastColumnIndex=d}return $f(e,o,n)}function Rl(e,t,n){var s,i;const o=e.apiRef.current.getLastMeasuredRowIndex();let r=o===1/0;(s=e.range)!=null&&s.lastRowIndex&&!r&&(r=o>=e.range.lastRowIndex);const l=it(o-(((i=e.range)==null?void 0:i.firstRowIndex)||0),0,e.rowsMeta.positions.length);return r||e.rowsMeta.positions[l]>=t?Kt(t,e.rowsMeta.positions,n):Af(t,e.rowsMeta.positions,l,n)}function $f(e,t,n){const[o,r]=ar({firstIndex:t.firstRowIndex,lastIndex:t.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:n.buffer.rowBefore,bufferAfter:n.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight}),[l,s]=ar({firstIndex:t.firstColumnIndex,lastIndex:t.lastColumnIndex,minFirstIndex:e.pinnedColumns.left.length,maxLastIndex:e.visibleColumns.length-e.pinnedColumns.right.length,bufferBefore:n.buffer.columnBefore,bufferAfter:n.buffer.columnAfter,positions:e.columnPositions,lastSize:e.lastColumnWidth}),i=gf({firstColumnToRender:l,apiRef:e.apiRef,firstRowToRender:o,lastRowToRender:r,visibleRows:e.rows});return{firstRowIndex:o,lastRowIndex:r,firstColumnIndex:i,lastColumnIndex:s}}function Kt(e,t,n=void 0,o=0,r=t.length){if(t.length<=0)return-1;if(o>=r)return o;const l=o+Math.floor((r-o)/2),s=t[l];let i;if(n!=null&&n.atStart){const c=(l===t.length-1?n.lastPosition:t[l+1])-s;i=e-c<s}else i=e<=s;return i?Kt(e,t,n,o,l):Kt(e,t,n,l+1,r)}function Af(e,t,n,o=void 0){let r=1;for(;n<t.length&&Math.abs(t[n])<e;)n+=r,r*=2;return Kt(e,t,o,Math.floor(n/2),Math.min(n,t.length))}function ar({firstIndex:e,lastIndex:t,bufferBefore:n,bufferAfter:o,minFirstIndex:r,maxLastIndex:l,positions:s,lastSize:i}){const c=s[e]-n,d=s[t]+o,u=Kt(c,s,{atStart:!0,lastPosition:s[s.length-1]+i}),g=Kt(d,s);return[it(u,r,l),it(g,r,l)]}function zl(e,t){return e===t?!0:e.firstRowIndex===t.firstRowIndex&&e.lastRowIndex===t.lastRowIndex&&e.firstColumnIndex===t.firstColumnIndex&&e.lastColumnIndex===t.lastColumnIndex}function Di(e,t,n){const o=(e[t.firstColumnIndex]??0)-(e[n]??0);return Math.abs(o)}function Rf(e,t){return e===0&&t===0?qe.NONE:Math.abs(t)>=Math.abs(e)?t>0?qe.DOWN:qe.UP:e>0?qe.RIGHT:qe.LEFT}function Hi(e,t,n,o,r,l){if(e)switch(t){case qe.LEFT:t=qe.RIGHT;break;case qe.RIGHT:t=qe.LEFT;break}switch(t){case qe.NONE:return{rowAfter:n,rowBefore:n,columnAfter:o,columnBefore:o};case qe.LEFT:return{rowAfter:0,rowBefore:0,columnAfter:0,columnBefore:l};case qe.RIGHT:return{rowAfter:0,rowBefore:0,columnAfter:l,columnBefore:0};case qe.UP:return{rowAfter:0,rowBefore:r,columnAfter:0,columnBefore:0};case qe.DOWN:return{rowAfter:r,rowBefore:0,columnAfter:0,columnBefore:0};default:throw new Error("unreachable")}}const zf=ke("div",{name:"MuiDataGrid",slot:"OverlayWrapper",shouldForwardProp:e=>e!=="overlayType"&&e!=="loadingOverlayVariant"&&e!=="right"})(({overlayType:e,loadingOverlayVariant:t,right:n})=>t!=="skeleton"?{position:"sticky",top:"var(--DataGrid-headersTotalHeight)",left:0,right:`${n}px`,width:0,height:0,zIndex:e==="loadingOverlay"?5:4}:{}),Vf=ke("div",{name:"MuiDataGrid",slot:"OverlayWrapperInner",shouldForwardProp:e=>e!=="overlayType"&&e!=="loadingOverlayVariant"})({}),Nf=e=>{const{classes:t}=e;return xe({root:["overlayWrapper"],inner:["overlayWrapperInner"]},ye,t)};function Bf(e){const t=me(),n=Z(),o=W(t,Re);let r=Math.max(o.viewportOuterSize.height-o.topContainerHeight-o.bottomContainerHeight-(o.hasScrollX?o.scrollbarSize:0),0);r===0&&(r=ei);const l=Nf(f({},e,{classes:n.classes}));return C.jsx(zf,f({className:l.root},e,{right:o.columnsTotalWidth-o.viewportOuterSize.width,children:C.jsx(Vf,f({className:l.inner,style:{height:r,width:o.viewportOuterSize.width}},e))}))}const zr=je(e=>e.pivoting),en=oe(zr,e=>e==null?void 0:e.active),jf=new Map,Oi=oe(zr,e=>(e==null?void 0:e.initialColumns)||jf);oe(zr,e=>e==null?void 0:e.panelOpen);const _f=()=>{var v,I;const e=me(),t=Z(),n=W(e,_n),o=W(e,kr),r=W(e,Ls),l=W(e,We),s=n===0&&r===0,i=W(e,rd),c=W(e,en),d=!i&&s,u=!i&&n>0&&o===0,g=!i&&l.length===0,h=d&&c;let m=null,S=null;d&&(m="noRowsOverlay"),g&&(m="noColumnsOverlay"),h&&(m="emptyPivotOverlay"),u&&(m="noResultsOverlay"),i&&(m="loadingOverlay",S=((I=(v=t.slotProps)==null?void 0:v.loadingOverlay)==null?void 0:I[s?"noRowsVariant":"variant"])??(s?"skeleton":"linear-progress"));const y={overlayType:m,loadingOverlayVariant:S};return{getOverlay:()=>{var k,$;if(!m)return null;const D=(k=t.slots)==null?void 0:k[m],H=($=t.slotProps)==null?void 0:$[m];return C.jsx(Bf,f({},y,{children:C.jsx(D,f({},H))}))},overlaysProps:y}},uo=je(e=>e.columnMenu);function Wf(){var p;const e=ut(),t=Z(),n=W(e,We),o=W(e,fg),r=W(e,dg),l=W(e,gi),s=W(e,()=>Or(e)===null),i=W(e,yg),c=W(e,xg),d=W(e,bo),u=W(e,Qn),g=W(e,uo),h=W(e,yt),m=W(e,af),S=!(i===null&&l===null&&s),y=e.current.columnHeadersContainerRef;return C.jsx(t.slots.columnHeaders,f({ref:y,visibleColumns:n,filterColumnLookup:o,sortColumnLookup:r,columnHeaderTabIndexState:l,columnGroupHeaderTabIndexState:i,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:u,columnMenuState:g,columnVisibility:h,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:S},(p=t.slotProps)==null?void 0:p.columnHeaders))}const Uf=Lt(Wf),Kf=ke("div")({position:"absolute",top:"var(--DataGrid-headersTotalHeight)",left:0,width:"calc(100% - (var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize)))"}),qf=ke("div",{name:"MuiDataGrid",slot:"Main",overridesResolver:(e,t)=>{const{ownerState:n,loadingOverlayVariant:o,overlayType:r}=e,l=o==="skeleton"||r==="noColumnsOverlay";return[t.main,n.hasPinnedRight&&t["main--hasPinnedRight"],l&&t["main--hiddenContent"]]}})({flexGrow:1,position:"relative",overflow:"hidden",display:"flex",flexDirection:"column"}),Xf=ue((e,t)=>{var s;const{ownerState:n}=e,o=Z(),l=Po().hooks.useGridAriaAttributes();return C.jsxs(qf,f({ownerState:n,className:e.className,tabIndex:-1},l,(s=o.slotProps)==null?void 0:s.main,{ref:t,children:[C.jsx(Kf,{role:"presentation","data-id":"gridPanelAnchor"}),e.children]}))}),Qf=()=>xe({root:["topContainer"]},ye,{}),Yf=ke("div")({position:"sticky",zIndex:40,top:0});function Zf(e){const t=Qf();return C.jsx(Yf,f({},e,{className:Fe(t.root,b["container--top"]),role:"presentation"}))}const Jf=(e,t)=>{const{classes:n}=e;return xe({root:["virtualScrollerContent",t&&"virtualScrollerContent--overflowed"]},ye,n)},eh=ke("div",{name:"MuiDataGrid",slot:"VirtualScrollerContent",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.virtualScrollerContent,n.overflowedContent&&t["virtualScrollerContent--overflowed"]]}})({}),th=ue(function(t,n){var i;const o=Z(),r=!o.autoHeight&&((i=t.style)==null?void 0:i.minHeight)==="auto",l=Jf(o,r),s={classes:o.classes,overflowedContent:r};return C.jsx(eh,f({},t,{ownerState:s,className:Fe(l.root,t.className),ref:n}))}),nh=ke("div")({display:"flex",flexDirection:"row",width:"var(--DataGrid-rowWidth)",boxSizing:"border-box"}),Gi=ke("div")({position:"sticky",height:"100%",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",backgroundColor:ee.cell.background.pinned}),oh=ke(Gi)({left:0,borderRight:"1px solid var(--rowBorderColor)"}),rh=ke(Gi)({right:0,borderLeft:"1px solid var(--rowBorderColor)"}),lh=ke("div")({flexGrow:1,borderTop:"1px solid var(--rowBorderColor)"});function sh({rowsLength:e}){const t=me(),{viewportOuterSize:n,minimumSize:o,hasScrollX:r,hasScrollY:l,scrollbarSize:s,leftPinnedWidth:i,rightPinnedWidth:c}=W(t,Re),d=r?s:0,u=n.height-o.height>0;return d===0&&!u?null:C.jsxs(nh,{className:b.filler,role:"presentation",style:{height:d,"--rowBorderColor":e===0?"transparent":"var(--DataGrid-rowBorderColor)"},children:[i>0&&C.jsx(oh,{className:b["filler--pinnedLeft"],style:{width:i}}),C.jsx(lh,{}),c>0&&C.jsx(rh,{className:b["filler--pinnedRight"],style:{width:c+(l?s:0)}})]})}const ih=Lt(sh),ch=["className"],ah=e=>{const{classes:t}=e;return xe({root:["virtualScrollerRenderZone"]},ye,t)},uh=ke("div",{name:"MuiDataGrid",slot:"VirtualScrollerRenderZone"})({position:"absolute",display:"flex",flexDirection:"column"}),dh=ue(function(t,n){const{className:o}=t,r=Q(t,ch),l=me(),s=Z(),i=ah(s),c=W(l,()=>{const d=Jn(l);return Zn(l).positions[d.firstRowIndex]??0});return C.jsx(uh,f({className:Fe(i.root,o),ownerState:s,style:{transform:`translate3d(0, ${c}px, 0)`}},r,{ref:n}))}),gh={includeHeaders:!0,includeOutliers:!1,outliersFactor:1.5,expand:!1,disableColumnVirtualization:!0},Ye=je(e=>e.editRows),Li=oe(Ye,(e,{rowId:t,editMode:n})=>n===Tt.Row&&!!e[t]),fh=oe(Ye,(e,{rowId:t,field:n})=>{var o;return((o=e[t])==null?void 0:o[n])??null}),tn=je(e=>e.preferencePanel),hh=oe(tn,(e,t)=>!!(e.open&&e.labelId===t));var lt=function(e){return e.filters="filters",e.columns="columns",e.aiAssistant="aiAssistant",e}(lt||{});function mh(e){return JSON.stringify([e.filterModel,e.sortModel,e.start,e.end])}class Ch{constructor({ttl:t=3e5,getKey:n=mh}){this.cache=void 0,this.ttl=void 0,this.getKey=void 0,this.cache={},this.ttl=t,this.getKey=n}set(t,n){const o=this.getKey(t),r=Date.now()+this.ttl;this.cache[o]={value:n,expiry:r}}get(t){const n=this.getKey(t),o=this.cache[n];if(o){if(Date.now()>o.expiry){delete this.cache[n];return}return o.value}}clear(){this.cache={}}}class bh extends Error{constructor(t){super(t.message),this.params=void 0,this.cause=void 0,this.name="GridGetRowsError",this.params=t.params,this.cause=t.cause}}class ph extends Error{constructor(t){super(t.message),this.params=void 0,this.cause=void 0,this.name="GridUpdateRowError",this.params=t.params,this.cause=t.cause}}const wh=(e,t)=>{const{classes:n}=e,o={root:["scrollbar",`scrollbar--${t}`],content:["scrollbarContent"]};return xe(o,ye,n)},$i=ke("div")({position:"absolute",display:"inline-block",zIndex:60,"&:hover":{zIndex:70},"--size":"calc(max(var(--DataGrid-scrollbarSize), 14px))"}),Sh=ke($i)({width:"var(--size)",height:"calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))",overflowY:"auto",overflowX:"hidden",outline:0,"& > div":{width:"var(--size)"},top:"var(--DataGrid-topContainerHeight)",right:"0px"}),xh=ke($i)({width:"100%",height:"var(--size)",overflowY:"hidden",overflowX:"auto",outline:0,"& > div":{height:"var(--size)"},bottom:"0px"}),Vl=ue(function(t,n){const o=ut(),r=Z(),l=a.useRef(!1),s=a.useRef(0),i=a.useRef(null),c=a.useRef(null),d=wh(r,t.position),u=W(o,Re),g=t.position==="vertical"?"height":"width",h=t.position==="vertical"?"scrollTop":"scrollLeft",m=t.position==="vertical"?"top":"left",S=t.position==="vertical"?u.hasScrollX:u.hasScrollY,y=u.minimumSize[g]+(S?u.scrollbarSize:0),v=(t.position==="vertical"?u.viewportInnerSize.height:u.viewportOuterSize.width)*(y/u.viewportOuterSize[g]),I=$e(()=>{const k=i.current,$=t.scrollPosition.current;if(!k||$[m]===s.current)return;if(s.current=$[m],l.current){l.current=!1;return}l.current=!0;const L=$[m]/y;k[h]=L*v}),D=$e(()=>{const k=o.current.virtualScrollerRef.current,$=i.current;if(!$)return;if(l.current){l.current=!1;return}l.current=!0;const L=$[h]/v;k[h]=L*y});Ss(()=>{const k=o.current.virtualScrollerRef.current,$=i.current,L={passive:!0};return k.addEventListener("scroll",I,L),$.addEventListener("scroll",D,L),()=>{k.removeEventListener("scroll",I,L),$.removeEventListener("scroll",D,L)}}),a.useEffect(()=>{c.current.style.setProperty(g,`${v}px`)},[v,g]);const H=t.position==="vertical"?Sh:xh;return C.jsx(H,{ref:at(n,i),className:d.root,style:t.position==="vertical"&&r.listView?{height:"100%",top:0}:void 0,tabIndex:-1,"aria-hidden":"true",onFocus:k=>{k.target.blur()},children:C.jsx("div",{ref:c,className:d.content})})}),yh=e=>{const{classes:t,hasScrollX:n,hasPinnedRight:o,loadingOverlayVariant:r,overlayType:l}=e;return xe({root:["main",o&&"main--hasPinnedRight",(r==="skeleton"||l==="noColumnsOverlay")&&"main--hiddenContent"],scroller:["virtualScroller",n&&"virtualScroller--hasScrollX"]},ye,t)},vh=ke("div",{name:"MuiDataGrid",slot:"VirtualScroller",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.virtualScroller,n.hasScrollX&&t["virtualScroller--hasScrollX"]]}})({position:"relative",height:"100%",flexGrow:1,overflow:"scroll",scrollbarWidth:"none",display:"flex",flexDirection:"column","&::-webkit-scrollbar":{display:"none"},"@media print":{overflow:"hidden"},zIndex:0}),Ih=e=>e.current.state.dimensions.rightPinnedWidth>0;function Mh(e){const t=me(),n=Z(),o=W(t,Gu),r=W(t,Ou),l=W(t,Ih),s=W(t,Ru),{getOverlay:i,overlaysProps:c}=_f(),d=f({classes:n.classes,hasScrollX:r,hasPinnedRight:l},c),u=yh(d),g=Gf(),{getContainerProps:h,getScrollerProps:m,getContentProps:S,getRenderZoneProps:y,getScrollbarVerticalProps:p,getScrollbarHorizontalProps:v,getRows:I,getScrollAreaProps:D}=g,H=I();return C.jsxs(Xf,f({className:u.root},h(),{ownerState:d,children:[C.jsx(Gl,f({scrollDirection:"left"},D())),C.jsx(Gl,f({scrollDirection:"right"},D())),C.jsxs(vh,f({className:u.scroller},m(),{ownerState:d,children:[C.jsxs(Zf,{children:[!n.listView&&C.jsx(Uf,{}),C.jsx(n.slots.pinnedRows,{position:"top",virtualScroller:g})]}),i(),C.jsx(th,f({},S(),{children:C.jsxs(dh,f({},y(),{children:[H,C.jsx(n.slots.detailPanels,{virtualScroller:g})]}))})),s&&C.jsx(ih,{rowsLength:H.length}),C.jsx(n.slots.bottomContainer,{children:C.jsx(n.slots.pinnedRows,{position:"bottom",virtualScroller:g})})]})),r&&!n.listView&&C.jsx(Vl,f({position:"horizontal"},v())),o&&C.jsx(Vl,f({position:"vertical"},p())),e.children]}))}function Ph(){var t;const e=Z();return e.hideFooter?null:C.jsx(e.slots.footer,f({},(t=e.slotProps)==null?void 0:t.footer))}let Zo;function Fh(){return Zo===void 0&&document.createElement("div").focus({get preventScroll(){return Zo=!0,!1}}),Zo}function Eh({defaultSlots:e,slots:t}){const n=t;if(!n||Object.keys(n).length===0)return e;const o=f({},e);return Object.keys(n).forEach(r=>{const l=r;n[l]!==void 0&&(o[l]=n[l])}),o}const kh=e=>{const t=e.match(/^__row_group_by_columns_group_(.*)__$/);return t?t[1]:null},Th=e=>e===ni||kh(e)!==null,Ai=(e,t)=>{if(e)if(t){if(e===ve.LEFT)return"right";if(e===ve.RIGHT)return"left"}else{if(e===ve.LEFT)return"left";if(e===ve.RIGHT)return"right"}};function Ro(e,t,n,o){const r=Ai(n,t);return!r||o===void 0||(e[r]=o),e}const Dh=["column","row","rowId","rowNode","align","children","colIndex","width","className","style","colSpan","disableDragEvents","isNotVisible","pinnedOffset","pinnedPosition","showRightBorder","showLeftBorder","onClick","onDoubleClick","onMouseDown","onMouseUp","onMouseOver","onKeyDown","onKeyUp","onDragEnter","onDragOver"],Hh=["changeReason","unstable_updateValueOnRender"];ve.LEFT+"",Nt.LEFT,ve.RIGHT+"",Nt.RIGHT,ve.NONE+"",ve.VIRTUAL+"";const Oh=e=>{const{align:t,showLeftBorder:n,showRightBorder:o,pinnedPosition:r,isEditable:l,isSelected:s,isSelectionMode:i,classes:c}=e,d={root:["cell",`cell--text${jn(t)}`,s&&"selected",l&&"cell--editable",n&&"cell--withLeftBorder",o&&"cell--withRightBorder",r===ve.LEFT&&"cell--pinnedLeft",r===ve.RIGHT&&"cell--pinnedRight",i&&!l&&"cell--selectionMode"]};return xe(d,ye,c)},Gh=ue(function(t,n){var Ee,ot,dt;const{column:o,row:r,rowId:l,rowNode:s,align:i,colIndex:c,width:d,className:u,style:g,colSpan:h,disableDragEvents:m,isNotVisible:S,pinnedOffset:y,pinnedPosition:p,showRightBorder:v,showLeftBorder:I,onClick:D,onDoubleClick:H,onMouseDown:k,onMouseUp:$,onMouseOver:L,onKeyDown:x,onKeyUp:w,onDragEnter:T,onDragOver:M}=t,O=Q(t,Dh),G=ut(),A=Z(),F=Dt(),E=o.field,P=W(G,fh,{rowId:l,field:E}),_=Po().hooks.useCellAggregationResult(l,E),V=P?Oe.Edit:Oe.View,R=G.current.getCellParamsForRow(l,E,r,{colDef:o,cellMode:V,rowNode:s,tabIndex:W(G,()=>{const pe=Or(G);return pe&&pe.field===E&&pe.id===l?0:-1}),hasFocus:W(G,()=>{const pe=st(G);return(pe==null?void 0:pe.id)===l&&pe.field===E})});R.api=G.current,_&&(R.value=_.value,R.formattedValue=o.valueFormatter?o.valueFormatter(R.value,r,o,G):R.value);const B=W(G,()=>G.current.unstable_applyPipeProcessors("isCellSelected",!1,{id:l,field:E})),q=W(G,ki),X=W(G,kf),{hasFocus:re,isEditable:Y=!1,value:ae}=R,ce=o.type==="actions"&&((Ee=o.getActions)==null?void 0:Ee.call(o,G.current.getRowParams(l)).some(pe=>!pe.props.disabled)),j=(V==="view"||!Y)&&!ce?R.tabIndex:-1,{classes:K,getCellClassName:te}=A,U=[W(G,()=>G.current.unstable_applyPipeProcessors("cellClassName",[],{id:l,field:E}).filter(Boolean).join(" "))];o.cellClassName&&U.push(typeof o.cellClassName=="function"?o.cellClassName(R):o.cellClassName),o.display==="flex"&&U.push(b["cell--flex"]),te&&U.push(te(R));const J=R.formattedValue??ae,ie=a.useRef(null),fe=at(n,ie),he=a.useRef(null),ge=A.cellSelection??!1,be={align:i,showLeftBorder:I,showRightBorder:v,isEditable:Y,classes:A.classes,pinnedPosition:p,isSelected:B,isSelectionMode:ge},Me=Oh(be),De=a.useCallback(pe=>Ue=>{const Ke=G.current.getCellParams(l,E||"");G.current.publishEvent(pe,Ke,Ue),$&&$(Ue)},[G,E,$,l]),se=a.useCallback(pe=>Ue=>{const Ke=G.current.getCellParams(l,E||"");G.current.publishEvent(pe,Ke,Ue),k&&k(Ue)},[G,E,k,l]),Ce=a.useCallback((pe,Ue)=>Ke=>{if(!G.current.getRow(l))return;const gt=G.current.getCellParams(l,E||"");G.current.publishEvent(pe,gt,Ke),Ue&&Ue(Ke)},[G,E,l]),Se=((ot=q[l])==null?void 0:ot[E])??!1,Pe=((dt=X[l])==null?void 0:dt[E])??1,Ge=a.useMemo(()=>{if(S)return{padding:0,opacity:0,width:0,height:0,border:0};const pe=Ro(f({"--width":`${d}px`},g),F,p,y),Ue=p===ve.LEFT,Ke=p===ve.RIGHT;return Pe>1&&(pe.height=`calc(var(--height) * ${Pe})`,pe.zIndex=10,(Ue||Ke)&&(pe.zIndex=40)),pe},[d,S,g,y,p,F,Pe]);if(a.useEffect(()=>{if(!re||V===Oe.Edit)return;const pe=Zt(G.current.rootElementRef.current);if(ie.current&&!ie.current.contains(pe.activeElement)){const Ue=ie.current.querySelector('[tabindex="0"]'),Ke=he.current||Ue||ie.current;if(Fh())Ke.focus({preventScroll:!0});else{const gt=G.current.getScrollPosition();Ke.focus(),G.current.scroll(gt)}}},[re,V,G]),Se)return C.jsx("div",{"data-colindex":c,role:"presentation",style:f({width:"var(--width)"},Ge)});let ze=O.onFocus,Ie,de;if(P===null&&o.renderCell&&(Ie=o.renderCell(R)),P!==null&&o.renderEditCell){const pe=G.current.getRowWithUpdatedValues(l,o.field),Ue=Q(P,Hh),Ke=o.valueFormatter?o.valueFormatter(P.value,pe,o,G):R.formattedValue,gt=f({},R,{row:pe,formattedValue:Ke},Ue);Ie=o.renderEditCell(gt),U.push(b["cell--editing"]),U.push(K==null?void 0:K["cell--editing"])}if(Ie===void 0){const pe=J==null?void 0:J.toString();Ie=pe,de=pe}a.isValidElement(Ie)&&ce&&(Ie=a.cloneElement(Ie,{focusElementRef:he}));const He=m?null:{onDragEnter:Ce("cellDragEnter",T),onDragOver:Ce("cellDragOver",M)};return C.jsx("div",f({className:Fe(Me.root,U,u),role:"gridcell","data-field":E,"data-colindex":c,"aria-colindex":c+1,"aria-colspan":h,"aria-rowspan":Pe,style:Ge,title:de,tabIndex:j,onClick:Ce("cellClick",D),onDoubleClick:Ce("cellDoubleClick",H),onMouseOver:Ce("cellMouseOver",L),onMouseDown:se("cellMouseDown"),onMouseUp:De("cellMouseUp"),onKeyDown:Ce("cellKeyDown",x),onKeyUp:Ce("cellKeyUp",w)},He,O,{onFocus:ze,ref:fe,children:Ie}))}),Lh=Lt(Gh),$h=["field","type","align","width","height","empty","style","className"],Nl="1.3em",Ah="1.2em",Bl=[40,80],Rh={number:[40,60],string:[40,80],date:[40,60],dateTime:[60,80],singleSelect:[40,80]},zh=e=>{const{align:t,classes:n,empty:o}=e,r={root:["cell","cellSkeleton",`cell--text${t?jn(t):"Left"}`,o&&"cellEmpty"]};return xe(r,ye,n)},Vh=xd(12345);function Nh(e){const{field:t,type:n,align:o,width:r,height:l,empty:s=!1,style:i,className:c}=e,d=Q(e,$h),u=Z(),g={classes:u.classes,align:o,empty:s},h=zh(g),m=a.useMemo(()=>{if(n==="boolean"||n==="actions")return{variant:"circular",width:Nl,height:Nl};const[y,p]=n?Rh[n]??Bl:Bl;return{variant:"text",width:`${Math.round(Vh(y,p))}%`,height:Ah}},[n]);return C.jsx("div",f({"data-field":t,className:Fe(h.root,c),style:f({height:l,maxWidth:r,minWidth:r},i)},d,{children:!s&&C.jsx(u.slots.baseSkeleton,f({},m))}))}const Bh=Lt(Nh),jh=10,so=-5,sn=1,jl={width:3,rx:1.5,x:10.5},_h="/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */",Wh=e=>e.current.state.dimensions.hasScrollX&&(!e.current.state.dimensions.hasScrollY||e.current.state.dimensions.scrollbarSize===0),Uh=Ne("div",{name:"MuiDataGrid",slot:"Root",overridesResolver:(e,t)=>[t.root,{[`&.${b.autoHeight}`]:t.autoHeight},{[`&.${b.autosizing}`]:t.autosizing},{[`&.${b["root--densityStandard"]}`]:t["root--densityStandard"]},{[`&.${b["root--densityComfortable"]}`]:t["root--densityComfortable"]},{[`&.${b["root--densityCompact"]}`]:t["root--densityCompact"]},{[`&.${b["root--disableUserSelection"]}`]:t["root--disableUserSelection"]},{[`&.${b["root--noToolbar"]}`]:t["root--noToolbar"]},{[`&.${b.withVerticalBorder}`]:t.withVerticalBorder},{[`& .${b.actionsCell}`]:t.actionsCell},{[`& .${b.booleanCell}`]:t.booleanCell},{[`& .${b.cell}`]:t.cell},{[`& .${b["cell--editable"]}`]:t["cell--editable"]},{[`& .${b["cell--editing"]}`]:t["cell--editing"]},{[`& .${b["cell--flex"]}`]:t["cell--flex"]},{[`& .${b["cell--pinnedLeft"]}`]:t["cell--pinnedLeft"]},{[`& .${b["cell--pinnedRight"]}`]:t["cell--pinnedRight"]},{[`& .${b["cell--rangeBottom"]}`]:t["cell--rangeBottom"]},{[`& .${b["cell--rangeLeft"]}`]:t["cell--rangeLeft"]},{[`& .${b["cell--rangeRight"]}`]:t["cell--rangeRight"]},{[`& .${b["cell--rangeTop"]}`]:t["cell--rangeTop"]},{[`& .${b["cell--selectionMode"]}`]:t["cell--selectionMode"]},{[`& .${b["cell--textCenter"]}`]:t["cell--textCenter"]},{[`& .${b["cell--textLeft"]}`]:t["cell--textLeft"]},{[`& .${b["cell--textRight"]}`]:t["cell--textRight"]},{[`& .${b["cell--withLeftBorder"]}`]:t["cell--withLeftBorder"]},{[`& .${b["cell--withRightBorder"]}`]:t["cell--withRightBorder"]},{[`& .${b.cellCheckbox}`]:t.cellCheckbox},{[`& .${b.cellEmpty}`]:t.cellEmpty},{[`& .${b.cellOffsetLeft}`]:t.cellOffsetLeft},{[`& .${b.cellSkeleton}`]:t.cellSkeleton},{[`& .${b.checkboxInput}`]:t.checkboxInput},{[`& .${b.columnHeader}`]:t.columnHeader},{[`& .${b["columnHeader--alignCenter"]}`]:t["columnHeader--alignCenter"]},{[`& .${b["columnHeader--alignLeft"]}`]:t["columnHeader--alignLeft"]},{[`& .${b["columnHeader--alignRight"]}`]:t["columnHeader--alignRight"]},{[`& .${b["columnHeader--dragging"]}`]:t["columnHeader--dragging"]},{[`& .${b["columnHeader--emptyGroup"]}`]:t["columnHeader--emptyGroup"]},{[`& .${b["columnHeader--filledGroup"]}`]:t["columnHeader--filledGroup"]},{[`& .${b["columnHeader--filtered"]}`]:t["columnHeader--filtered"]},{[`& .${b["columnHeader--last"]}`]:t["columnHeader--last"]},{[`& .${b["columnHeader--lastUnpinned"]}`]:t["columnHeader--lastUnpinned"]},{[`& .${b["columnHeader--moving"]}`]:t["columnHeader--moving"]},{[`& .${b["columnHeader--numeric"]}`]:t["columnHeader--numeric"]},{[`& .${b["columnHeader--pinnedLeft"]}`]:t["columnHeader--pinnedLeft"]},{[`& .${b["columnHeader--pinnedRight"]}`]:t["columnHeader--pinnedRight"]},{[`& .${b["columnHeader--siblingFocused"]}`]:t["columnHeader--siblingFocused"]},{[`& .${b["columnHeader--sortable"]}`]:t["columnHeader--sortable"]},{[`& .${b["columnHeader--sorted"]}`]:t["columnHeader--sorted"]},{[`& .${b["columnHeader--withLeftBorder"]}`]:t["columnHeader--withLeftBorder"]},{[`& .${b["columnHeader--withRightBorder"]}`]:t["columnHeader--withRightBorder"]},{[`& .${b.columnHeaderCheckbox}`]:t.columnHeaderCheckbox},{[`& .${b.columnHeaderDraggableContainer}`]:t.columnHeaderDraggableContainer},{[`& .${b.columnHeaderTitleContainer}`]:t.columnHeaderTitleContainer},{[`& .${b.columnHeaderTitleContainerContent}`]:t.columnHeaderTitleContainerContent},{[`& .${b.columnSeparator}`]:t.columnSeparator},{[`& .${b["columnSeparator--resizable"]}`]:t["columnSeparator--resizable"]},{[`& .${b["columnSeparator--resizing"]}`]:t["columnSeparator--resizing"]},{[`& .${b["columnSeparator--sideLeft"]}`]:t["columnSeparator--sideLeft"]},{[`& .${b["columnSeparator--sideRight"]}`]:t["columnSeparator--sideRight"]},{[`& .${b["container--bottom"]}`]:t["container--bottom"]},{[`& .${b["container--top"]}`]:t["container--top"]},{[`& .${b.detailPanelToggleCell}`]:t.detailPanelToggleCell},{[`& .${b["detailPanelToggleCell--expanded"]}`]:t["detailPanelToggleCell--expanded"]},{[`& .${b.editBooleanCell}`]:t.editBooleanCell},{[`& .${b.filterIcon}`]:t.filterIcon},{[`& .${b["filler--borderBottom"]}`]:t["filler--borderBottom"]},{[`& .${b["filler--pinnedLeft"]}`]:t["filler--pinnedLeft"]},{[`& .${b["filler--pinnedRight"]}`]:t["filler--pinnedRight"]},{[`& .${b.groupingCriteriaCell}`]:t.groupingCriteriaCell},{[`& .${b.groupingCriteriaCellLoadingContainer}`]:t.groupingCriteriaCellLoadingContainer},{[`& .${b.groupingCriteriaCellToggle}`]:t.groupingCriteriaCellToggle},{[`& .${b.headerFilterRow}`]:t.headerFilterRow},{[`& .${b.iconSeparator}`]:t.iconSeparator},{[`& .${b.menuIcon}`]:t.menuIcon},{[`& .${b.menuIconButton}`]:t.menuIconButton},{[`& .${b.menuList}`]:t.menuList},{[`& .${b.menuOpen}`]:t.menuOpen},{[`& .${b.overlayWrapperInner}`]:t.overlayWrapperInner},{[`& .${b.pinnedRows}`]:t.pinnedRows},{[`& .${b["pinnedRows--bottom"]}`]:t["pinnedRows--bottom"]},{[`& .${b["pinnedRows--top"]}`]:t["pinnedRows--top"]},{[`& .${b.row}`]:t.row},{[`& .${b["row--borderBottom"]}`]:t["row--borderBottom"]},{[`& .${b["row--detailPanelExpanded"]}`]:t["row--detailPanelExpanded"]},{[`& .${b["row--dragging"]}`]:t["row--dragging"]},{[`& .${b["row--dynamicHeight"]}`]:t["row--dynamicHeight"]},{[`& .${b["row--editable"]}`]:t["row--editable"]},{[`& .${b["row--editing"]}`]:t["row--editing"]},{[`& .${b["row--firstVisible"]}`]:t["row--firstVisible"]},{[`& .${b["row--lastVisible"]}`]:t["row--lastVisible"]},{[`& .${b.rowReorderCell}`]:t.rowReorderCell},{[`& .${b["rowReorderCell--draggable"]}`]:t["rowReorderCell--draggable"]},{[`& .${b.rowReorderCellContainer}`]:t.rowReorderCellContainer},{[`& .${b.rowReorderCellPlaceholder}`]:t.rowReorderCellPlaceholder},{[`& .${b.rowSkeleton}`]:t.rowSkeleton},{[`& .${b.scrollbar}`]:t.scrollbar},{[`& .${b["scrollbar--horizontal"]}`]:t["scrollbar--horizontal"]},{[`& .${b["scrollbar--vertical"]}`]:t["scrollbar--vertical"]},{[`& .${b.scrollbarFiller}`]:t.scrollbarFiller},{[`& .${b["scrollbarFiller--borderBottom"]}`]:t["scrollbarFiller--borderBottom"]},{[`& .${b["scrollbarFiller--borderTop"]}`]:t["scrollbarFiller--borderTop"]},{[`& .${b["scrollbarFiller--header"]}`]:t["scrollbarFiller--header"]},{[`& .${b["scrollbarFiller--pinnedRight"]}`]:t["scrollbarFiller--pinnedRight"]},{[`& .${b.sortIcon}`]:t.sortIcon},{[`& .${b.treeDataGroupingCell}`]:t.treeDataGroupingCell},{[`& .${b.treeDataGroupingCellLoadingContainer}`]:t.treeDataGroupingCellLoadingContainer},{[`& .${b.treeDataGroupingCellToggle}`]:t.treeDataGroupingCellToggle},{[`& .${b.withBorderColor}`]:t.withBorderColor}]})(()=>{const e=ut(),t=W(e,Wh),n=ee.colors.background.base,o=ee.header.background.base,r=ee.cell.background.pinned,l=Kh(ee.colors.interactive.hover),s=ee.colors.interactive.hoverOpacity,i=ee.colors.interactive.selected,c=ee.colors.interactive.selectedOpacity,d=i,u=`calc(${c} + ${s})`,g=zt(n,l,s),h=zt(n,i,c),m=zt(n,d,u),S=zt(r,l,s),y=zt(r,i,c),p=zt(r,d,u),v=L=>({[`& .${b["cell--pinnedLeft"]}, & .${b["cell--pinnedRight"]}`]:{backgroundColor:L,"&.Mui-selected":{backgroundColor:zt(L,h,c),"&:hover":{backgroundColor:zt(L,m,u)}}}}),I=v(S),D=v(y),H=v(p),k={backgroundColor:h,"&:hover":{backgroundColor:m,"@media (hover: none)":{backgroundColor:h}}};return{"--unstable_DataGrid-radius":ee.radius.base,"--unstable_DataGrid-headWeight":ee.typography.fontWeight.medium,"--DataGrid-rowBorderColor":ee.colors.border.base,"--DataGrid-cellOffsetMultiplier":2,"--DataGrid-width":"0px","--DataGrid-hasScrollX":"0","--DataGrid-hasScrollY":"0","--DataGrid-scrollbarSize":"10px","--DataGrid-rowWidth":"0px","--DataGrid-columnsTotalWidth":"0px","--DataGrid-leftPinnedWidth":"0px","--DataGrid-rightPinnedWidth":"0px","--DataGrid-headerHeight":"0px","--DataGrid-headersTotalHeight":"0px","--DataGrid-topContainerHeight":"0px","--DataGrid-bottomContainerHeight":"0px",flex:1,boxSizing:"border-box",position:"relative",borderWidth:"1px",borderStyle:"solid",borderColor:ee.colors.border.base,borderRadius:"var(--unstable_DataGrid-radius)",backgroundColor:ee.colors.background.base,color:ee.colors.foreground.base,font:ee.typography.font.body,outline:"none",height:"100%",display:"flex",minWidth:0,minHeight:0,flexDirection:"column",overflow:"hidden",overflowAnchor:"none",transform:"translate(0, 0)",[`.${b.main} > *:first-child${_h}`]:{borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"},[`&.${b.autoHeight}`]:{height:"auto"},[`&.${b.autosizing}`]:{[`& .${b.columnHeaderTitleContainerContent} > *`]:{overflow:"visible !important"},"@media (hover: hover)":{[`& .${b.menuIcon}`]:{width:"0 !important",visibility:"hidden !important"}},[`& .${b.cell}`]:{overflow:"visible !important",whiteSpace:"nowrap",minWidth:"max-content !important",maxWidth:"max-content !important"},[`& .${b.groupingCriteriaCell}`]:{width:"unset"},[`& .${b.treeDataGroupingCell}`]:{width:"unset"}},[`&.${b.withSidePanel}`]:{flexDirection:"row"},[`& .${b.mainContent}`]:{display:"flex",flexDirection:"column",overflow:"hidden",flex:1},[`& .${b.columnHeader}, & .${b.cell}`]:{WebkitTapHighlightColor:"transparent",padding:"0 10px",boxSizing:"border-box"},[`& .${b.columnHeader}:focus-within, & .${b.cell}:focus-within`]:{outline:`solid ${Ri(ee.colors.interactive.focus,.5)} ${sn}px`,outlineOffset:sn*-1},[`& .${b.columnHeader}:focus, & .${b.cell}:focus`]:{outline:`solid ${ee.colors.interactive.focus} ${sn}px`,outlineOffset:sn*-1},[`& .${b.columnHeader}:focus,
      & .${b["columnHeader--withLeftBorder"]},
      & .${b["columnHeader--withRightBorder"]},
      & .${b["columnHeader--siblingFocused"]},
      & .${b["virtualScroller--hasScrollX"]} .${b["columnHeader--lastUnpinned"]},
      & .${b["virtualScroller--hasScrollX"]} .${b["columnHeader--last"]}
      `]:{[`& .${b.columnSeparator}`]:{opacity:0},"@media (hover: none)":{[`& .${b["columnSeparator--resizable"]}`]:{opacity:1}},[`& .${b["columnSeparator--resizable"]}:hover`]:{opacity:1}},[`&.${b["root--noToolbar"]} [aria-rowindex="1"] [aria-colindex="1"]`]:{borderTopLeftRadius:"calc(var(--unstable_DataGrid-radius) - 1px)"},[`&.${b["root--noToolbar"]} [aria-rowindex="1"] .${b["columnHeader--last"]}`]:{borderTopRightRadius:t?"calc(var(--unstable_DataGrid-radius) - 1px)":void 0},[`& .${b.columnHeaderCheckbox}, & .${b.cellCheckbox}`]:{padding:0,justifyContent:"center",alignItems:"center"},[`& .${b.columnHeader}`]:{position:"relative",display:"flex",alignItems:"center",backgroundColor:o},[`& .${b["columnHeader--filter"]}`]:{paddingTop:8,paddingBottom:8,paddingRight:5,minHeight:"min-content",overflow:"hidden"},[`& .${b["virtualScroller--hasScrollX"]} .${b["columnHeader--last"]}`]:{overflow:"hidden"},[`& .${b["pivotPanelField--sorted"]} .${b.iconButtonContainer},
      & .${b["columnHeader--sorted"]} .${b.iconButtonContainer},
      & .${b["columnHeader--filtered"]} .${b.iconButtonContainer}`]:{visibility:"visible",width:"auto"},[`& .${b.pivotPanelField}:not(.${b["pivotPanelField--sorted"]}) .${b.sortButton},
      & .${b.columnHeader}:not(.${b["columnHeader--sorted"]}) .${b.sortButton}`]:{opacity:0,transition:ee.transition(["opacity"],{duration:ee.transitions.duration.short})},[`& .${b.columnHeaderTitleContainer}`]:{display:"flex",alignItems:"center",gap:ee.spacing(.25),minWidth:0,flex:1,whiteSpace:"nowrap",overflow:"hidden"},[`& .${b.columnHeaderTitleContainerContent}`]:{overflow:"hidden",display:"flex",alignItems:"center"},[`& .${b["columnHeader--filledGroup"]} .${b.columnHeaderTitleContainer}`]:{borderBottomWidth:"1px",borderBottomStyle:"solid",boxSizing:"border-box"},[`& .${b.sortIcon}, & .${b.filterIcon}`]:{fontSize:"inherit"},[`& .${b["columnHeader--sortable"]}`]:{cursor:"pointer"},[`& .${b["columnHeader--alignCenter"]} .${b.columnHeaderTitleContainer}`]:{justifyContent:"center"},[`& .${b["columnHeader--alignRight"]} .${b.columnHeaderDraggableContainer}, & .${b["columnHeader--alignRight"]} .${b.columnHeaderTitleContainer}`]:{flexDirection:"row-reverse"},[`& .${b["columnHeader--alignCenter"]} .${b.menuIcon}`]:{marginLeft:"auto"},[`& .${b["columnHeader--alignRight"]} .${b.menuIcon}`]:{marginRight:"auto",marginLeft:-5},[`& .${b["columnHeader--moving"]}`]:{backgroundColor:g},[`& .${b["columnHeader--pinnedLeft"]}, & .${b["columnHeader--pinnedRight"]}`]:{position:"sticky",zIndex:40,background:ee.header.background.base},[`& .${b.columnSeparator}`]:{position:"absolute",overflow:"hidden",zIndex:30,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",maxWidth:jh,color:ee.colors.border.base},[`& .${b.columnHeaders}`]:{width:"var(--DataGrid-rowWidth)",backgroundColor:o},"@media (hover: hover)":{[`& .${b.columnHeader}:hover`]:{[`& .${b.menuIcon}`]:{width:"auto",visibility:"visible"},[`& .${b.iconButtonContainer}`]:{visibility:"visible",width:"auto"}},[`& .${b.columnHeader}:not(.${b["columnHeader--sorted"]}):hover .${b.sortButton},
        & .${b.pivotPanelField}:not(.${b["pivotPanelField--sorted"]}):hover .${b.sortButton},
        & .${b.pivotPanelField}:not(.${b["pivotPanelField--sorted"]}) .${b.sortButton}:focus-visible`]:{opacity:.5}},"@media (hover: none)":{[`& .${b.columnHeader} .${b.menuIcon}`]:{width:"auto",visibility:"visible"},[`& .${b.columnHeader}:focus,
        & .${b["columnHeader--siblingFocused"]}`]:{[`.${b["columnSeparator--resizable"]}`]:{color:ee.colors.foreground.accent}},[`& .${b.pivotPanelField}:not(.${b["pivotPanelField--sorted"]}) .${b.sortButton}`]:{opacity:.5}},[`& .${b["columnSeparator--sideLeft"]}`]:{left:so},[`& .${b["columnSeparator--sideRight"]}`]:{right:so},[`& .${b["columnHeader--withRightBorder"]} .${b["columnSeparator--sideLeft"]}`]:{left:so-.5},[`& .${b["columnHeader--withRightBorder"]} .${b["columnSeparator--sideRight"]}`]:{right:so-.5},[`& .${b["columnSeparator--resizable"]}`]:{cursor:"col-resize",touchAction:"none",[`&.${b["columnSeparator--resizing"]}`]:{color:ee.colors.foreground.accent},"@media (hover: none)":{[`& .${b.iconSeparator} rect`]:jl},"@media (hover: hover)":{"&:hover":{color:ee.colors.foreground.accent,[`& .${b.iconSeparator} rect`]:jl}},"& svg":{pointerEvents:"none"}},[`& .${b.iconSeparator}`]:{color:"inherit",transition:ee.transition(["color","width"],{duration:ee.transitions.duration.short})},[`& .${b.menuIcon}`]:{width:0,visibility:"hidden",fontSize:20,marginRight:-5,display:"flex",alignItems:"center"},[`.${b.menuOpen}`]:{visibility:"visible",width:"auto"},[`& .${b.headerFilterRow}`]:{[`& .${b.columnHeader}`]:{boxSizing:"border-box",borderBottom:"1px solid var(--DataGrid-rowBorderColor)"}},[`& .${b["row--borderBottom"]} .${b.columnHeader},
      & .${b["row--borderBottom"]} .${b.filler},
      & .${b["row--borderBottom"]} .${b.scrollbarFiller}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${b["row--borderBottom"]} .${b.cell}`]:{borderBottom:"1px solid var(--rowBorderColor)"},[`.${b.row}`]:{display:"flex",width:"var(--DataGrid-rowWidth)",breakInside:"avoid","--rowBorderColor":"var(--DataGrid-rowBorderColor)",[`&.${b["row--firstVisible"]}`]:{"--rowBorderColor":"transparent"},"&:hover":{backgroundColor:g,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${b.rowSkeleton}:hover`]:{backgroundColor:"transparent"},"&.Mui-selected":k},[`& .${b["container--top"]}, & .${b["container--bottom"]}`]:{"[role=row]":{background:ee.colors.background.base}},[`& .${b.cell}`]:{flex:"0 0 auto",height:"var(--height)",width:"var(--width)",lineHeight:"calc(var(--height) - 1px)",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis","&.Mui-selected":k},[`& .${b["virtualScrollerContent--overflowed"]} .${b["row--lastVisible"]} .${b.cell}`]:{borderTopColor:"transparent"},[`& .${b.pinnedRows} .${b.row}, .${b.aggregationRowOverlayWrapper} .${b.row}`]:{backgroundColor:r,"&:hover":{backgroundColor:S}},[`& .${b["pinnedRows--top"]} :first-of-type`]:{[`& .${b.cell}, .${b.scrollbarFiller}`]:{borderTop:"none"}},[`&.${b["root--disableUserSelection"]}`]:{userSelect:"none"},[`& .${b["row--dynamicHeight"]} > .${b.cell}`]:{whiteSpace:"initial",lineHeight:"inherit"},[`& .${b.cellEmpty}`]:{flex:1,padding:0,height:"unset"},[`& .${b.cell}.${b["cell--selectionMode"]}`]:{cursor:"default"},[`& .${b.cell}.${b["cell--editing"]}`]:{padding:1,display:"flex",boxShadow:ee.shadows.base,backgroundColor:ee.colors.background.overlay,"&:focus-within":{outline:`${sn}px solid ${ee.colors.interactive.focus}`,outlineOffset:sn*-1}},[`& .${b["row--editing"]}`]:{boxShadow:ee.shadows.base},[`& .${b["row--editing"]} .${b.cell}`]:{boxShadow:"none",backgroundColor:ee.colors.background.overlay},[`& .${b.editBooleanCell}`]:{display:"flex",height:"100%",width:"100%",alignItems:"center",justifyContent:"center"},[`& .${b.booleanCell}[data-value="true"]`]:{color:ee.colors.foreground.muted},[`& .${b.booleanCell}[data-value="false"]`]:{color:ee.colors.foreground.disabled},[`& .${b.actionsCell}`]:{display:"inline-flex",alignItems:"center",gridGap:ee.spacing(1)},[`& .${b.rowReorderCell}`]:{display:"inline-flex",flex:1,alignItems:"center",justifyContent:"center",opacity:ee.colors.interactive.disabledOpacity},[`& .${b["rowReorderCell--draggable"]}`]:{cursor:"grab",opacity:1},[`& .${b.rowReorderCellContainer}`]:{padding:0,display:"flex",alignItems:"stretch"},[`.${b.withBorderColor}`]:{borderColor:ee.colors.border.base},[`& .${b["cell--withLeftBorder"]}, & .${b["columnHeader--withLeftBorder"]}`]:{borderLeftColor:"var(--DataGrid-rowBorderColor)",borderLeftWidth:"1px",borderLeftStyle:"solid"},[`& .${b["cell--withRightBorder"]}, & .${b["columnHeader--withRightBorder"]}`]:{borderRightColor:"var(--DataGrid-rowBorderColor)",borderRightWidth:"1px",borderRightStyle:"solid"},[`& .${b["cell--flex"]}`]:{display:"flex",alignItems:"center",lineHeight:"inherit"},[`& .${b["cell--textLeft"]}`]:{textAlign:"left",justifyContent:"flex-start"},[`& .${b["cell--textRight"]}`]:{textAlign:"right",justifyContent:"flex-end"},[`& .${b["cell--textCenter"]}`]:{textAlign:"center",justifyContent:"center"},[`& .${b["cell--pinnedLeft"]}, & .${b["cell--pinnedRight"]}`]:{position:"sticky",zIndex:30,background:ee.cell.background.pinned,"&.Mui-selected":{backgroundColor:y}},[`& .${b.row}`]:{"&:hover":I,"&.Mui-selected":D,"&.Mui-selected:hover":H},[`& .${b.cellOffsetLeft}`]:{flex:"0 0 auto",display:"inline-block"},[`& .${b.cellSkeleton}`]:{flex:"0 0 auto",height:"100%",display:"inline-flex",alignItems:"center"},[`& .${b.columnHeaderDraggableContainer}`]:{display:"flex",width:"100%",height:"100%"},[`& .${b.rowReorderCellPlaceholder}`]:{display:"none"},[`& .${b["columnHeader--dragging"]}, & .${b["row--dragging"]}`]:{background:ee.colors.background.overlay,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:ee.colors.interactive.disabledOpacity},[`& .${b["row--dragging"]}`]:{background:ee.colors.background.overlay,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:ee.colors.interactive.disabledOpacity,[`& .${b.rowReorderCellPlaceholder}`]:{display:"flex"}},[`& .${b.treeDataGroupingCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${b.treeDataGroupingCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:ee.spacing(2)},[`& .${b.treeDataGroupingCellLoadingContainer}, .${b.groupingCriteriaCellLoadingContainer}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},[`& .${b.groupingCriteriaCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${b.groupingCriteriaCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:ee.spacing(2)},[`& .${b.columnHeaders} .${b.scrollbarFiller}`]:{backgroundColor:o},[`.${b.scrollbarFiller}`]:{minWidth:"calc(var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))",alignSelf:"stretch",[`&.${b["scrollbarFiller--borderTop"]}`]:{borderTop:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${b["scrollbarFiller--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${b["scrollbarFiller--pinnedRight"]}`]:{backgroundColor:ee.cell.background.pinned,position:"sticky",right:0}},[`& .${b.filler}`]:{flex:"1 0 auto"},[`& .${b["filler--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${b.columnHeaders} .${b.filler}`]:{backgroundColor:o},[`& .${b["main--hiddenContent"]}`]:{[`& .${b.virtualScrollerContent}`]:{position:"fixed",visibility:"hidden"},[`& .${b["scrollbar--vertical"]}, & .${b.pinnedRows}, & .${b.virtualScroller} > .${b.filler}`]:{display:"none"}}}});function Ri(e,t){return`rgba(from ${e} r g b / ${t})`}function Kh(e){return Ri(e,1)}function zt(e,t,n){return`color-mix(in srgb,${e}, ${t} calc(${n} * 100%))`}const qh=()=>()=>{},Xh=()=>!1,Qh=()=>!0,Yh=()=>ws.useSyncExternalStore(qh,Xh,Qh),zi=a.createContext(void 0);function Vr(){const e=a.useContext(zi);if(e===void 0)throw new Error("MUI X: Missing context.");return e}function Zh({children:e}){const t=a.useRef(null),n=a.useRef(null),o=a.useRef(null),r=a.useMemo(()=>({columnsPanelTriggerRef:t,filterPanelTriggerRef:n,aiAssistantPanelTriggerRef:o}),[]);return C.jsx(zi.Provider,{value:r,children:e})}function Jh(){var d;const e=me(),t=W(e,bt),n=Z(),o=W(e,tn),{columnsPanelTriggerRef:r,filterPanelTriggerRef:l,aiAssistantPanelTriggerRef:s}=Vr(),i=e.current.unstable_applyPipeProcessors("preferencePanel",null,o.openedPanelValue??lt.filters);let c=null;switch(o.openedPanelValue){case lt.filters:c=l.current;break;case lt.columns:c=r.current;break;case lt.aiAssistant:c=s.current;break}return C.jsx(n.slots.panel,f({id:o.panelId,open:t.length>0&&o.open,"aria-labelledby":o.labelId,target:c,onClose:()=>e.current.hidePreferences()},(d=n.slotProps)==null?void 0:d.panel,{children:i}))}function em(){var t;const e=Z();return C.jsxs(a.Fragment,{children:[C.jsx(Jh,{}),e.showToolbar&&C.jsx(e.slots.toolbar,f({},(t=e.slotProps)==null?void 0:t.toolbar))]})}const tm=["className","children","sidePanel"],nm=(e,t)=>{const{autoHeight:n,classes:o,showCellVerticalBorder:r}=e,l={root:["root",n&&"autoHeight",`root--density${jn(t)}`,e.slots.toolbar===null&&"root--noToolbar","withBorderColor",r&&"withVerticalBorder"]};return xe(l,ye,o)},om=ue(function(t,n){const o=Z(),{className:r,children:l,sidePanel:s}=t,i=Q(t,tm),c=ut(),d=W(c,dn),u=c.current.rootElementRef,g=a.useCallback(v=>{v!==null&&c.current.publishEvent("rootMount",v)},[c]),h=at(u,n,g),m=o,S=nm(m,d),y=$d();return Yh()?null:C.jsxs(Uh,f({className:Fe(S.root,r,y.className,s&&b.withSidePanel),ownerState:m},i,{ref:h,children:[C.jsxs("div",{className:b.mainContent,role:"presentation",children:[C.jsx(em,{}),C.jsx(Mh,{children:l}),C.jsx(Ph,{})]}),s,y.tag]}))}),rm=Lt(om),lm=["className"],sm=e=>{const{classes:t}=e;return xe({root:["footerContainer","withBorderColor"]},ye,t)},im=ke("div",{name:"MuiDataGrid",slot:"FooterContainer"})({display:"flex",justifyContent:"space-between",alignItems:"center",minHeight:52,borderTop:"1px solid"}),cm=ue(function(t,n){const{className:o}=t,r=Q(t,lm),l=Z(),s=sm(l);return C.jsx(im,f({className:Fe(s.root,o),ownerState:l},r,{ref:n}))}),am=["className"],um=e=>{const{classes:t}=e;return xe({root:["overlay"]},ye,t)},dm=ke("div",{name:"MuiDataGrid",slot:"Overlay"})({width:"100%",height:"100%",display:"flex",gap:ee.spacing(1),flexDirection:"column",alignSelf:"center",alignItems:"center",justifyContent:"center",textAlign:"center",textWrap:"balance",backgroundColor:ee.colors.background.backdrop}),zo=ue(function(t,n){const{className:o}=t,r=Q(t,am),l=Z(),s=um(l);return C.jsx(dm,f({className:Fe(s.root,o),ownerState:l},r,{ref:n}))});function Ht(e,t,n,o={}){return typeof t=="function"?t(n,o):t?(t.props.className&&(n.className=gm(t.props.className,n.className)),(t.props.style||n.style)&&(n.style=f({},n.style,t.props.style)),a.cloneElement(t,n)):a.createElement(e,n)}function gm(e,t){return!e||!t?e||t:`${e} ${t}`}const Vi=a.createContext(void 0);function fm(){const e=a.useContext(Vi);if(e===void 0)throw new Error("MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.");return e}function hm(e,t){if(!e.ref.current||!t.ref.current)return 0;const n=e.ref.current.compareDocumentPosition(t.ref.current);return n?n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0:0}const mm=["render","className"],Cm=e=>{const{classes:t}=e;return xe({root:["toolbar"]},ye,t)},bm=ke("div",{name:"MuiDataGrid",slot:"Toolbar"})({flex:0,display:"flex",alignItems:"center",justifyContent:"end",gap:ee.spacing(.25),padding:ee.spacing(.75),minHeight:52,boxSizing:"border-box",borderBottom:`1px solid ${ee.colors.border.base}`}),pm=ue(function(t,n){const{render:o,className:r}=t,l=Q(t,mm),s=Z(),i=Cm(s),[c,d]=a.useState(null),[u,g]=a.useState([]),h=a.useCallback(()=>u.sort(hm),[u]),m=a.useCallback((k,$,L=!0)=>{var M,O;let x=k;const w=h(),T=w.length;for(let G=0;G<T;G+=1){if(x+=$,x>=T){if(!L)return-1;x=0}else if(x<0){if(!L)return-1;x=T-1}if(!((M=w[x].ref.current)!=null&&M.disabled)&&((O=w[x].ref.current)==null?void 0:O.ariaDisabled)!=="true")return x}return-1},[h]),S=a.useCallback((k,$)=>{g(L=>[...L,{id:k,ref:$}])},[]),y=a.useCallback(k=>{g($=>$.filter(L=>L.id!==k))},[]),p=a.useCallback(k=>{var w;if(!c)return;const $=h(),L=$.findIndex(T=>T.id===c);let x=-1;if(k.key==="ArrowRight"?(k.preventDefault(),x=m(L,1)):k.key==="ArrowLeft"?(k.preventDefault(),x=m(L,-1)):k.key==="Home"?(k.preventDefault(),x=m(-1,1,!1)):k.key==="End"&&(k.preventDefault(),x=m($.length,-1,!1)),x>=0&&x<$.length){const T=$[x];d(T.id),(w=T.ref.current)==null||w.focus()}},[h,c,m]),v=a.useCallback(k=>{c!==k&&d(k)},[c,d]),I=a.useCallback(k=>{var w;const $=h(),L=$.findIndex(T=>T.id===k),x=m(L,1);if(x>=0&&x<$.length){const T=$[x];d(T.id),(w=T.ref.current)==null||w.focus()}},[h,m]);a.useEffect(()=>{var $,L;const k=h();if(k.length>0){if(!c){d(k[0].id);return}const x=k.findIndex(w=>w.id===c);if(k[x]){if(x===-1){const w=k[x];w&&(d(w.id),(L=w.ref.current)==null||L.focus())}}else{const w=k[k.length-1];w&&(d(w.id),($=w.ref.current)==null||$.focus())}}},[h,m]);const D=a.useMemo(()=>({focusableItemId:c,registerItem:S,unregisterItem:y,onItemKeyDown:p,onItemFocus:v,onItemDisabled:I}),[c,S,y,p,v,I]),H=Ht(bm,o,f({role:"toolbar","aria-orientation":"horizontal","aria-label":s.label||void 0,className:Fe(i.root,r)},l,{ref:n}));return C.jsx(Vi.Provider,{value:D,children:H})}),wm=["render","onKeyDown","onFocus","disabled","aria-disabled"],go=ue(function(t,n){var x;const{render:o,onKeyDown:r,onFocus:l,disabled:s,"aria-disabled":i}=t,c=Q(t,wm),d=Ae(),u=Z(),g=a.useRef(null),h=at(g,n),{focusableItemId:m,registerItem:S,unregisterItem:y,onItemKeyDown:p,onItemFocus:v,onItemDisabled:I}=fm(),D=w=>{p(w),r==null||r(w)},H=w=>{v(d),l==null||l(w)};a.useEffect(()=>(S(d,g),()=>y(d)),[]);const k=a.useRef(s);a.useEffect(()=>{k.current!==s&&s===!0&&I(d,s),k.current=s},[s,d,I]);const $=a.useRef(i);a.useEffect(()=>{$.current!==i&&i===!0&&I(d,!0),$.current=i},[i,d,I]);const L=Ht(u.slots.baseIconButton,o,f({},(x=u.slotProps)==null?void 0:x.baseIconButton,{tabIndex:m===d?0:-1},c,{disabled:s,"aria-disabled":i,onKeyDown:D,onFocus:H,ref:h}));return C.jsx(a.Fragment,{children:L})}),Sm=e=>{const{classes:t,open:n}=e;return xe({root:["menuIcon",n&&"menuOpen"],button:["menuIconButton"]},ye,t)},xm=a.memo(e=>{var h,m;const{colDef:t,open:n,columnMenuId:o,columnMenuButtonId:r,iconButtonRef:l}=e,s=me(),i=Z(),c=f({},e,{classes:i.classes}),d=Sm(c),u=a.useCallback(S=>{S.preventDefault(),S.stopPropagation(),s.current.toggleColumnMenu(t.field)},[s,t.field]),g=t.headerName??t.field;return C.jsx("div",{className:d.root,children:C.jsx(i.slots.baseTooltip,f({title:s.current.getLocaleText("columnMenuLabel"),enterDelay:1e3},(h=i.slotProps)==null?void 0:h.baseTooltip,{children:C.jsx(i.slots.baseIconButton,f({ref:l,tabIndex:-1,className:d.button,"aria-label":s.current.getLocaleText("columnMenuAriaLabel")(g),size:"small",onClick:u,"aria-haspopup":"menu","aria-expanded":n,"aria-controls":n?o:void 0,id:r},(m=i.slotProps)==null?void 0:m.baseIconButton,{children:C.jsx(i.slots.columnMenuIcon,{fontSize:"inherit"})}))}))})});function ym({columnMenuId:e,columnMenuButtonId:t,ContentComponent:n,contentComponentProps:o,field:r,open:l,target:s,onExited:i}){const c=me(),d=c.current.getColumn(r),u=$e(g=>{g&&(g.stopPropagation(),s!=null&&s.contains(g.target))||c.current.hideColumnMenu()});return!s||!d?null:C.jsx(Fo,{position:`bottom-${d.align==="right"?"start":"end"}`,open:l,target:s,onClose:u,onExited:i,children:C.jsx(n,f({colDef:d,hideMenu:u,open:l,id:e,labelledby:t},o))})}function vm(e){return e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth}function So(e,t){return e.closest(`.${t}`)}function Rt(e){return e.replace(/["\\]/g,"\\$&")}function Im(e,t){return e.querySelector(`[role="columnheader"][data-field="${Rt(t)}"]`)}function Ni(e){return`.${b.row}[data-id="${Rt(String(e))}"]`}function Mm(e,t){return e.querySelector(Ni(t))}function Pm(e,{id:t,field:n}){const o=Ni(t),r=`.${b.cell}[data-field="${Rt(n)}"]`,l=`${o} ${r}`;return e.querySelector(l)}function eo(e){return e.target.nodeType===1&&!e.currentTarget.contains(e.target)}function Fm(e){return e.getAttribute("data-field")}function Em(e,t){return e.querySelector(`[data-field="${Rt(t)}"]`)}function km(e){return e.getAttribute("data-fields").slice(2,-2).split("-|-")}function Tm(e,t){return Array.from(e.querySelectorAll(`[data-fields*="|-${Rt(t)}-|"]`)??[])}function Dm(e,t){var s;if(!So(e,b.root))throw new Error("MUI X: The root element is not found.");const o=e.getAttribute("aria-colindex");if(!o)return[];const r=Number(o)-1,l=[];return(s=t.virtualScrollerRef)!=null&&s.current?(_i(t).forEach(i=>{const c=i.getAttribute("data-id");if(!c)return;let d=r;const u=t.unstable_getCellColSpanInfo(c,r);u&&u.spannedByColSpan&&(d=u.leftVisibleCellIndex);const g=i.querySelector(`[data-colindex="${d}"]`);g&&l.push(g)}),l):[]}function _l(e,t){return e.rootElementRef.current.querySelector(`.${b[t]}`)}const Bi=({api:e,colIndex:t,position:n,filterFn:o})=>{if(t===null)return[];const r=[];return _i(e).forEach(l=>{l.getAttribute("data-id")&&l.querySelectorAll(`.${b[n==="left"?"cell--pinnedLeft":"cell--pinnedRight"]}`).forEach(i=>{const c=En(i);c!==null&&o(c)&&r.push(i)})}),r};function Hm(e,t,n){const o=En(t);return Bi({api:e,colIndex:o,position:n?"right":"left",filterFn:r=>n?r<o:r>o})}function Om(e,t,n){const o=En(t);return Bi({api:e,colIndex:o,position:n?"left":"right",filterFn:r=>n?r>o:r<o})}const ji=({api:e,colIndex:t,position:n,filterFn:o})=>{var l;if(!((l=e.columnHeadersContainerRef)!=null&&l.current))return[];if(t===null)return[];const r=[];return e.columnHeadersContainerRef.current.querySelectorAll(`.${b[n==="left"?"columnHeader--pinnedLeft":"columnHeader--pinnedRight"]}`).forEach(s=>{const i=En(s);i!==null&&o(i,s)&&r.push(s)}),r};function Gm(e,t,n){const o=En(t);return ji({api:e,position:n?"right":"left",colIndex:o,filterFn:r=>n?r<o:r>o})}function Lm(e,t,n){const o=En(t);return ji({api:e,position:n?"left":"right",colIndex:o,filterFn:(r,l)=>l.classList.contains(b["columnHeader--last"])?!1:n?r>o:r<o})}function $m(e,t){return e.columnHeadersContainerRef.current.querySelector(`:scope > div > [data-field="${Rt(t)}"][role="columnheader"]`)}function Am(e,t){const n=e.virtualScrollerRef.current;return Array.from(n.querySelectorAll(`:scope > div > div > div > [data-field="${Rt(t)}"][role="gridcell"]`))}function _i(e){return e.virtualScrollerRef.current.querySelectorAll(`:scope > div > div > .${b.row}`)}function En(e){const t=e.getAttribute("aria-colindex");return t?Number(t)-1:null}const Rm=["className","aria-label"],zm=e=>{const{classes:t}=e;return xe({root:["columnHeaderTitle"]},ye,t)},Vm=ke("div",{name:"MuiDataGrid",slot:"ColumnHeaderTitle"})({textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",fontWeight:"var(--unstable_DataGrid-headWeight)",lineHeight:"normal"}),Nm=ue(function(t,n){const{className:o}=t,r=Q(t,Rm),l=Z(),s=zm(l);return C.jsx(Vm,f({className:Fe(s.root,o),ownerState:l},r,{ref:n}))});function Bm(e){var c;const{label:t,description:n}=e,o=Z(),r=a.useRef(null),[l,s]=a.useState(""),i=a.useCallback(()=>{if(!n&&(r!=null&&r.current)){const d=vm(r.current);s(d?t:"")}},[n,t]);return C.jsx(o.slots.baseTooltip,f({title:n||l},(c=o.slotProps)==null?void 0:c.baseTooltip,{children:C.jsx(Nm,{onMouseOver:i,ref:r,children:t})}))}const jm=["resizable","resizing","height","side"];var Nr=function(e){return e.Left="left",e.Right="right",e}(Nr||{});const _m=e=>{const{resizable:t,resizing:n,classes:o,side:r}=e,l={root:["columnSeparator",t&&"columnSeparator--resizable",n&&"columnSeparator--resizing",r&&`columnSeparator--side${jn(r)}`],icon:["iconSeparator"]};return xe(l,ye,o)};function Wm(e){const{height:t,side:n=Nr.Right}=e,o=Q(e,jm),r=Z(),l=f({},e,{side:n,classes:r.classes}),s=_m(l),i=a.useCallback(c=>{c.preventDefault(),c.stopPropagation()},[]);return C.jsx("div",f({className:s.root,style:{minHeight:t}},o,{onClick:i,children:C.jsx(r.slots.columnResizeIcon,{className:s.icon})}))}const Um=a.memo(Wm),Km=["classes","columnMenuOpen","colIndex","height","isResizing","sortDirection","hasFocus","tabIndex","separatorSide","isDraggable","headerComponent","description","elementId","width","columnMenuIconButton","columnMenu","columnTitleIconButtons","headerClassName","label","resizable","draggableContainerProps","columnHeaderSeparatorProps","style"],Wi=ue(function(t,n){const{classes:o,colIndex:r,height:l,isResizing:s,sortDirection:i,hasFocus:c,tabIndex:d,separatorSide:u,isDraggable:g,headerComponent:h,description:m,width:S,columnMenuIconButton:y=null,columnMenu:p=null,columnTitleIconButtons:v=null,headerClassName:I,label:D,resizable:H,draggableContainerProps:k,columnHeaderSeparatorProps:$,style:L}=t,x=Q(t,Km),w=ut(),T=Z(),M=a.useRef(null),O=at(M,n);let G="none";return i!=null&&(G=i==="asc"?"ascending":"descending"),a.useLayoutEffect(()=>{var F;const A=w.current.state.columnMenu;if(c&&!A.open){const P=M.current.querySelector('[tabindex="0"]')||M.current;P==null||P.focus(),(F=w.current.columnHeadersContainerRef)!=null&&F.current&&(w.current.columnHeadersContainerRef.current.scrollLeft=0)}},[w,c]),C.jsxs("div",f({className:Fe(o.root,I),style:f({},L,{width:S}),role:"columnheader",tabIndex:d,"aria-colindex":r+1,"aria-sort":G},x,{ref:O,children:[C.jsxs("div",f({className:o.draggableContainer,draggable:g,role:"presentation"},k,{children:[C.jsxs("div",{className:o.titleContainer,role:"presentation",children:[C.jsx("div",{className:o.titleContainerContent,children:h!==void 0?h:C.jsx(Bm,{label:D,description:m,columnWidth:S})}),v]}),y]})),C.jsx(Um,f({resizable:!T.disableColumnResize&&!!H,resizing:s,height:l,side:u},$)),p]}))}),qm=e=>{const{colDef:t,classes:n,isDragging:o,sortDirection:r,showRightBorder:l,showLeftBorder:s,filterItemsCounter:i,pinnedPosition:c,isLastUnpinned:d,isSiblingFocused:u}=e,g=r!=null,h=i!=null&&i>0,m=t.type==="number",S={root:["columnHeader",t.headerAlign==="left"&&"columnHeader--alignLeft",t.headerAlign==="center"&&"columnHeader--alignCenter",t.headerAlign==="right"&&"columnHeader--alignRight",t.sortable&&"columnHeader--sortable",o&&"columnHeader--moving",g&&"columnHeader--sorted",h&&"columnHeader--filtered",m&&"columnHeader--numeric","withBorderColor",l&&"columnHeader--withRightBorder",s&&"columnHeader--withLeftBorder",c===ve.LEFT&&"columnHeader--pinnedLeft",c===ve.RIGHT&&"columnHeader--pinnedRight",d&&"columnHeader--lastUnpinned",u&&"columnHeader--siblingFocused"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer"],titleContainerContent:["columnHeaderTitleContainerContent"]};return xe(S,ye,n)};function Xm(e){var ce,j,K;const{colDef:t,columnMenuOpen:n,colIndex:o,headerHeight:r,isResizing:l,isLast:s,sortDirection:i,sortIndex:c,filterItemsCounter:d,hasFocus:u,tabIndex:g,disableReorder:h,separatorSide:m,showLeftBorder:S,showRightBorder:y,pinnedPosition:p,pinnedOffset:v}=e,I=ut(),D=Z(),H=Dt(),k=a.useRef(null),$=Ae(),L=Ae(),x=a.useRef(null),[w,T]=a.useState(n),M=a.useMemo(()=>!D.disableColumnReorder&&!h&&!t.disableReorder,[D.disableColumnReorder,h,t.disableReorder]);let O;t.renderHeader&&(O=t.renderHeader(I.current.getColumnHeaderParams(t.field)));const G=f({},e,{classes:D.classes,showRightBorder:y,showLeftBorder:S}),A=qm(G),F=a.useCallback(te=>ne=>{eo(ne)||I.current.publishEvent(te,I.current.getColumnHeaderParams(t.field),ne)},[I,t.field]),E=a.useMemo(()=>({onClick:F("columnHeaderClick"),onContextMenu:F("columnHeaderContextMenu"),onDoubleClick:F("columnHeaderDoubleClick"),onMouseOver:F("columnHeaderOver"),onMouseOut:F("columnHeaderOut"),onMouseEnter:F("columnHeaderEnter"),onMouseLeave:F("columnHeaderLeave"),onKeyDown:F("columnHeaderKeyDown"),onFocus:F("columnHeaderFocus"),onBlur:F("columnHeaderBlur")}),[F]),P=a.useMemo(()=>M?{onDragStart:F("columnHeaderDragStart"),onDragEnter:F("columnHeaderDragEnter"),onDragOver:F("columnHeaderDragOver"),onDragEndCapture:F("columnHeaderDragEnd")}:{},[M,F]),z=a.useMemo(()=>({onMouseDown:F("columnSeparatorMouseDown"),onDoubleClick:F("columnSeparatorDoubleClick")}),[F]);a.useEffect(()=>{w||T(n)},[w,n]);const _=a.useCallback(()=>{T(!1)},[]),V=!D.disableColumnMenu&&!t.disableColumnMenu&&C.jsx(xm,{colDef:t,columnMenuId:$,columnMenuButtonId:L,open:w,iconButtonRef:x}),R=C.jsx(ym,{columnMenuId:$,columnMenuButtonId:L,field:t.field,open:n,target:x.current,ContentComponent:D.slots.columnMenu,contentComponentProps:(ce=D.slotProps)==null?void 0:ce.columnMenu,onExited:_}),B=t.sortingOrder??D.sortingOrder,q=(t.sortable||i!=null)&&!t.hideSortIcons&&!D.disableColumnSorting,X=C.jsxs(a.Fragment,{children:[!D.disableColumnFilter&&C.jsx(D.slots.columnHeaderFilterIconButton,f({field:t.field,counter:d},(j=D.slotProps)==null?void 0:j.columnHeaderFilterIconButton)),q&&C.jsx(D.slots.columnHeaderSortIcon,f({field:t.field,direction:i,index:c,sortingOrder:B,disabled:!t.sortable},(K=D.slotProps)==null?void 0:K.columnHeaderSortIcon))]});a.useLayoutEffect(()=>{var ne;const te=I.current.state.columnMenu;if(u&&!te.open){const J=k.current.querySelector('[tabindex="0"]')||k.current;J==null||J.focus(),(ne=I.current.columnHeadersContainerRef)!=null&&ne.current&&(I.current.columnHeadersContainerRef.current.scrollLeft=0)}},[I,u]);const re=typeof t.headerClassName=="function"?t.headerClassName({field:t.field,colDef:t}):t.headerClassName,Y=t.headerName??t.field,ae=a.useMemo(()=>Ro(f({},e.style),H,p,v),[p,v,e.style,H]);return C.jsx(Wi,f({ref:k,classes:A,columnMenuOpen:n,colIndex:o,height:r,isResizing:l,sortDirection:i,hasFocus:u,tabIndex:g,separatorSide:m,isDraggable:M,headerComponent:O,description:t.description,elementId:t.field,width:t.computedWidth,columnMenuIconButton:V,columnTitleIconButtons:X,headerClassName:Fe(re,s&&b["columnHeader--last"]),label:Y,resizable:!D.disableColumnResize&&!!t.resizable,"data-field":t.field,columnMenu:R,draggableContainerProps:P,columnHeaderSeparatorProps:z,style:ae},E))}const Qm=Lt(Xm),Ym=["className"],Zm=e=>{const{classes:t}=e;return xe({root:["iconButtonContainer"]},ye,t)},Jm=ke("div",{name:"MuiDataGrid",slot:"IconButtonContainer"})(()=>({display:"flex",visibility:"hidden",width:0})),Ui=ue(function(t,n){const{className:o}=t,r=Q(t,Ym),l=Z(),s=Zm(l);return C.jsx(Jm,f({className:Fe(s.root,o),ownerState:l},r,{ref:n}))}),eC=["sortingOrder"],tC=a.memo(function(t){const{sortingOrder:n}=t,o=Q(t,eC),r=Z(),[l]=n,s=l==="asc"?r.slots.columnSortedAscendingIcon:r.slots.columnSortedDescendingIcon;return s?C.jsx(s,f({},o)):null}),nC=["direction","index","sortingOrder","disabled","className"],oC=e=>{const{classes:t}=e;return xe({root:["sortButton"],icon:["sortIcon"]},ye,t)},rC=ke($t,{name:"MuiDataGrid",slot:"SortButton"})({transition:ee.transition(["opacity"],{duration:ee.transitions.duration.short,easing:ee.transitions.easing.easeInOut})});function lC(e,t,n,o){let r;const l={};return t==="asc"?r=e.columnSortedAscendingIcon:t==="desc"?r=e.columnSortedDescendingIcon:(r=tC,l.sortingOrder=o),r?C.jsx(r,f({fontSize:"small",className:n},l)):null}function sC(e){var m;const{direction:t,index:n,sortingOrder:o,disabled:r,className:l}=e,s=Q(e,nC),i=me(),c=Z(),d=f({},e,{classes:c.classes}),u=oC(d),g=lC(c.slots,t,u.icon,o);if(!g)return null;const h=C.jsx(rC,f({as:c.slots.baseIconButton,ownerState:d,"aria-label":i.current.getLocaleText("columnHeaderSortIconLabel"),title:i.current.getLocaleText("columnHeaderSortIconLabel"),size:"small",disabled:r,className:Fe(u.root,l)},(m=c.slotProps)==null?void 0:m.baseIconButton,s,{children:g}));return C.jsxs(a.Fragment,{children:[n!=null&&C.jsx(c.slots.baseBadge,{badgeContent:n,color:"default",overlap:"circular",children:h}),n==null&&h]})}function iC(e){return C.jsx(Ui,{children:C.jsx(sC,f({},e,{tabIndex:-1}))})}const cC=a.memo(iC),aC=e=>{const{classes:t}=e;return xe({icon:["filterIcon"]},ye,t)};function uC(e){return e.counter?C.jsx(dC,f({},e)):null}function dC(e){var m,S;const{counter:t,field:n,onClick:o}=e,r=me(),l=Z(),s=f({},e,{classes:l.classes}),i=aC(s),c=Ae(),d=W(r,hh,c),u=Ae(),g=a.useCallback(y=>{y.preventDefault(),y.stopPropagation();const{open:p,openedPanelValue:v}=tn(r);p&&v===lt.filters?r.current.hideFilterPanel():r.current.showFilterPanel(void 0,u,c),o&&o(r.current.getColumnHeaderParams(n),y)},[r,n,o,u,c]);if(!t)return null;const h=C.jsx(l.slots.baseIconButton,f({id:c,onClick:g,"aria-label":r.current.getLocaleText("columnHeaderFiltersLabel"),size:"small",tabIndex:-1,"aria-haspopup":"menu","aria-expanded":d,"aria-controls":d?u:void 0},(m=l.slotProps)==null?void 0:m.baseIconButton,{children:C.jsx(l.slots.columnFilteredIcon,{className:i.icon,fontSize:"small"})}));return C.jsx(l.slots.baseTooltip,f({title:r.current.getLocaleText("columnHeaderFiltersTooltipActive")(t),enterDelay:1e3},(S=l.slotProps)==null?void 0:S.baseTooltip,{children:C.jsxs(Ui,{children:[t>1&&C.jsx(l.slots.baseBadge,{badgeContent:t,color:"default",children:h}),t===1&&h]})}))}function Ki(e){return e.key.length===1&&!e.ctrlKey&&!e.metaKey}const xo=e=>e.indexOf("Arrow")===0||e.indexOf("Page")===0||e===" "||e==="Home"||e==="End",gC=e=>!!e.key,qi=e=>e==="Tab"||e==="Escape";function Xi(e){return(e.ctrlKey||e.metaKey)&&String.fromCharCode(e.keyCode)==="V"&&!e.shiftKey&&!e.altKey}function fC(e){return(e.ctrlKey||e.metaKey)&&String.fromCharCode(e.keyCode)==="C"&&!e.shiftKey&&!e.altKey}const hC=["hideMenu","colDef","id","labelledby","className","children","open"],mC=Ne($t)(()=>({minWidth:248})),CC=ue(function(t,n){const{hideMenu:o,id:r,labelledby:l,className:s,children:i,open:c}=t,d=Q(t,hC),u=Z(),g=a.useCallback(h=>{h.key==="Tab"&&h.preventDefault(),qi(h.key)&&o(h)},[o]);return C.jsx(mC,f({as:u.slots.baseMenuList,id:r,className:Fe(b.menuList,s),"aria-labelledby":l,onKeyDown:g,autoFocus:c},d,{ref:n,children:i}))}),bC=["displayOrder"],pC=e=>{const t=ut(),n=Z(),{defaultSlots:o,defaultSlotProps:r,slots:l={},slotProps:s={},hideMenu:i,colDef:c,addDividers:d=!0}=e,u=a.useMemo(()=>f({},o,l),[o,l]),g=a.useMemo(()=>{if(!s||Object.keys(s).length===0)return r;const S=f({},s);return Object.entries(r).forEach(([y,p])=>{S[y]=f({},p,s[y]||{})}),S},[r,s]),h=t.current.unstable_applyPipeProcessors("columnMenu",[],e.colDef),m=a.useMemo(()=>{const S=Object.keys(o);return Object.keys(l).filter(y=>!S.includes(y))},[l,o]);return a.useMemo(()=>{const p=Array.from(new Set([...h,...m])).filter(v=>u[v]!=null).sort((v,I)=>{const D=g[v],H=g[I],k=Number.isFinite(D==null?void 0:D.displayOrder)?D.displayOrder:100,$=Number.isFinite(H==null?void 0:H.displayOrder)?H.displayOrder:100;return k-$});return p.reduce((v,I,D)=>{let H={colDef:c,onClick:i};const k=g[I];if(k){const $=Q(k,bC);H=f({},H,$)}return d&&D!==p.length-1?[...v,[u[I],H],[n.slots.baseDivider,{}]]:[...v,[u[I],H]]},[])},[d,c,h,i,u,g,m,n.slots.baseDivider])};function wC(e){const{colDef:t,onClick:n}=e,o=me(),r=Z(),i=We(o).filter(d=>d.disableColumnMenu!==!0).length===1,c=a.useCallback(d=>{i||(o.current.setColumnVisibility(t.field,!1),n(d))},[o,t.field,n,i]);return r.disableColumnSelector||t.hideable===!1?null:C.jsx(r.slots.baseMenuItem,{onClick:c,disabled:i,iconStart:C.jsx(r.slots.columnMenuHideIcon,{fontSize:"small"}),children:o.current.getLocaleText("columnMenuHideColumn")})}function SC(e){const{onClick:t}=e,n=me(),o=Z(),r=a.useCallback(l=>{t(l),n.current.showPreferences(lt.columns)},[n,t]);return o.disableColumnSelector?null:C.jsx(o.slots.baseMenuItem,{onClick:r,iconStart:C.jsx(o.slots.columnMenuManageColumnsIcon,{fontSize:"small"}),children:n.current.getLocaleText("columnMenuManageColumns")})}function xC(e){return C.jsxs(a.Fragment,{children:[C.jsx(wC,f({},e)),C.jsx(SC,f({},e))]})}function yC(e){const{colDef:t,onClick:n}=e,o=me(),r=Z(),l=a.useCallback(s=>{n(s),o.current.showFilterPanel(t.field)},[o,t.field,n]);return r.disableColumnFilter||!t.filterable?null:C.jsx(r.slots.baseMenuItem,{onClick:l,iconStart:C.jsx(r.slots.columnMenuFilterIcon,{fontSize:"small"}),children:o.current.getLocaleText("columnMenuFilter")})}function vC(e){const{colDef:t,onClick:n}=e,o=me(),r=W(o,ht),l=Z(),s=a.useMemo(()=>{if(!t)return null;const u=r.find(g=>g.field===t.field);return u==null?void 0:u.sort},[t,r]),i=t.sortingOrder??l.sortingOrder,c=a.useCallback(u=>{n(u);const g=u.currentTarget.getAttribute("data-value")||null;o.current.sortColumn(t.field,g===s?null:g)},[o,t,n,s]);if(l.disableColumnSorting||!t||!t.sortable||!i.some(u=>!!u))return null;const d=u=>{const g=o.current.getLocaleText(u);return typeof g=="function"?g(t):g};return C.jsxs(a.Fragment,{children:[i.includes("asc")&&s!=="asc"?C.jsx(l.slots.baseMenuItem,{onClick:c,"data-value":"asc",iconStart:C.jsx(l.slots.columnMenuSortAscendingIcon,{fontSize:"small"}),children:d("columnMenuSortAsc")}):null,i.includes("desc")&&s!=="desc"?C.jsx(l.slots.baseMenuItem,{onClick:c,"data-value":"desc",iconStart:C.jsx(l.slots.columnMenuSortDescendingIcon,{fontSize:"small"}),children:d("columnMenuSortDesc")}):null,i.includes(null)&&s!=null?C.jsx(l.slots.baseMenuItem,{onClick:c,iconStart:l.slots.columnMenuUnsortIcon?C.jsx(l.slots.columnMenuUnsortIcon,{fontSize:"small"}):C.jsx("span",{}),children:o.current.getLocaleText("columnMenuUnsort")}):null]})}const IC=["defaultSlots","defaultSlotProps","slots","slotProps"],MC={columnMenuSortItem:vC,columnMenuFilterItem:yC,columnMenuColumnsItem:xC},PC={columnMenuSortItem:{displayOrder:10},columnMenuFilterItem:{displayOrder:20},columnMenuColumnsItem:{displayOrder:30}},FC=ue(function(t,n){const{defaultSlots:o,defaultSlotProps:r,slots:l,slotProps:s}=t,i=Q(t,IC),c=pC(f({},i,{defaultSlots:o,defaultSlotProps:r,slots:l,slotProps:s}));return C.jsx(CC,f({},i,{ref:n,children:c.map(([d,u],g)=>C.jsx(d,f({},u),g))}))}),EC=ue(function(t,n){return C.jsx(FC,f({},t,{ref:n,defaultSlots:MC,defaultSlotProps:PC}))}),kC=["className"],TC=e=>{const{classes:t}=e;return xe({root:["panelWrapper"]},ye,t)},DC=Ne("div",{name:"MuiDataGrid",slot:"PanelWrapper"})({display:"flex",flexDirection:"column",flex:1,"&:focus":{outline:0}}),Qi=ue(function(t,n){const{className:o}=t,r=Q(t,kC),l=Z(),s=TC(l);return C.jsx(DC,f({tabIndex:-1,className:Fe(s.root,o),ownerState:l},r,{ref:n}))});function HC(e){var n;const t=Z();return C.jsx(Qi,f({},e,{children:C.jsx(t.slots.columnsManagement,f({},(n=t.slotProps)==null?void 0:n.columnsManagement))}))}const OC=["children","className","classes","onClose"],GC=ps("MuiDataGrid",["panel","paper"]),LC=Ne($t,{name:"MuiDataGrid",slot:"panel"})({zIndex:ee.zIndex.panel}),$C=Ne("div",{name:"MuiDataGrid",slot:"panelContent"})({backgroundColor:ee.colors.background.overlay,borderRadius:ee.radius.base,boxShadow:ee.shadows.overlay,display:"flex",maxWidth:`calc(100vw - ${ee.spacing(2)})`,overflow:"auto"}),AC=ue((e,t)=>{var I;const{children:n,className:o,onClose:r}=e,l=Q(e,OC),s=me(),i=Z(),c=GC,[d,u]=a.useState(!1),g=Ys(),h=$e(()=>u(!0)),m=$e(()=>u(!1)),S=$e(()=>{r==null||r()}),y=$e(D=>{D.key==="Escape"&&(r==null||r())}),[p,v]=a.useState(null);return a.useEffect(()=>{var H,k;const D=(k=(H=s.current.rootElementRef)==null?void 0:H.current)==null?void 0:k.querySelector('[data-id="gridPanelAnchor"]');D&&v(D)},[s]),p?C.jsx(LC,f({as:i.slots.basePopper,ownerState:i,placement:"bottom-end",className:Fe(c.panel,o,g),flip:!0,onDidShow:h,onDidHide:m,onClickAway:S,clickAwayMouseEvent:"onPointerUp",clickAwayTouchEvent:!1,focusTrap:!0},l,(I=i.slotProps)==null?void 0:I.basePopper,{target:e.target??p,ref:t,children:C.jsx($C,{className:c.paper,ownerState:i,onKeyDown:y,children:d&&n})})):null}),RC=["className"],zC=e=>{const{classes:t}=e;return xe({root:["panelContent"]},ye,t)},VC=ke("div",{name:"MuiDataGrid",slot:"PanelContent"})({display:"flex",flexDirection:"column",overflow:"auto",flex:"1 1",maxHeight:400,padding:ee.spacing(2.5,1.5,2,1),gap:ee.spacing(2.5)});function NC(e){const{className:t}=e,n=Q(e,RC),o=Z(),r=zC(o);return C.jsx(VC,f({className:Fe(r.root,t),ownerState:o},n))}const BC=["className"],jC=e=>{const{classes:t}=e;return xe({root:["panelFooter"]},ye,t)},_C=Ne("div",{name:"MuiDataGrid",slot:"PanelFooter"})({padding:ee.spacing(1),display:"flex",justifyContent:"space-between",borderTop:`1px solid ${ee.colors.border.base}`});function WC(e){const{className:t}=e,n=Q(e,BC),o=Z(),r=jC(o);return C.jsx(_C,f({className:Fe(r.root,t),ownerState:o},n))}const UC=["item","hasMultipleFilters","deleteFilter","applyFilterChanges","showMultiFilterOperators","disableMultiFilterOperator","applyMultiFilterOperatorChanges","focusElementRef","logicOperators","columnsSort","filterColumns","deleteIconProps","logicOperatorInputProps","operatorInputProps","columnInputProps","valueInputProps","readOnly","children"],KC=["InputComponentProps"],qC=e=>{const{classes:t}=e;return xe({root:["filterForm"],deleteIcon:["filterFormDeleteIcon"],logicOperatorInput:["filterFormLogicOperatorInput"],columnInput:["filterFormColumnInput"],operatorInput:["filterFormOperatorInput"],valueInput:["filterFormValueInput"]},ye,t)},XC=Ne("div",{name:"MuiDataGrid",slot:"FilterForm"})({display:"flex",gap:ee.spacing(1.5)}),QC=Ne("div",{name:"MuiDataGrid",slot:"FilterFormDeleteIcon"})({flexShrink:0,display:"flex",justifyContent:"center",alignItems:"center"}),YC=Ne("div",{name:"MuiDataGrid",slot:"FilterFormLogicOperatorInput"})({minWidth:75,justifyContent:"end"}),ZC=Ne("div",{name:"MuiDataGrid",slot:"FilterFormColumnInput"})({width:150}),JC=Ne("div",{name:"MuiDataGrid",slot:"FilterFormOperatorInput"})({width:150}),eb=Ne("div",{name:"MuiDataGrid",slot:"FilterFormValueInput"})({width:190}),tb=e=>{switch(e){case rt.And:return"filterPanelOperatorAnd";case rt.Or:return"filterPanelOperatorOr";default:throw new Error("MUI X: Invalid `logicOperator` property in the `GridFilterPanel`.")}},Dn=e=>e.headerName||e.field,Wl=new Intl.Collator,Ul=ue(function(t,n){var ie,fe,he,ge,be,Me,De;const{item:o,hasMultipleFilters:r,deleteFilter:l,applyFilterChanges:s,showMultiFilterOperators:i,disableMultiFilterOperator:c,applyMultiFilterOperatorChanges:d,focusElementRef:u,logicOperators:g=[rt.And,rt.Or],columnsSort:h,filterColumns:m,deleteIconProps:S={},logicOperatorInputProps:y={},operatorInputProps:p={},columnInputProps:v={},valueInputProps:I={},readOnly:D}=t,H=Q(t,UC),k=me(),$=W(k,At),L=W(k,Ks),x=W(k,Qe),w=Ae(),T=Ae(),M=Ae(),O=Ae(),G=Z(),A=qC(G),F=a.useRef(null),E=a.useRef(null),P=x.logicOperator??rt.And,z=r&&g.length>0,V=(((ie=G.slotProps)==null?void 0:ie.baseSelect)||{}).native??!1,R=((fe=G.slotProps)==null?void 0:fe.baseSelectOption)||{},{InputComponentProps:B}=I,q=Q(I,KC),X=W(k,en),re=W(k,Oi),{filteredColumns:Y,selectedField:ae}=a.useMemo(()=>{let se=o.field;const Ce=$[o.field].filterable===!1?$[o.field]:null;if(Ce)return{filteredColumns:[Ce],selectedField:se};if(X)return{filteredColumns:L.filter(Pe=>re.get(Pe.field)!==void 0),selectedField:se};if(m===void 0||typeof m!="function")return{filteredColumns:L,selectedField:se};const Se=m({field:o.field,columns:L,currentFilters:(x==null?void 0:x.items)||[]});return{filteredColumns:L.filter(Pe=>{const Ge=Se.includes(Pe.field);return Pe.field===o.field&&!Ge&&(se=void 0),Ge}),selectedField:se}},[o.field,$,X,m,L,x==null?void 0:x.items,re]),ce=a.useMemo(()=>{switch(h){case"asc":return Y.sort((se,Ce)=>Wl.compare(Dn(se),Dn(Ce)));case"desc":return Y.sort((se,Ce)=>-Wl.compare(Dn(se),Dn(Ce)));default:return Y}},[Y,h]),j=o.field?k.current.getColumn(o.field):null,K=a.useMemo(()=>{var se;return!o.operator||!j?null:(se=j.filterOperators)==null?void 0:se.find(Ce=>Ce.value===o.operator)},[o,j]),te=a.useCallback(se=>{const Ce=se.target.value,Se=k.current.getColumn(Ce);if(Se.field===j.field)return;const Pe=Se.filterOperators.find(Ie=>Ie.value===o.operator)||Se.filterOperators[0];let ze=!Pe.InputComponent||Pe.InputComponent!==(K==null?void 0:K.InputComponent)||Se.type!==j.type?void 0:o.value;if(Se.type==="singleSelect"&&ze!==void 0){const Ie=Se,de=Jt(Ie);Array.isArray(ze)?ze=ze.filter(He=>po(He,de,Ie==null?void 0:Ie.getOptionValue)!==void 0):po(o.value,de,Ie==null?void 0:Ie.getOptionValue)===void 0&&(ze=void 0)}s(f({},o,{field:Ce,operator:Pe.value,value:ze}))},[k,s,o,j,K]),ne=a.useCallback(se=>{const Ce=se.target.value,Se=j==null?void 0:j.filterOperators.find(Ge=>Ge.value===Ce),Pe=!(Se!=null&&Se.InputComponent)||(Se==null?void 0:Se.InputComponent)!==(K==null?void 0:K.InputComponent);s(f({},o,{operator:Ce,value:Pe?void 0:o.value}))},[s,o,j,K]),U=a.useCallback(se=>{const Ce=se.target.value===rt.And.toString()?rt.And:rt.Or;d(Ce)},[d]),J=()=>{l(o)};return a.useImperativeHandle(u,()=>({focus:()=>{var se;K!=null&&K.InputComponent?(se=F==null?void 0:F.current)==null||se.focus():E.current.focus()}}),[K]),C.jsxs(XC,f({className:A.root,"data-id":o.id,ownerState:G},H,{ref:n,children:[C.jsx(QC,f({},S,{className:Fe(A.deleteIcon,S.className),ownerState:G,children:C.jsx(G.slots.baseIconButton,f({"aria-label":k.current.getLocaleText("filterPanelDeleteIconLabel"),title:k.current.getLocaleText("filterPanelDeleteIconLabel"),onClick:J,size:"small",disabled:D},(he=G.slotProps)==null?void 0:he.baseIconButton,{children:C.jsx(G.slots.filterPanelDeleteIcon,{fontSize:"small"})}))})),C.jsx(YC,f({as:G.slots.baseSelect,sx:[z?{display:"flex"}:{display:"none"},i?{visibility:"visible"}:{visibility:"hidden"},y.sx],className:Fe(A.logicOperatorInput,y.className),ownerState:G},y,{size:"small",slotProps:{htmlInput:{"aria-label":k.current.getLocaleText("filterPanelLogicOperator")}},value:P??"",onChange:U,disabled:!!c||g.length===1,native:V},(ge=G.slotProps)==null?void 0:ge.baseSelect,{children:g.map(se=>a.createElement(G.slots.baseSelectOption,f({},R,{native:V,key:se.toString(),value:se.toString()}),k.current.getLocaleText(tb(se))))})),C.jsx(ZC,f({as:G.slots.baseSelect},v,{className:Fe(A.columnInput,v.className),ownerState:G,size:"small",labelId:T,id:w,label:k.current.getLocaleText("filterPanelColumns"),value:ae??"",onChange:te,native:V,disabled:D},(be=G.slotProps)==null?void 0:be.baseSelect,{children:ce.map(se=>a.createElement(G.slots.baseSelectOption,f({},R,{native:V,key:se.field,value:se.field}),Dn(se)))})),C.jsx(JC,f({as:G.slots.baseSelect,size:"small"},p,{className:Fe(A.operatorInput,p.className),ownerState:G,labelId:O,label:k.current.getLocaleText("filterPanelOperator"),id:M,value:o.operator,onChange:ne,native:V,inputRef:E,disabled:D},(Me=G.slotProps)==null?void 0:Me.baseSelect,{children:(De=j==null?void 0:j.filterOperators)==null?void 0:De.map(se=>a.createElement(G.slots.baseSelectOption,f({},R,{native:V,key:se.value,value:se.value}),se.label||k.current.getLocaleText(`filterOperator${jn(se.value)}`)))})),C.jsx(eb,f({},q,{className:Fe(A.valueInput,q.className),ownerState:G,children:K!=null&&K.InputComponent?C.jsx(K.InputComponent,f({apiRef:k,item:o,applyValue:s,focusElementRef:F,disabled:D,slotProps:{root:{size:"small"}}},K.InputComponentProps,B),o.field):null}))]}))}),nb=["logicOperators","columnsSort","filterFormProps","getColumnForNewFilter","children","disableAddFilterButton","disableRemoveAllButton"],Kl=e=>({field:e.field,operator:e.filterOperators[0].value,id:Math.round(Math.random()*1e5)}),ob=ue(function(t,n){var O,G;const o=me(),r=Z(),l=W(o,Qe),s=W(o,Ks),i=W(o,Md),c=a.useRef(null),d=a.useRef(null),{logicOperators:u=[rt.And,rt.Or],columnsSort:g,filterFormProps:h,getColumnForNewFilter:m,disableAddFilterButton:S=!1,disableRemoveAllButton:y=!1}=t,p=Q(t,nb),v=o.current.upsertFilterItem,I=a.useCallback(A=>{o.current.setFilterLogicOperator(A)},[o]),D=a.useCallback(()=>{let A;if(m&&typeof m=="function"){const F=m({currentFilters:(l==null?void 0:l.items)||[],columns:s});if(F===null)return null;A=s.find(({field:E})=>E===F)}else A=s.find(F=>{var E;return(E=F.filterOperators)==null?void 0:E.length});return A?Kl(A):null},[l==null?void 0:l.items,s,m]),H=a.useCallback(()=>{if(m===void 0||typeof m!="function")return D();const A=l.items.length?l.items:[D()].filter(Boolean),F=m({currentFilters:A,columns:s});if(F===null)return null;const E=s.find(({field:P})=>P===F);return E?Kl(E):null},[l.items,s,m,D]),k=a.useMemo(()=>l.items.length?l.items:(d.current||(d.current=D()),d.current?[d.current]:[]),[l.items,D]),$=k.length>1,{readOnlyFilters:L,validFilters:x}=a.useMemo(()=>k.reduce((A,F)=>(i[F.field]?A.validFilters.push(F):A.readOnlyFilters.push(F),A),{readOnlyFilters:[],validFilters:[]}),[k,i]),w=a.useCallback(()=>{const A=H();A&&o.current.upsertFilterItems([...k,A])},[o,H,k]),T=a.useCallback(A=>{const F=x.length===1;o.current.deleteFilterItem(A),F&&o.current.hideFilterPanel()},[o,x.length]),M=a.useCallback(()=>x.length===1&&x[0].value===void 0?(o.current.deleteFilterItem(x[0]),o.current.hideFilterPanel()):o.current.setFilterModel(f({},l,{items:L}),"removeAllFilterItems"),[o,L,l,x]);return a.useEffect(()=>{u.length>0&&l.logicOperator&&!u.includes(l.logicOperator)&&I(u[0])},[u,I,l.logicOperator]),a.useEffect(()=>{x.length>0&&c.current.focus()},[x.length]),C.jsxs(Qi,f({},p,{ref:n,children:[C.jsxs(NC,{children:[L.map((A,F)=>C.jsx(Ul,f({item:A,applyFilterChanges:v,deleteFilter:T,hasMultipleFilters:$,showMultiFilterOperators:F>0,disableMultiFilterOperator:F!==1,applyMultiFilterOperatorChanges:I,focusElementRef:null,readOnly:!0,logicOperators:u,columnsSort:g},h),A.id==null?F:A.id)),x.map((A,F)=>C.jsx(Ul,f({item:A,applyFilterChanges:v,deleteFilter:T,hasMultipleFilters:$,showMultiFilterOperators:L.length+F>0,disableMultiFilterOperator:L.length+F!==1,applyMultiFilterOperatorChanges:I,focusElementRef:F===x.length-1?c:null,logicOperators:u,columnsSort:g},h),A.id==null?F+L.length:A.id))]}),!r.disableMultipleColumnsFiltering&&!(S&&y)?C.jsxs(WC,{children:[S?C.jsx("span",{}):C.jsx(r.slots.baseButton,f({onClick:w,startIcon:C.jsx(r.slots.filterPanelAddIcon,{})},(O=r.slotProps)==null?void 0:O.baseButton,{children:o.current.getLocaleText("filterPanelAddFilter")})),!y&&x.length>0?C.jsx(r.slots.baseButton,f({onClick:M,startIcon:C.jsx(r.slots.filterPanelRemoveAllIcon,{})},(G=r.slotProps)==null?void 0:G.baseButton,{children:o.current.getLocaleText("filterPanelRemoveAll")})):null]}):null]}))}),rb=(e,t)=>{const n=new Set(Object.keys(e).filter(l=>e[l]===!1)),o=new Set(Object.keys(t).filter(l=>t[l]===!1));if(n.size!==o.size)return!1;let r=!0;return n.forEach(l=>{o.has(l)||(r=!1)}),r},lb=(e,t)=>(e.headerName||e.field).toLowerCase().indexOf(t)>-1,sb=["children"],ib=xs({from:{opacity:0},to:{opacity:1}}),cb=xs({"from, to":{"--scrollable":'" "'}}),ab=ke("div",{name:"MuiDataGrid",slot:"ShadowScrollArea"})`
  flex: 1;
  display: flex;
  flex-direction: column;
  animation: ${cb};
  animation-timeline: --scroll-timeline;
  animation-fill-mode: none;
  box-sizing: border-box;
  overflow: auto;
  scrollbar-width: thin;
  scroll-timeline: --scroll-timeline block;

  &::before,
  &::after {
    content: '';
    flex-shrink: 0;
    display: block;
    position: sticky;
    left: 0;
    width: 100%;
    height: 4px;
    animation: ${ib} linear both;
    animation-timeline: --scroll-timeline;

    // Custom property toggle trick:
    // - Detects if the element is scrollable
    // - https://css-tricks.com/the-css-custom-property-toggle-trick/
    --visibility-scrollable: var(--scrollable) visible;
    --visibility-not-scrollable: hidden;
    visibility: var(--visibility-scrollable, var(--visibility-not-scrollable));
  }

  &::before {
    top: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 0, transparent 100%);
    animation-range: 0 4px;
  }

  &::after {
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.05) 0, transparent 100%);
    animation-direction: reverse;
    animation-range: calc(100% - 4px) 100%;
  }
`,ub=ue(function(t,n){const{children:o}=t,r=Q(t,sb);return C.jsx(ab,f({},r,{ref:n,children:o}))}),db=e=>{const{classes:t}=e;return xe({root:["columnsManagement"],header:["columnsManagementHeader"],searchInput:["columnsManagementSearchInput"],footer:["columnsManagementFooter"],row:["columnsManagementRow"]},ye,t)},ql=new Intl.Collator;function gb(e){var z,_,V,R;const t=me(),n=a.useRef(null),o=W(t,Id),r=W(t,yt),l=Z(),[s,i]=a.useState(""),c=db(l),d=W(t,bt),u=W(t,en),g=W(t,Oi),h=a.useMemo(()=>u?Array.from(g.values()):d,[u,g,d]),{sort:m,searchPredicate:S=lb,autoFocusSearchField:y=!0,disableShowHideToggle:p=!1,disableResetButton:v=!1,toggleAllMode:I="all",getTogglableColumns:D,searchInputProps:H}=e,k=a.useMemo(()=>rb(r,o),[r,o]),$=a.useMemo(()=>{switch(m){case"asc":return[...h].sort((B,q)=>ql.compare(B.headerName||B.field,q.headerName||q.field));case"desc":return[...h].sort((B,q)=>-ql.compare(B.headerName||B.field,q.headerName||q.field));default:return h}},[h,m]),L=B=>{const{name:q}=B.target;t.current.setColumnVisibility(q,r[q]===!1)},x=a.useMemo(()=>{const B=D?D($):null,q=B?$.filter(({field:X})=>B.includes(X)):$;return s?q.filter(X=>S(X,s.toLowerCase())):q},[$,s,S,D]),w=a.useCallback(B=>{const q=yt(t),X=f({},q),re=D?D(h):null;return(I==="filteredOnly"?x:h).forEach(Y=>{Y.hideable&&(re==null||re.includes(Y.field))&&(B?delete X[Y.field]:X[Y.field]=!1)}),t.current.setColumnVisibilityModel(X)},[t,h,D,I,x]),T=a.useCallback(B=>{i(B.target.value)},[]),M=a.useMemo(()=>x.filter(B=>B.hideable),[x]),O=a.useMemo(()=>M.every(B=>r[B.field]==null||r[B.field]!==!1),[r,M]),G=a.useMemo(()=>M.every(B=>r[B.field]===!1),[r,M]),A=a.useRef(null);a.useEffect(()=>{var B;y?(B=n.current)==null||B.focus():A.current&&typeof A.current.focus=="function"&&A.current.focus()},[y]);let F=!1;const E=B=>F===!1&&B.hideable!==!1?(F=!0,!0):!1,P=a.useCallback(()=>{var B;i(""),(B=n.current)==null||B.focus()},[]);return C.jsxs(a.Fragment,{children:[C.jsx(mb,{className:c.header,ownerState:l,children:C.jsx(Cb,f({as:l.slots.baseTextField,ownerState:l,placeholder:t.current.getLocaleText("columnsManagementSearchTitle"),inputRef:n,className:c.searchInput,value:s,onChange:T,size:"small",type:"search",slotProps:{input:{startAdornment:C.jsx(l.slots.quickFilterIcon,{fontSize:"small"}),endAdornment:C.jsx(l.slots.baseIconButton,f({size:"small","aria-label":t.current.getLocaleText("columnsManagementDeleteIconLabel"),style:s?{visibility:"visible"}:{visibility:"hidden"},tabIndex:-1,onClick:P,edge:"end"},(z=l.slotProps)==null?void 0:z.baseIconButton,{children:C.jsx(l.slots.quickFilterClearIcon,{fontSize:"small"})}))},htmlInput:{"aria-label":t.current.getLocaleText("columnsManagementSearchTitle")}},autoComplete:"off",fullWidth:!0},(_=l.slotProps)==null?void 0:_.baseTextField,H))}),C.jsx(hb,{ownerState:l,children:C.jsxs(fb,{className:c.root,ownerState:l,children:[x.map(B=>{var q;return C.jsx(l.slots.baseCheckbox,f({className:c.row,disabled:B.hideable===!1||u,checked:r[B.field]!==!1,onChange:L,name:B.field,inputRef:E(B)?A:void 0,label:B.headerName||B.field,density:"compact",fullWidth:!0},(q=l.slotProps)==null?void 0:q.baseCheckbox),B.field)}),x.length===0&&C.jsx(pb,{ownerState:l,children:t.current.getLocaleText("columnsManagementNoColumns")})]})}),!p||!v?C.jsxs(bb,{ownerState:l,className:c.footer,children:[p?C.jsx("span",{}):C.jsx(l.slots.baseCheckbox,f({disabled:M.length===0||u,checked:O,indeterminate:!O&&!G,onChange:()=>w(!O),name:t.current.getLocaleText("columnsManagementShowHideAllText"),label:t.current.getLocaleText("columnsManagementShowHideAllText"),density:"compact"},(V=l.slotProps)==null?void 0:V.baseCheckbox)),v?null:C.jsx(l.slots.baseButton,f({onClick:()=>t.current.setColumnVisibilityModel(o),disabled:k||u},(R=l.slotProps)==null?void 0:R.baseButton,{children:t.current.getLocaleText("columnsManagementReset")}))]}):null]})}const fb=Ne("div",{name:"MuiDataGrid",slot:"ColumnsManagement"})({display:"flex",flexDirection:"column",padding:ee.spacing(.5,1.5)}),hb=Ne(ub,{name:"MuiDataGrid",slot:"ColumnsManagementScrollArea"})({maxHeight:300}),mb=Ne("div",{name:"MuiDataGrid",slot:"ColumnsManagementHeader"})({padding:ee.spacing(1.5,2),borderBottom:`1px solid ${ee.colors.border.base}`}),Cb=Ne($t,{name:"MuiDataGrid",slot:"ColumnsManagementSearchInput"})({[`& .${oo.input}::-webkit-search-decoration,
      & .${oo.input}::-webkit-search-cancel-button,
      & .${oo.input}::-webkit-search-results-button,
      & .${oo.input}::-webkit-search-results-decoration`]:{display:"none"}}),bb=Ne("div",{name:"MuiDataGrid",slot:"ColumnsManagementFooter"})({padding:ee.spacing(1,1,1,1.5),display:"flex",justifyContent:"space-between",borderTop:`1px solid ${ee.colors.border.base}`}),pb=Ne("div",{name:"MuiDataGrid",slot:"ColumnsManagementEmptyText"})({padding:ee.spacing(1,0),alignSelf:"center",font:ee.typography.font.body}),wb=ue(function(t,n){var I,D;const{children:o,slotProps:r={}}=t,l=r.button||{},s=r.tooltip||{},i=me(),c=Z(),d=Ae(),u=Ae(),[g,h]=a.useState(!1),m=a.useRef(null),S=at(n,m),y=H=>{var k;h($=>!$),(k=l.onClick)==null||k.call(l,H)},p=()=>h(!1),v=H=>{H.key==="Tab"&&H.preventDefault(),qi(H.key)&&p()};return o==null?null:C.jsxs(a.Fragment,{children:[C.jsx(c.slots.baseTooltip,f({title:i.current.getLocaleText("toolbarExportLabel"),enterDelay:1e3},(I=c.slotProps)==null?void 0:I.baseTooltip,s,{children:C.jsx(c.slots.baseButton,f({size:"small",startIcon:C.jsx(c.slots.exportIcon,{}),"aria-expanded":g,"aria-label":i.current.getLocaleText("toolbarExportLabel"),"aria-haspopup":"menu","aria-controls":g?u:void 0,id:d},(D=c.slotProps)==null?void 0:D.baseButton,l,{onClick:y,ref:S,children:i.current.getLocaleText("toolbarExport")}))})),C.jsx(Fo,{open:g,target:m.current,onClose:p,position:"bottom-end",children:C.jsx(c.slots.baseMenuList,{id:u,className:b.menuList,"aria-labelledby":d,onKeyDown:v,autoFocusItem:g,children:a.Children.map(o,H=>a.isValidElement(H)?a.cloneElement(H,{hideMenu:p}):H)})})]})}),Sb=["hideMenu","options"],xb=["hideMenu","options"],yb=["csvOptions","printOptions","excelOptions"];function vb(e){const t=me(),n=Z(),{hideMenu:o,options:r}=e,l=Q(e,Sb);return C.jsx(n.slots.baseMenuItem,f({onClick:()=>{t.current.exportDataAsCsv(r),o==null||o()}},l,{children:t.current.getLocaleText("toolbarExportCSV")}))}function Ib(e){const t=me(),n=Z(),{hideMenu:o,options:r}=e,l=Q(e,xb);return C.jsx(n.slots.baseMenuItem,f({onClick:()=>{t.current.exportDataAsPrint(r),o==null||o()}},l,{children:t.current.getLocaleText("toolbarExportPrint")}))}ue(function(t,n){const o=t,{csvOptions:r={},printOptions:l={},excelOptions:s}=o,i=Q(o,yb),d=me().current.unstable_applyPipeProcessors("exportMenu",[],{excelOptions:s,csvOptions:r,printOptions:l}).sort((u,g)=>u.componentName>g.componentName?1:-1);return d.length===0?null:C.jsx(wb,f({},i,{ref:n,children:d.map((u,g)=>a.cloneElement(u.component,{key:g}))}))});function ct(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){const r=e.length;if(r!==t.length)return!1;for(let l=0;l<r;l+=1)if(!ct(e[l],t[l]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let l=0;l<r.length;l+=1)if(!t.has(r[l][0]))return!1;for(let l=0;l<r.length;l+=1){const s=r[l];if(!ct(s[1],t.get(s[0])))return!1}return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let l=0;l<r.length;l+=1)if(!t.has(r[l][0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){const r=e.length;if(r!==t.length)return!1;for(let l=0;l<r;l+=1)if(e[l]!==t[l])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const n=Object.keys(e),o=n.length;if(o!==Object.keys(t).length)return!1;for(let r=0;r<o;r+=1)if(!Object.prototype.hasOwnProperty.call(t,n[r]))return!1;for(let r=0;r<o;r+=1){const l=n[r];if(!ct(e[l],t[l]))return!1}return!0}return e!==e&&t!==t}const Yi=a.createContext(void 0);function Br(){const e=a.useContext(Yi);if(e===void 0)throw new Error("MUI X: Missing context. Quick Filter subcomponents must be placed within a <QuickFilter /> component.");return e}const Mb=["render","className","parser","formatter","debounceMs","defaultExpanded","expanded","onExpandedChange"],Pb=e=>e.split(" ").filter(t=>t!==""),Fb=e=>e.join(" ");function Eb(e){const t=Z(),{render:n,className:o,parser:r=Pb,formatter:l=Fb,debounceMs:s=t.filterDebounceMs,defaultExpanded:i,expanded:c,onExpandedChange:d}=e,u=Q(e,Mb),g=me(),h=a.useRef(null),m=a.useRef(null),S=W(g,li),[y,p]=a.useState(l(S??[])),[v,I]=a.useState(i??y.length>0),D=c??v,H=a.useMemo(()=>({value:y,expanded:D}),[y,D]),k=typeof o=="function"?o(H):o,$=a.useRef(null),L=Ae(),x=a.useCallback(P=>{d&&d(P),c===void 0&&I(P)},[d,c]),w=a.useRef(S);a.useEffect(()=>{ct(w.current,S)||(w.current=S,p(P=>ct(r(P),S)?P:l(S??[])))},[S,l,r]);const T=a.useRef(!0),M=a.useRef(D);a.useEffect(()=>{var P;if(T.current){T.current=!1;return}M.current!==D&&(D?requestAnimationFrame(()=>{var z;(z=h.current)==null||z.focus({preventScroll:!0})}):(P=m.current)==null||P.focus({preventScroll:!0}),M.current=D)},[D]);const O=a.useMemo(()=>ys(P=>{const z=r(P);w.current=z,g.current.setQuickFilterValues(z)},s),[g,s,r]);a.useEffect(()=>O.clear,[O]);const G=a.useCallback(P=>{const z=P.target.value;p(z),O(z)},[O]),A=a.useCallback(()=>{var P;p(""),g.current.setQuickFilterValues([]),(P=h.current)==null||P.focus()},[g,h]),F=a.useMemo(()=>({controlRef:h,triggerRef:m,state:H,controlId:L,clearValue:A,onValueChange:G,onExpandedChange:x}),[L,H,G,A,x]);nt(()=>{var P;$.current&&m.current&&$.current.style.setProperty("--trigger-width",`${(P=m.current)==null?void 0:P.offsetWidth}px`)},[]);const E=Ht("div",n,f({className:k},u,{ref:$}),H);return C.jsx(Yi.Provider,{value:F,children:E})}const kb=["render","className","slotProps","onKeyDown","onChange"],Tb=ue(function(t,n){var $;const{render:o,className:r,slotProps:l,onKeyDown:s,onChange:i}=t,c=Q(t,kb),d=Z(),{state:u,controlId:g,controlRef:h,onValueChange:m,onExpandedChange:S,clearValue:y}=Br(),p=typeof r=="function"?r(u):r,v=at(h,n),I=L=>{L.key==="Escape"&&(u.value===""?S(!1):y()),s==null||s(L)},D=L=>{var x,w;u.value===""&&S(!1),(w=(x=l==null?void 0:l.htmlInput)==null?void 0:x.onBlur)==null||w.call(x,L)},H=L=>{u.expanded||S(!0),m(L),i==null||i(L)},k=Ht(d.slots.baseTextField,o,f({},($=d.slotProps)==null?void 0:$.baseTextField,{slotProps:f({htmlInput:f({role:"searchbox",id:g,tabIndex:u.expanded?void 0:-1},l==null?void 0:l.htmlInput,{onBlur:D})},l),value:u.value,className:p},c,{onChange:H,onKeyDown:I,ref:v}),u);return C.jsx(a.Fragment,{children:k})}),Db=["render","className","onClick"],Hb=ue(function(t,n){var m;const{render:o,className:r,onClick:l}=t,s=Q(t,Db),i=Z(),{state:c,clearValue:d}=Br(),u=typeof r=="function"?r(c):r,g=S=>{d(),l==null||l(S)},h=Ht(i.slots.baseIconButton,o,f({},(m=i.slotProps)==null?void 0:m.baseIconButton,{className:u,tabIndex:-1},s,{onClick:g,ref:n}),c);return C.jsx(a.Fragment,{children:h})}),Ob=["render","className","onClick"],Gb=ue(function(t,n){var p;const{render:o,className:r,onClick:l}=t,s=Q(t,Ob),i=Z(),{state:c,controlId:d,onExpandedChange:u,triggerRef:g}=Br(),h=typeof r=="function"?r(c):r,m=at(g,n),S=v=>{u(!c.expanded),l==null||l(v)},y=Ht(i.slots.baseButton,o,f({},(p=i.slotProps)==null?void 0:p.baseButton,{className:h,"aria-controls":d,"aria-expanded":c.expanded},s,{onClick:S,ref:m}),c);return C.jsx(a.Fragment,{children:y})}),Lb=["quickFilterParser","quickFilterFormatter","debounceMs","className","slotProps"],$b=["ref","slotProps"],Ab=e=>{const{classes:t}=e;return xe({root:["toolbarQuickFilter"],trigger:["toolbarQuickFilterTrigger"],control:["toolbarQuickFilterControl"]},ye,t)},Rb=ke("div",{name:"MuiDataGrid",slot:"ToolbarQuickFilter"})({display:"grid",alignItems:"center"}),zb=ke(go,{name:"MuiDataGrid",slot:"ToolbarQuickFilterTrigger"})(({ownerState:e})=>({gridArea:"1 / 1",width:"min-content",height:"min-content",zIndex:1,opacity:e.expanded?0:1,pointerEvents:e.expanded?"none":"auto",transition:ee.transition(["opacity"])})),Vb=ke(e=>{throw new Error("Failed assertion: should not be rendered")},{name:"MuiDataGrid",slot:"ToolbarQuickFilterControl"})(({ownerState:e})=>({gridArea:"1 / 1",overflowX:"clip",width:e.expanded?260:"var(--trigger-width)",opacity:e.expanded?1:0,transition:ee.transition(["width","opacity"])}));function Nb(e){const t=me(),n=Z(),o={classes:n.classes,expanded:!1},r=Ab(o),{quickFilterParser:l,quickFilterFormatter:s,debounceMs:i,className:c,slotProps:d}=e,u=Q(e,Lb);return C.jsx(Eb,{parser:l,formatter:s,debounceMs:i,render:(g,h)=>{const m=f({},o,{expanded:h.expanded});return C.jsxs(Rb,f({},g,{className:Fe(r.root,c),children:[C.jsx(Gb,{render:S=>C.jsx(n.slots.baseTooltip,{title:t.current.getLocaleText("toolbarQuickFilterLabel"),enterDelay:0,children:C.jsx(zb,f({className:r.trigger},S,{ownerState:m,color:"default","aria-disabled":h.expanded,children:C.jsx(n.slots.quickFilterIcon,{fontSize:"small"})}))})}),C.jsx(Tb,{render:S=>{var I;let{ref:y,slotProps:p}=S,v=Q(S,$b);return C.jsx(Vb,f({as:n.slots.baseTextField,className:r.control,ownerState:m,inputRef:y,"aria-label":t.current.getLocaleText("toolbarQuickFilterLabel"),placeholder:t.current.getLocaleText("toolbarQuickFilterPlaceholder"),size:"small",slotProps:f({input:f({startAdornment:C.jsx(n.slots.quickFilterIcon,{fontSize:"small"}),endAdornment:v.value?C.jsx(Hb,{render:C.jsx(n.slots.baseIconButton,{size:"small",edge:"end","aria-label":t.current.getLocaleText("toolbarQuickFilterDeleteIconLabel"),children:C.jsx(n.slots.quickFilterClearIcon,{fontSize:"small"})})}):null},p==null?void 0:p.input)},p)},(I=n.slotProps)==null?void 0:I.baseTextField,v,d==null?void 0:d.root,u))}})]}))}})}const Bb=["render","className","onClick","onPointerUp"],jb=ue(function(t,n){var L;const{render:o,className:r,onClick:l,onPointerUp:s}=t,i=Q(t,Bb),c=Z(),d=Ae(),u=Ae(),g=me(),h=W(g,tn),m=h.open&&h.openedPanelValue===lt.filters,y=W(g,Dr).length,p={open:m,filterCount:y},v=typeof r=="function"?r(p):r,{filterPanelTriggerRef:I}=Vr(),D=at(n,I),H=x=>{m?g.current.hidePreferences():g.current.showPreferences(lt.filters,u,d),l==null||l(x)},k=x=>{m&&x.stopPropagation(),s==null||s(x)},$=Ht(c.slots.baseButton,o,f({},(L=c.slotProps)==null?void 0:L.baseButton,{id:d,"aria-haspopup":"true","aria-expanded":m?"true":void 0,"aria-controls":m?u:void 0,onClick:H,onPointerUp:k,className:v},i,{ref:D}),p);return C.jsx(a.Fragment,{children:$})}),_b=["render","className","onClick","onPointerUp"],Wb=ue(function(t,n){var k;const{render:o,className:r,onClick:l,onPointerUp:s}=t,i=Q(t,_b),c=Z(),d=Ae(),u=Ae(),g=me(),h=W(g,tn),m=h.open&&h.openedPanelValue===lt.columns,S={open:m},y=typeof r=="function"?r(S):r,{columnsPanelTriggerRef:p}=Vr(),v=at(n,p),I=$=>{m?g.current.hidePreferences():g.current.showPreferences(lt.columns,u,d),l==null||l($)},D=$=>{m&&$.stopPropagation(),s==null||s($)},H=Ht(c.slots.baseButton,o,f({},(k=c.slotProps)==null?void 0:k.baseButton,{id:d,"aria-haspopup":"true","aria-expanded":m?"true":void 0,"aria-controls":m?u:void 0,className:y},i,{onPointerUp:D,onClick:I,ref:v}),S);return C.jsx(a.Fragment,{children:H})}),Ub=["render","options","onClick"],Kb=ue(function(t,n){var g;const{render:o,options:r,onClick:l}=t,s=Q(t,Ub),i=Z(),c=me(),d=h=>{c.current.exportDataAsCsv(r),l==null||l(h)},u=Ht(i.slots.baseButton,o,f({},(g=i.slotProps)==null?void 0:g.baseButton,{onClick:d},s,{ref:n}));return C.jsx(a.Fragment,{children:u})}),qb=["render","options","onClick"],Xb=ue(function(t,n){var g;const{render:o,options:r,onClick:l}=t,s=Q(t,qb),i=Z(),c=me(),d=h=>{c.current.exportDataAsPrint(r),l==null||l(h)},u=Ht(i.slots.baseButton,o,f({},(g=i.slotProps)==null?void 0:g.baseButton,{onClick:d},s,{ref:n}));return C.jsx(a.Fragment,{children:u})}),Qb=["className"],Yb=["className"],Zi=e=>{const{classes:t}=e;return xe({divider:["toolbarDivider"],label:["toolbarLabel"]},ye,t)},Zb=ke($t,{name:"MuiDataGrid",slot:"ToolbarDivider"})({height:"50%",margin:ee.spacing(0,.5)}),Jb=ke("span",{name:"MuiDataGrid",slot:"ToolbarLabel"})({flex:1,font:ee.typography.font.large,fontWeight:ee.typography.fontWeight.medium,margin:ee.spacing(0,.5),textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"});function Xl(e){const t=Q(e,Qb),n=Z(),o=Zi(n);return C.jsx(Zb,f({as:n.slots.baseDivider,orientation:"vertical",className:o.divider},t))}function ep(e){const t=Q(e,Yb),n=Z(),o=Zi(n);return C.jsx(Jb,f({className:o.label},t))}function tp(e){var p,v,I;const{showQuickFilter:t=!0,quickFilterProps:n,csvOptions:o,printOptions:r,additionalItems:l,additionalExportMenuItems:s}=e,i=me(),c=Z(),[d,u]=a.useState(!1),g=a.useRef(null),h=Ae(),m=Ae(),S=!(o!=null&&o.disableToolbarButton)||!(r!=null&&r.disableToolbarButton)||s,y=()=>u(!1);return C.jsxs(pm,{children:[c.label&&C.jsx(ep,{children:c.label}),!c.disableColumnSelector&&C.jsx(c.slots.baseTooltip,{title:i.current.getLocaleText("toolbarColumns"),children:C.jsx(Wb,{render:C.jsx(go,{}),children:C.jsx(c.slots.columnSelectorIcon,{fontSize:"small"})})}),!c.disableColumnFilter&&C.jsx(c.slots.baseTooltip,{title:i.current.getLocaleText("toolbarFilters"),children:C.jsx(jb,{render:(D,H)=>C.jsx(go,f({},D,{color:H.filterCount>0?"primary":"default",children:C.jsx(c.slots.baseBadge,{badgeContent:H.filterCount,color:"primary",variant:"dot",children:C.jsx(c.slots.openFilterButtonIcon,{fontSize:"small"})})}))})}),l,S&&(!c.disableColumnFilter||!c.disableColumnSelector)&&C.jsx(Xl,{}),S&&C.jsxs(a.Fragment,{children:[C.jsx(c.slots.baseTooltip,{title:i.current.getLocaleText("toolbarExport"),children:C.jsx(go,{ref:g,id:m,"aria-controls":h,"aria-haspopup":"true","aria-expanded":d?"true":void 0,onClick:()=>u(!d),children:C.jsx(c.slots.exportIcon,{fontSize:"small"})})}),C.jsx(Fo,{target:g.current,open:d,onClose:y,position:"bottom-end",children:C.jsxs(c.slots.baseMenuList,f({id:h,"aria-labelledby":m,autoFocusItem:!0},(p=c.slotProps)==null?void 0:p.baseMenuList,{children:[!(r!=null&&r.disableToolbarButton)&&C.jsx(Xb,{render:C.jsx(c.slots.baseMenuItem,f({},(v=c.slotProps)==null?void 0:v.baseMenuItem)),options:r,onClick:y,children:i.current.getLocaleText("toolbarExportPrint")}),!(o!=null&&o.disableToolbarButton)&&C.jsx(Kb,{render:C.jsx(c.slots.baseMenuItem,f({},(I=c.slotProps)==null?void 0:I.baseMenuItem)),options:o,onClick:y,children:i.current.getLocaleText("toolbarExportCSV")}),s==null?void 0:s(y)]}))})]}),t&&C.jsxs(a.Fragment,{children:[C.jsx(Xl,{}),C.jsx(Nb,f({},n))]})]})}const np=["className","selectedRowCount"],op=e=>{const{classes:t}=e;return xe({root:["selectedRowCount"]},ye,t)},rp=ke("div",{name:"MuiDataGrid",slot:"SelectedRowCount"})({alignItems:"center",display:"flex",margin:ee.spacing(0,2),visibility:"hidden",width:0,height:0,[ee.breakpoints.up("sm")]:{visibility:"visible",width:"auto",height:"auto"}}),lp=ue(function(t,n){const{className:o,selectedRowCount:r}=t,l=Q(t,np),s=me(),i=Z(),c=op(i),d=s.current.getLocaleText("footerRowSelected")(r);return C.jsx(rp,f({className:Fe(c.root,o),ownerState:i},l,{ref:n,children:d}))}),sp=ue(function(t,n){var g;const o=me(),r=Z(),l=W(o,ld),s=W(o,Oo),i=W(o,Tr),c=!r.hideFooterSelectedRowCount&&s>0?C.jsx(lp,{selectedRowCount:s}):C.jsx("div",{}),d=!r.hideFooterRowCount&&!r.pagination?C.jsx(r.slots.footerRowCount,f({},(g=r.slotProps)==null?void 0:g.footerRowCount,{rowCount:l,visibleRowCount:i})):null,u=r.pagination&&!r.hideFooterPagination&&r.slots.pagination&&C.jsx(r.slots.pagination,{});return C.jsxs(cm,f({},t,{ref:n,children:[c,d,u]}))}),$n=(e,t,n,o,r,l)=>{let s;switch(e){case ve.LEFT:s=o[n];break;case ve.RIGHT:s=r-o[n]-t+l;break;default:s=void 0;break}return s},yo=(e,t,n,o,r)=>{const l=t===n-1;return e===ve.LEFT&&l?!0:o?e===ve.LEFT?!0:e===ve.RIGHT?!l:!l||r:!1},vo=(e,t)=>e===ve.RIGHT&&t===0,Hn={root:b.scrollbarFiller,header:b["scrollbarFiller--header"],borderTop:b["scrollbarFiller--borderTop"],borderBottom:b["scrollbarFiller--borderBottom"],pinnedRight:b["scrollbarFiller--pinnedRight"]};function jr({header:e,borderTop:t=!0,borderBottom:n,pinnedRight:o}){return C.jsx("div",{role:"presentation",className:Fe(Hn.root,e&&Hn.header,t&&Hn.borderTop,n&&Hn.borderBottom,o&&Hn.pinnedRight)})}const ip=["skeletonRowsCount","visibleColumns","showFirstRowBorder"],cp=ke("div",{name:"MuiDataGrid",slot:"SkeletonLoadingOverlay"})({minWidth:"100%",width:"max-content",height:"100%",overflow:"clip"}),ap=e=>{const{classes:t}=e;return xe({root:["skeletonLoadingOverlay"]},ye,t)},Ql=e=>parseInt(e.getAttribute("data-colindex"),10),up=ue(function(t,n){const o=Z(),{slots:r}=o,l=Dt(),s=ap({classes:o.classes}),i=a.useRef(null),c=at(i,n),d=me(),u=W(d,Re),g=W(d,Mo),h=W(d,qt),m=a.useMemo(()=>h.filter(x=>x<=g).length,[g,h]),{skeletonRowsCount:S,visibleColumns:y,showFirstRowBorder:p}=t,v=Q(t,ip),I=W(d,We),D=a.useMemo(()=>I.slice(0,m),[I,m]),H=W(d,Mn),k=a.useCallback(x=>{if(H.left.findIndex(w=>w.field===x)!==-1)return ve.LEFT;if(H.right.findIndex(w=>w.field===x)!==-1)return ve.RIGHT},[H.left,H.right]),$=a.useMemo(()=>{const x=[];for(let w=0;w<S;w+=1){const T=[];for(let M=0;M<D.length;M+=1){const O=D[M],G=k(O.field),A=G===ve.LEFT,F=G===ve.RIGHT,E=Ai(G,l),P=E?H[E].length:D.length-H.left.length-H.right.length,z=E?H[E].findIndex(ne=>ne.field===O.field):M-H.left.length,_=u.hasScrollY?u.scrollbarSize:0,V=Ro({},l,G,$n(G,O.computedWidth,M,h,u.columnsTotalWidth,_)),R=u.columnsTotalWidth<u.viewportOuterSize.width,B=yo(G,z,P,o.showCellVerticalBorder,R),q=vo(G,z),X=M===D.length-1,re=F&&z===0,Y=re&&R,ae=X&&!re&&R,ce=u.viewportOuterSize.width-u.columnsTotalWidth,j=Math.max(0,ce),K=C.jsx(r.skeletonCell,{width:j,empty:!0},`skeleton-filler-column-${w}`),te=X&&_!==0;Y&&T.push(K),T.push(C.jsx(r.skeletonCell,{field:O.field,type:O.type,align:O.align,width:"var(--width)",height:u.rowHeight,"data-colindex":M,empty:y&&!y.has(O.field),className:Fe(A&&b["cell--pinnedLeft"],F&&b["cell--pinnedRight"],B&&b["cell--withRightBorder"],q&&b["cell--withLeftBorder"]),style:f({"--width":`${O.computedWidth}px`},V)},`skeleton-column-${w}-${O.field}`)),ae&&T.push(K),te&&T.push(C.jsx(jr,{pinnedRight:H.right.length>0},`skeleton-scrollbar-filler-${w}`))}x.push(C.jsx("div",{className:Fe(b.row,b.rowSkeleton,w===0&&!p&&b["row--firstVisible"]),children:T},`skeleton-row-${w}`))}return x},[S,D,k,l,H,u.hasScrollY,u.scrollbarSize,u.columnsTotalWidth,u.viewportOuterSize.width,u.rowHeight,h,o.showCellVerticalBorder,r,y,p]);return le(d,"columnResize",x=>{var z,_,V;const{colDef:w,width:T}=x,M=(z=i.current)==null?void 0:z.querySelectorAll(`[data-field="${Rt(w.field)}"]`);if(!M)throw new Error("MUI X: Expected skeleton cells to be defined with `data-field` attribute.");const O=D.findIndex(R=>R.field===w.field),G=k(w.field),A=G===ve.LEFT,F=G===ve.RIGHT,E=getComputedStyle(M[0]).getPropertyValue("--width"),P=parseInt(E,10)-T;if(M&&M.forEach(R=>{R.style.setProperty("--width",`${T}px`)}),A){const R=(_=i.current)==null?void 0:_.querySelectorAll(`.${b["cell--pinnedLeft"]}`);R==null||R.forEach(B=>{Ql(B)>O&&(B.style.left=`${parseInt(getComputedStyle(B).left,10)-P}px`)})}if(F){const R=(V=i.current)==null?void 0:V.querySelectorAll(`.${b["cell--pinnedRight"]}`);R==null||R.forEach(B=>{Ql(B)<O&&(B.style.right=`${parseInt(getComputedStyle(B).right,10)+P}px`)})}}),C.jsx(cp,f({className:s.root},v,{ref:c,children:$}))}),dp=ue(function(t,n){const o=me(),r=W(o,Re),l=(r==null?void 0:r.viewportInnerSize.height)??0,s=Math.ceil(l/r.rowHeight);return C.jsx(up,f({},t,{skeletonRowsCount:s,ref:n}))}),gp=["variant","noRowsVariant","style"],fp={"circular-progress":{component:e=>e.slots.baseCircularProgress,style:{}},"linear-progress":{component:e=>e.slots.baseLinearProgress,style:{display:"block"}},skeleton:{component:()=>dp,style:{display:"block"}}},hp=ue(function(t,n){const{variant:o="linear-progress",noRowsVariant:r="skeleton",style:l}=t,s=Q(t,gp),i=me(),c=Z(),d=W(i,_n),u=fp[d===0?r:o],g=u.component(c);return C.jsx(zo,f({style:f({},u.style,l)},s,{ref:n,children:C.jsx(g,{})}))}),mp=ue(function(t,n){const r=me().current.getLocaleText("noRowsLabel");return C.jsx(zo,f({},t,{ref:n,children:r}))}),Cp=ue(function(t,n){var c;const o=Z(),r=me(),l=W(r,kt),s=()=>{r.current.showPreferences(lt.columns)},i=!o.disableColumnSelector&&l.length>0;return C.jsxs(zo,f({},t,{ref:n,children:[r.current.getLocaleText("noColumnsOverlayLabel"),i&&C.jsx(o.slots.baseButton,f({size:"small"},(c=o.slotProps)==null?void 0:c.baseButton,{onClick:s,children:r.current.getLocaleText("noColumnsOverlayManageColumns")}))]}))}),bp=Ne($t)({maxHeight:"calc(100% + 1px)",flexGrow:1});function pp(){const e=me(),t=Z(),n=W(e,Je),o=W(e,gn),r=W(e,pi),{paginationMode:l,loading:s}=t,i=o===-1&&l==="server"&&s,c=a.useMemo(()=>Math.max(0,r-1),[r]),d=a.useMemo(()=>o===-1||n.page<=c?n.page:c,[c,n.page,o]),u=a.useCallback(S=>{e.current.setPageSize(S)},[e]),g=a.useCallback((S,y)=>{e.current.setPage(y)},[e]),m=(S=>{for(let y=0;y<t.pageSizeOptions.length;y+=1){const p=t.pageSizeOptions[y];if(typeof p=="number"){if(p===S)return!0}else if(p.value===S)return!0}return!1})(n.pageSize)?t.pageSizeOptions:[];return C.jsx(bp,{as:t.slots.basePagination,count:o,page:d,rowsPerPageOptions:m,rowsPerPage:n.pageSize,onPageChange:g,onRowsPerPageChange:u,disabled:i})}const wp=["className","rowCount","visibleRowCount"],Sp=e=>{const{classes:t}=e;return xe({root:["rowCount"]},ye,t)},xp=ke("div",{name:"MuiDataGrid",slot:"RowCount"})({alignItems:"center",display:"flex",margin:ee.spacing(0,2)}),yp=ue(function(t,n){const{className:o,rowCount:r,visibleRowCount:l}=t,s=Q(t,wp),i=me(),c=Z(),d=Sp(c);if(r===0)return null;const u=l<r?i.current.getLocaleText("footerTotalVisibleRows")(l,r):r.toLocaleString();return C.jsxs(xp,f({className:Fe(d.root,o),ownerState:c},s,{ref:n,children:[i.current.getLocaleText("footerTotalRows")," ",u]}))});function vp(e,t){return xe(t,ye,e)}const Ip=["selected","rowId","row","index","style","rowHeight","className","visibleColumns","pinnedColumns","offsetLeft","columnsTotalWidth","firstColumnIndex","lastColumnIndex","focusedColumnIndex","isFirstVisible","isLastVisible","isNotVisible","showBottomBorder","scrollbarWidth","gridHasFiller","onClick","onDoubleClick","onMouseEnter","onMouseLeave","onMouseOut","onMouseOver"],Mp=oe(Ye,(e,t)=>t?!!Do(e):!1),Pp=ue(function(t,n){var Ie;const{selected:o,rowId:r,row:l,index:s,style:i,rowHeight:c,className:d,visibleColumns:u,pinnedColumns:g,offsetLeft:h,columnsTotalWidth:m,firstColumnIndex:S,lastColumnIndex:y,focusedColumnIndex:p,isFirstVisible:v,isLastVisible:I,isNotVisible:D,showBottomBorder:H,scrollbarWidth:k,gridHasFiller:$,onClick:L,onDoubleClick:x,onMouseEnter:w,onMouseLeave:T,onMouseOut:M,onMouseOver:O}=t,G=Q(t,Ip),A=ut(),F=Po(),E=a.useRef(null),P=Z(),z=Ar(A),_=W(A,ht),V=W(A,yn),R=W(A,qt),B=P.rowReordering,q=W(A,Mp,B),X=at(E,n),re=Et(A,r),Y=W(A,Li,{rowId:r,editMode:P.editMode}),ae=P.editMode===Tt.Row,ce=p!==void 0,j=ce&&p>=g.left.length&&p<S,K=ce&&p<u.length-g.right.length&&p>=y,te=vp(P.classes,{root:["row",o&&"selected",ae&&"row--editable",Y&&"row--editing",v&&"row--firstVisible",I&&"row--lastVisible",H&&"row--borderBottom",c==="auto"&&"row--dynamicHeight"]}),ne=F.hooks.useGridRowAriaAttributes();a.useLayoutEffect(()=>{if(z.range){const de=A.current.getRowIndexRelativeToVisibleRows(r);de!==void 0&&A.current.unstable_setLastMeasuredRowIndex(de)}if(E.current&&c==="auto")return A.current.observeRowHeight(E.current,r)},[A,z.range,c,r]);const U=a.useCallback((de,He)=>Ee=>{eo(Ee)||A.current.getRow(r)&&(A.current.publishEvent(de,A.current.getRowParams(r),Ee),He&&He(Ee))},[A,r]),J=a.useCallback(de=>{const He=So(de.target,b.cell),Ee=He==null?void 0:He.getAttribute("data-field");if(Ee){if(Ee===Fn.field||Ee===To||Ee==="__reorder__"||A.current.getCellMode(r,Ee)===Oe.Edit)return;const ot=A.current.getColumn(Ee);if((ot==null?void 0:ot.type)===Eo)return}U("rowClick",L)(de)},[A,L,U,r]),{slots:ie,slotProps:fe,disableColumnReorder:he}=P,ge=W(A,()=>f({},A.current.getRowHeightEntry(r)),void 0,Gs),be=a.useMemo(()=>{if(D)return{opacity:0,width:0,height:0};const de=f({},i,{maxHeight:c==="auto"?"none":c,minHeight:c,"--height":typeof c=="number"?`${c}px`:c});if(ge.spacingTop){const He=P.rowSpacingType==="border"?"borderTopWidth":"marginTop";de[He]=ge.spacingTop}if(ge.spacingBottom){const He=P.rowSpacingType==="border"?"borderBottomWidth":"marginBottom";let Ee=de[He];typeof Ee!="number"&&(Ee=parseInt(Ee||"0",10)),Ee+=ge.spacingBottom,de[He]=Ee}return de},[D,c,i,ge,P.rowSpacingType]),Me=A.current.unstable_applyPipeProcessors("rowClassName",[],r),De=ne(re,s);if(typeof P.getRowClassName=="function"){const de=s-(((Ie=z.range)==null?void 0:Ie.firstRowIndex)||0),He=f({},A.current.getRowParams(r),{isFirstVisible:de===0,isLastVisible:de===z.rows.length-1,indexRelativeToCurrentPage:de});Me.push(P.getRowClassName(He))}const se=(de,He,Ee,ot,dt=ve.NONE)=>{const pe=A.current.unstable_getCellColSpanInfo(r,Ee);if(pe!=null&&pe.spannedByColSpan)return null;const Ue=(pe==null?void 0:pe.cellProps.width)??de.computedWidth,Ke=(pe==null?void 0:pe.cellProps.colSpan)??1,gt=$n(dt,de.computedWidth,Ee,R,m,k);if(re.type==="skeletonRow")return C.jsx(ie.skeletonCell,{type:de.type,width:Ue,height:c,field:de.field,align:de.align},de.field);const to=de.field==="__reorder__",Vo=!(he||de.disableReorder),No=q&&!_.length&&V<=1,Bo=!(Vo||to&&No),jo=dt===ve.VIRTUAL,_o=vo(dt,He),no=yo(dt,He,ot,P.showCellVerticalBorder,$);return C.jsx(ie.cell,f({column:de,width:Ue,rowId:r,align:de.align||"left",colIndex:Ee,colSpan:Ke,disableDragEvents:Bo,isNotVisible:jo,pinnedOffset:gt,pinnedPosition:dt,showLeftBorder:_o,showRightBorder:no,row:l,rowNode:re},fe==null?void 0:fe.cell),de.field)},Ce=g.left.map((de,He)=>se(de,He,He,g.left.length,ve.LEFT)),Se=g.right.map((de,He)=>{const Ee=u.length-g.right.length+He;return se(de,He,Ee,g.right.length,ve.RIGHT)}),Pe=u.length-g.left.length-g.right.length,Ge=[];j&&Ge.push(se(u[p],p-g.left.length,p,Pe,ve.VIRTUAL));for(let de=S;de<y;de+=1){const He=u[de],Ee=de-g.left.length;He&&Ge.push(se(He,Ee,de,Pe))}K&&Ge.push(se(u[p],p-g.left.length,p,Pe,ve.VIRTUAL));const ze=l?{onClick:J,onDoubleClick:U("rowDoubleClick",x),onMouseEnter:U("rowMouseEnter",w),onMouseLeave:U("rowMouseLeave",T),onMouseOut:U("rowMouseOut",M),onMouseOver:U("rowMouseOver",O)}:null;return C.jsxs("div",f({"data-id":r,"data-rowindex":s,role:"row",className:Fe(...Me,te.root,d),style:be},De,ze,G,{ref:X,children:[Ce,C.jsx("div",{role:"presentation",className:b.cellOffsetLeft,style:{width:h}}),Ge,C.jsx("div",{role:"presentation",className:Fe(b.cell,b.cellEmpty)}),Se,k!==0&&C.jsx(jr,{pinnedRight:g.right.length>0,borderTop:!v})]}))}),Fp=Lt(Pp),Ep=()=>{const e=ut(),t=Z(),n=W(e,We),o=W(e,kr),r=W(e,Qn),l=W(e,Ls),s=t["aria-label"],i=t["aria-labelledby"];return{role:"grid","aria-label":!s&&!i&&t.label?t.label:s,"aria-labelledby":i,"aria-colcount":n.length,"aria-rowcount":r+1+l+o,"aria-multiselectable":Hr(t)}},kp=()=>{const e=ut(),t=W(e,Qn);return a.useCallback((n,o)=>{const r={},l=o+t+2;return r["aria-rowindex"]=l,e.current.isRowSelectable(n.id)&&(r["aria-selected"]=e.current.isRowSelected(n.id)),r},[e,t])};function Tp({privateApiRef:e,configuration:t,props:n,children:o}){const r=a.useRef(e.current.getPublicApi());return C.jsx(Qs.Provider,{value:t,children:C.jsx(Nc.Provider,{value:n,children:C.jsx(Pi.Provider,{value:e,children:C.jsx(Bc.Provider,{value:r,children:C.jsx(Zh,{children:C.jsx(Ad,{children:o})})})})})})}const Dp=e=>{const t=a.useRef(null),n=a.useRef(null),o=a.useRef(null),r=a.useRef(null),l=a.useRef(null),s=a.useRef(null);e.current.register("public",{rootElementRef:t}),e.current.register("private",{mainElementRef:n,virtualScrollerRef:o,virtualScrollbarVerticalRef:r,virtualScrollbarHorizontalRef:l,columnHeadersContainerRef:s})},Hp=e=>{const t=Dt();e.current.state.isRtl===void 0&&(e.current.state.isRtl=t);const n=a.useRef(!0);a.useEffect(()=>{n.current?n.current=!1:e.current.setState(o=>f({},o,{isRtl:t}))},[e,t])},Op=wd()&&window.localStorage.getItem("DEBUG")!=null,Gn=()=>{},Gp={debug:Gn,info:Gn,warn:Gn,error:Gn},Yl=["debug","info","warn","error"];function Zl(e,t,n=console){const o=Yl.indexOf(t);if(o===-1)throw new Error(`MUI X: Log level ${t} not recognized.`);return Yl.reduce((l,s,i)=>(i>=o?l[s]=(...c)=>{const[d,...u]=c;n[s](`MUI X: ${e} - ${d}`,...u)}:l[s]=Gn,l),{})}const Lp=(e,t)=>{const n=a.useCallback(o=>Op?Zl(o,"debug",t.logger):t.logLevel?Zl(o,t.logLevel.toString(),t.logger):Gp,[t.logLevel,t.logger]);we(e,{getLogger:n},"private")},$p=(e,t)=>{const n=a.useCallback(o=>{if(t.localeText[o]==null)throw new Error(`Missing translation for key ${o}.`);return t.localeText[o]},[t.localeText]);e.current.register("public",{getLocaleText:n})};function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}function Ap(e,t){if(Vn(e)!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var o=n.call(e,t);if(Vn(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Nn(e){var t=Ap(e,"string");return Vn(t)=="symbol"?t:t+""}const Rp=e=>{const t=a.useRef({}),n=a.useRef(!1),o=a.useCallback(u=>{n.current||!u||(n.current=!0,Object.values(u.appliers).forEach(g=>{g()}),n.current=!1)},[]),r=a.useCallback((u,g,h)=>{t.current[u]||(t.current[u]={processors:new Map,processorsAsArray:[],appliers:{}});const m=t.current[u];return m.processors.get(g)!==h&&(m.processors.set(g,h),m.processorsAsArray=Array.from(t.current[u].processors.values()).filter(y=>y!==null),o(m)),()=>{t.current[u].processors.set(g,null),t.current[u].processorsAsArray=Array.from(t.current[u].processors.values()).filter(y=>y!==null)}},[o]),l=a.useCallback((u,g,h)=>(t.current[u]||(t.current[u]={processors:new Map,processorsAsArray:[],appliers:{}}),t.current[u].appliers[g]=h,()=>{const m=t.current[u].appliers,S=Q(m,[g].map(Nn));t.current[u].appliers=S}),[]),s=a.useCallback(u=>{o(t.current[u])},[o]),i=a.useCallback((...u)=>{const[g,h,m]=u;if(!t.current[g])return h;const S=t.current[g].processorsAsArray;let y=h;for(let p=0;p<S.length;p+=1)y=S[p](y,m);return y},[]),c={registerPipeProcessor:r,registerPipeApplier:l,requestPipeProcessorsApplication:s},d={unstable_applyPipeProcessors:i};we(e,c,"private"),we(e,d,"public")},_e=(e,t,n,o=!0)=>{const r=a.useRef(null),l=a.useRef(`mui-${Math.round(Math.random()*1e9)}`),s=a.useCallback(()=>{r.current=e.current.registerPipeProcessor(t,l.current,n)},[e,n,t]);Yn(()=>{o&&s()});const i=a.useRef(!0);a.useEffect(()=>(i.current?i.current=!1:o&&s(),()=>{r.current&&(r.current(),r.current=null)}),[s,o])},_r=(e,t,n)=>{const o=a.useRef(null),r=a.useRef(`mui-${Math.round(Math.random()*1e9)}`),l=a.useCallback(()=>{o.current=e.current.registerPipeApplier(t,r.current,n)},[e,n,t]);Yn(()=>{l()});const s=a.useRef(!0);a.useEffect(()=>(s.current?s.current=!1:l(),()=>{o.current&&(o.current(),o.current=null)}),[l])};let Wt=function(e){return e.DataSource="dataSource",e.RowTree="rowTree",e}({});const Bn=(e,t,n,o)=>{const r=a.useCallback(()=>{e.current.registerStrategyProcessor(t,n,o)},[e,o,n,t]);Yn(()=>{r()});const l=a.useRef(!0);a.useEffect(()=>{l.current?l.current=!1:r()},[r])},nn="none",Jl={dataSourceRowsUpdate:Wt.DataSource,rowTreeCreation:Wt.RowTree,filtering:Wt.RowTree,sorting:Wt.RowTree,visibleRowsLookupCreation:Wt.RowTree},zp=e=>{const t=a.useRef(new Map),n=a.useRef({}),o=a.useCallback((c,d,u)=>{const g=()=>{const S=n.current[d],y=Q(S,[c].map(Nn));n.current[d]=y};n.current[d]||(n.current[d]={});const h=n.current[d],m=h[c];return h[c]=u,!m||m===u||c===e.current.getActiveStrategy(Jl[d])&&e.current.publishEvent("activeStrategyProcessorChange",d),g},[e]),r=a.useCallback((c,d)=>{const u=e.current.getActiveStrategy(Jl[c]);if(u==null)throw new Error("Can't apply a strategy processor before defining an active strategy");const g=n.current[c];if(!g||!g[u])throw new Error(`No processor found for processor "${c}" on strategy "${u}"`);const h=g[u];return h(d)},[e]),l=a.useCallback(c=>{const u=Array.from(t.current.entries()).find(([,g])=>g.group!==c?!1:g.isAvailable());return(u==null?void 0:u[0])??nn},[]),s=a.useCallback((c,d,u)=>{t.current.set(d,{group:c,isAvailable:u}),e.current.publishEvent("strategyAvailabilityChange")},[e]);we(e,{registerStrategyProcessor:o,applyStrategyProcessor:r,getActiveStrategy:l,setStrategyAvailability:s},"private")},Vp=e=>{const t=a.useRef({}),n=a.useCallback(i=>{t.current[i.stateId]=i},[]),o=a.useCallback((i,c)=>{let d;if(pd(i)?d=i(e.current.state):d=i,e.current.state===d)return!1;const u={current:{state:d}};let g=!1;const h=[];if(Object.keys(t.current).forEach(m=>{const S=t.current[m],y=S.stateSelector(e),p=S.stateSelector(u);p!==y&&(h.push({stateId:S.stateId,hasPropChanged:p!==S.propModel}),S.propModel!==void 0&&p!==S.propModel&&(g=!0))}),h.length>1)throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${h[0].stateId}, therefore, you're not allowed to update ${h.map(m=>m.stateId).join(", ")} in the same transaction.`);if(g||(e.current.state=d,e.current.publishEvent("stateChange",d),e.current.store.update(d)),h.length===1){const{stateId:m,hasPropChanged:S}=h[0],y=t.current[m],p=y.stateSelector(u);y.propOnChange&&S&&y.propOnChange(p,{reason:c,api:e.current}),g||e.current.publishEvent(y.changeEvent,p,{reason:c})}return!g},[e]),r=a.useCallback((i,c,d)=>e.current.setState(u=>f({},u,{[i]:c(u[i])}),d),[e]),l={setState:o},s={updateControlState:r,registerControlState:n};we(e,l,"public"),we(e,s,"private")},Np=(e,t)=>f({},e,{props:{getRowId:t.getRowId}}),Bp=(e,t)=>{a.useEffect(()=>{e.current.setState(n=>f({},n,{props:{getRowId:t.getRowId}}))},[e,t.getRowId])},jp=(e,t)=>{Dp(e),Bp(e,t),Hp(e),Lp(e,t),Vp(e),Rp(e),zp(e),$p(e,t),e.current.register("private",{rootProps:t})},Xe=(e,t,n)=>{const o=a.useRef(!1);o.current||(t.current.state=e(t.current.state,n,t),o.current=!0)};function ur(e,t){if(e==null)return"";const n=typeof e=="string"?e:`${e}`;if(t.shouldAppendQuotes||t.escapeFormulas){const o=n.replace(/"/g,'""');return t.escapeFormulas&&["=","+","-","@","	","\r"].includes(o[0])?`"'${o}"`:[t.delimiter,`
`,"\r",'"'].some(r=>n.includes(r))?`"${o}"`:o}return n}const Ji=(e,t)=>{var l,s;const{csvOptions:n,ignoreValueFormatter:o}=t;let r;if(o){const i=e.colDef.type;i==="number"?r=String(e.value):i==="date"||i==="dateTime"?r=(l=e.value)==null?void 0:l.toISOString():typeof((s=e.value)==null?void 0:s.toString)=="function"?r=e.value.toString():r=e.value}else r=e.formattedValue;return ur(r,n)};class dr{constructor(t){this.options=void 0,this.rowString="",this.isEmpty=!0,this.options=t}addValue(t){this.isEmpty||(this.rowString+=this.options.csvOptions.delimiter),typeof this.options.sanitizeCellValue=="function"?this.rowString+=this.options.sanitizeCellValue(t,this.options.csvOptions):this.rowString+=t,this.isEmpty=!1}getRowString(){return this.rowString}}const _p=({id:e,columns:t,getCellParams:n,csvOptions:o,ignoreValueFormatter:r})=>{const l=new dr({csvOptions:o});return t.forEach(s=>{const i=n(e,s.field);l.addValue(Ji(i,{ignoreValueFormatter:r,csvOptions:o}))}),l.getRowString()};function Wp(e){const{columns:t,rowIds:n,csvOptions:o,ignoreValueFormatter:r,apiRef:l}=e,s=n.reduce((g,h)=>`${g}${_p({id:h,columns:t,getCellParams:l.current.getCellParams,ignoreValueFormatter:r,csvOptions:o})}\r
`,"").trim();if(!o.includeHeaders)return s;const i=t.filter(g=>g.field!==Fn.field),c=[];if(o.includeColumnGroupsHeaders){const g=l.current.getAllGroupDetails();let h=0;const m=i.reduce((S,y)=>{const p=l.current.getColumnGroupPath(y.field);return S[y.field]=p,h=Math.max(h,p.length),S},{});for(let S=0;S<h;S+=1){const y=new dr({csvOptions:o,sanitizeCellValue:ur});c.push(y),i.forEach(p=>{const v=(m[p.field]||[])[S],I=g[v];y.addValue(I?I.headerName||I.groupId:"")})}}const d=new dr({csvOptions:o,sanitizeCellValue:ur});return i.forEach(g=>{d.addValue(g.headerName||g.field)}),c.push(d),`${`${c.map(g=>g.getRowString()).join(`\r
`)}\r
`}${s}`.trim()}function es(e){const t=document.createElement("span");t.style.whiteSpace="pre",t.style.userSelect="all",t.style.opacity="0px",t.textContent=e,document.body.appendChild(t);const n=document.createRange();n.selectNode(t);const o=window.getSelection();o.removeAllRanges(),o.addRange(n);try{document.execCommand("copy")}finally{document.body.removeChild(t)}}function Up(e){navigator.clipboard?navigator.clipboard.writeText(e).catch(()=>{es(e)}):es(e)}function Kp(e){var t;return!!((t=window.getSelection())!=null&&t.toString()||e&&(e.selectionEnd||0)-(e.selectionStart||0)>0)}const qp=(e,t)=>{const n=t.ignoreValueFormatterDuringExport,o=(typeof n=="object"?n==null?void 0:n.clipboardExport:n)||!1,r=t.clipboardCopyCellDelimiter,l=a.useCallback(s=>{if(!fC(s)||Kp(s.target))return;let i="";if(Oo(e)>0)i=e.current.getDataAsCsv({includeHeaders:!1,delimiter:r,shouldAppendQuotes:!1,escapeFormulas:!1});else{const d=st(e);if(d){const u=e.current.getCellParams(d.id,d.field);i=Ji(u,{csvOptions:{delimiter:r,shouldAppendQuotes:!1,escapeFormulas:!1},ignoreValueFormatter:o})}}i=e.current.unstable_applyPipeProcessors("clipboardCopy",i),i&&(Up(i),e.current.publishEvent("clipboardCopy",i))},[e,o,r]);Fi(e,()=>e.current.rootElementRef.current,"keydown",l),Le(e,"clipboardCopy",t.onClipboardCopy)},Xp=e=>f({},e,{columnMenu:{open:!1}}),Qp=e=>{const t=Ze(e,"useGridColumnMenu"),n=a.useCallback(s=>{const i=uo(e),c={open:!0,field:s};(c.open!==i.open||c.field!==i.field)&&(e.current.setState(u=>u.columnMenu.open&&u.columnMenu.field===s?u:(t.debug("Opening Column Menu"),f({},u,{columnMenu:{open:!0,field:s}}))),e.current.hidePreferences())},[e,t]),o=a.useCallback(()=>{const s=uo(e);if(s.field){const d=At(e),u=yt(e),g=kt(e);let h=s.field;if(d[h]||(h=g[0]),u[h]===!1){const m=g.filter(y=>y===h?!0:u[y]!==!1),S=m.indexOf(h);h=m[S+1]||m[S-1]}e.current.setColumnHeaderFocus(h)}const i={open:!1,field:void 0};(i.open!==s.open||i.field!==s.field)&&e.current.setState(d=>(t.debug("Hiding Column Menu"),f({},d,{columnMenu:i})))},[e,t]),r=a.useCallback(s=>{t.debug("Toggle Column Menu");const i=uo(e);!i.open||i.field!==s?n(s):o()},[e,t,n,o]);we(e,{showColumnMenu:n,hideColumnMenu:o,toggleColumnMenu:r},"public"),le(e,"columnResizeStart",o),le(e,"virtualScrollerWheel",e.current.hideColumnMenu),le(e,"virtualScrollerTouchMove",e.current.hideColumnMenu)},Yp=(e,t,n)=>{var r,l,s;const o=an({apiRef:n,columnsToUpsert:t.columns,initialState:(r=t.initialState)==null?void 0:r.columns,columnVisibilityModel:t.columnVisibilityModel??((s=(l=t.initialState)==null?void 0:l.columns)==null?void 0:s.columnVisibilityModel)??{},keepOnlyColumnsToUpsert:!0});return f({},e,{columns:o,pinnedColumns:e.pinnedColumns??Co})};function Zp(e,t){var T,M;const n=Ze(e,"useGridColumns"),o=a.useRef(t.columns);e.current.registerControlState({stateId:"visibleColumns",propModel:t.columnVisibilityModel,propOnChange:t.onColumnVisibilityModelChange,stateSelector:yt,changeEvent:"columnVisibilityModelChange"});const r=a.useCallback(O=>{n.debug("Updating columns state."),e.current.setState(ts(O)),e.current.publishEvent("columnsChange",O.orderedFields)},[n,e]),l=a.useCallback(O=>At(e)[O],[e]),s=a.useCallback(()=>bt(e),[e]),i=a.useCallback(()=>We(e),[e]),c=a.useCallback((O,G=!0)=>(G?We(e):bt(e)).findIndex(F=>F.field===O),[e]),d=a.useCallback(O=>{const G=c(O);return qt(e)[G]},[e,c]),u=a.useCallback(O=>{var A,F;yt(e)!==O&&(e.current.setState(E=>f({},E,{columns:an({apiRef:e,columnsToUpsert:[],initialState:void 0,columnVisibilityModel:O,keepOnlyColumnsToUpsert:!1})})),(F=(A=e.current).updateRenderContext)==null||F.call(A))},[e]),g=a.useCallback(O=>{if(en(e)){e.current.updateNonPivotColumns(O);return}const G=an({apiRef:e,columnsToUpsert:O,initialState:void 0,keepOnlyColumnsToUpsert:!1,updateInitialVisibilityModel:!0});r(G)},[e,r]),h=a.useCallback((O,G)=>{const A=yt(e),F=A[O]??!0;if(G!==F){const E=f({},A,{[O]:G});e.current.setColumnVisibilityModel(E)}},[e]),m=a.useCallback(O=>kt(e).findIndex(A=>A===O),[e]),S=a.useCallback((O,G)=>{const A=kt(e),F=m(O);if(F===G)return;n.debug(`Moving column ${O} to index ${G}`);const E=[...A],P=E.splice(F,1)[0];E.splice(G,0,P),r(f({},It(e),{orderedFields:E}));const z={column:e.current.getColumn(O),targetIndex:e.current.getColumnIndexRelativeToVisibleColumns(O),oldIndex:F};e.current.publishEvent("columnIndexChange",z)},[e,n,r,m]),y=a.useCallback((O,G)=>{n.debug(`Updating column ${O} width to ${G}`);const A=It(e),F=A.lookup[O],E=f({},F,{width:G,hasBeenResized:!0});r(ir(f({},A,{lookup:f({},A.lookup,{[O]:E})}),e.current.getRootDimensions())),e.current.publishEvent("columnWidthChange",{element:e.current.getColumnHeaderElement(O),colDef:E,width:G})},[e,n,r]),p={getColumn:l,getAllColumns:s,getColumnIndex:c,getColumnPosition:d,getVisibleColumns:i,getColumnIndexRelativeToVisibleColumns:m,updateColumns:g,setColumnVisibilityModel:u,setColumnVisibility:h,setColumnWidth:y},v={setColumnIndex:S};we(e,p,"public"),we(e,v,t.signature===vt.DataGrid?"private":"public");const I=a.useCallback((O,G)=>{var _,V;const A={},F=yt(e);(!G.exportOnlyDirtyModels||t.columnVisibilityModel!=null||Object.keys(((V=(_=t.initialState)==null?void 0:_.columns)==null?void 0:V.columnVisibilityModel)??{}).length>0||Object.keys(F).length>0)&&(A.columnVisibilityModel=F),A.orderedFields=kt(e);const P=bt(e),z={};return P.forEach(R=>{if(R.hasBeenResized){const B={};Mi.forEach(q=>{let X=R[q];X===1/0&&(X=-1),B[q]=X}),z[R.field]=B}}),Object.keys(z).length>0&&(A.dimensions=z),f({},O,{columns:A})},[e,t.columnVisibilityModel,(T=t.initialState)==null?void 0:T.columns]),D=a.useCallback((O,G)=>{var P;const A=(P=G.stateToRestore.columns)==null?void 0:P.columnVisibilityModel,F=G.stateToRestore.columns;if(A==null&&F==null)return O;const E=an({apiRef:e,columnsToUpsert:[],initialState:F,columnVisibilityModel:A,keepOnlyColumnsToUpsert:!1});return e.current.setState(ts(E)),F!=null&&e.current.publishEvent("columnsChange",E.orderedFields),O},[e]),H=a.useCallback((O,G)=>{var A;if(G===lt.columns){const F=t.slots.columnsPanel;return C.jsx(F,f({},(A=t.slotProps)==null?void 0:A.columnsPanel))}return O},[t.slots.columnsPanel,(M=t.slotProps)==null?void 0:M.columnsPanel]),k=a.useCallback(O=>{const G=en(e);return t.disableColumnSelector||G?O:[...O,"columnMenuColumnsItem"]},[t.disableColumnSelector,e]);_e(e,"columnMenu",k),_e(e,"exportState",I),_e(e,"restoreState",D),_e(e,"preferencePanel",H);const $=a.useRef(null);le(e,"viewportInnerSizeChange",O=>{if($.current!==O.width){if($.current=O.width,!We(e).some(A=>A.flex&&A.flex>0))return;r(ir(It(e),e.current.getRootDimensions()))}});const x=a.useCallback(()=>{n.info("Columns pipe processing have changed, regenerating the columns");const O=an({apiRef:e,columnsToUpsert:[],initialState:void 0,keepOnlyColumnsToUpsert:!1});r(O)},[e,n,r]);_r(e,"hydrateColumns",x);const w=a.useRef(!0);a.useEffect(()=>{if(w.current){w.current=!1;return}if(n.info(`GridColumns have changed, new length ${t.columns.length}`),o.current===t.columns)return;const O=an({apiRef:e,initialState:void 0,columnsToUpsert:t.columns,keepOnlyColumnsToUpsert:!0,updateInitialVisibilityModel:!0});o.current=t.columns,r(O)},[n,e,r,t.columns]),a.useEffect(()=>{t.columnVisibilityModel!==void 0&&e.current.setColumnVisibilityModel(t.columnVisibilityModel)},[e,n,t.columnVisibilityModel])}function ts(e){return t=>f({},t,{columns:e})}const Jp=(e,t)=>{var n;return f({},e,{density:((n=t.initialState)==null?void 0:n.density)??t.density??"standard"})},ew=(e,t)=>{var i;const n=Ze(e,"useDensity");e.current.registerControlState({stateId:"density",propModel:t.density,propOnChange:t.onDensityChange,stateSelector:dn,changeEvent:"densityChange"});const r={setDensity:$e(c=>{dn(e)!==c&&(n.debug(`Set grid density to ${c}`),e.current.setState(u=>f({},u,{density:c})))})};we(e,r,"public");const l=a.useCallback((c,d)=>{var h;const u=dn(e);return!d.exportOnlyDirtyModels||t.density!=null||((h=t.initialState)==null?void 0:h.density)!=null?f({},c,{density:u}):c},[e,t.density,(i=t.initialState)==null?void 0:i.density]),s=a.useCallback((c,d)=>{var g;const u=(g=d.stateToRestore)!=null&&g.density?d.stateToRestore.density:dn(e);return e.current.setState(h=>f({},h,{density:u})),c},[e]);_e(e,"exportState",l),_e(e,"restoreState",s),a.useEffect(()=>{t.density&&e.current.setDensity(t.density)},[e,t.density])};function tw(e,t="csv",n=document.title||"untitled"){const o=`${n}.${t}`;if("download"in HTMLAnchorElement.prototype){const r=URL.createObjectURL(e),l=document.createElement("a");l.href=r,l.download=o,l.click(),setTimeout(()=>{URL.revokeObjectURL(r)});return}throw new Error("MUI X: exportAs not supported.")}const ec=({apiRef:e,options:t})=>{const n=bt(e);return t.fields?t.fields.reduce((r,l)=>{const s=n.find(i=>i.field===l);return s&&r.push(s),r},[]):(t.allColumns?n:We(e)).filter(r=>!r.disableExport)},tc=({apiRef:e})=>{var c,d;const t=ii(e),n=tt(e),o=Oo(e),r=t.filter(u=>n[u].type!=="footer"),l=vn(e),s=((c=l==null?void 0:l.top)==null?void 0:c.map(u=>u.id))||[],i=((d=l==null?void 0:l.bottom)==null?void 0:d.map(u=>u.id))||[];if(r.unshift(...s),r.push(...i),o>0){const u=ui(e);return r.filter(g=>u.has(g))}return r},nw=(e,t)=>{const n=Ze(e,"useGridCsvExport"),o=t.ignoreValueFormatterDuringExport,r=(typeof o=="object"?o==null?void 0:o.csvExport:o)||!1,l=a.useCallback((d={})=>{n.debug("Get data as CSV");const u=ec({apiRef:e,options:d}),h=(d.getRowsToExport??tc)({apiRef:e});return Wp({columns:u,rowIds:h,csvOptions:{delimiter:d.delimiter||",",shouldAppendQuotes:d.shouldAppendQuotes??!0,includeHeaders:d.includeHeaders??!0,includeColumnGroupsHeaders:d.includeColumnGroupsHeaders??!0,escapeFormulas:d.escapeFormulas??!0},ignoreValueFormatter:r,apiRef:e})},[n,e,r]),s=a.useCallback(d=>{n.debug("Export data as CSV");const u=l(d),g=new Blob([d!=null&&d.utf8WithBom?new Uint8Array([239,187,191]):"",u],{type:"text/csv"});tw(g,"csv",d==null?void 0:d.fileName)},[n,l]);we(e,{getDataAsCsv:l,exportDataAsCsv:s},"public");const c=a.useCallback((d,u)=>{var g;return(g=u.csvOptions)!=null&&g.disableToolbarButton?d:[...d,{component:C.jsx(vb,{options:u.csvOptions}),componentName:"csvExport"}]},[]);_e(e,"exportMenu",c)};function ow(e,t){const n=[],o=t.querySelectorAll("style, link[rel='stylesheet']");for(let r=0;r<o.length;r+=1){const l=o[r];if(l.tagName==="STYLE"){const s=e.createElement(l.tagName),i=l.sheet;if(i){let c="";for(let d=0;d<i.cssRules.length;d+=1)typeof i.cssRules[d].cssText=="string"&&(c+=`${i.cssRules[d].cssText}\r
`);s.appendChild(e.createTextNode(c)),e.head.appendChild(s)}}else if(l.getAttribute("href")){const s=e.createElement(l.tagName);for(let i=0;i<l.attributes.length;i+=1){const c=l.attributes[i];c&&s.setAttribute(c.nodeName,c.nodeValue||"")}n.push(new Promise(i=>{s.addEventListener("load",()=>i())})),e.head.appendChild(s)}}return n}const fo=(e,t,n)=>{let o=e.paginationModel;const r=e.rowCount,l=(n==null?void 0:n.pageSize)??o.pageSize,s=(n==null?void 0:n.page)??o.page,i=fi(r,l,s);n&&((n==null?void 0:n.page)!==o.page||(n==null?void 0:n.pageSize)!==o.pageSize)&&(o=n);const c=l===-1?0:Mg(o.page,i);return c!==o.page&&(o=f({},o,{page:c})),mi(o.pageSize,t),o},rw=(e,t)=>{var I,D;const n=Ze(e,"useGridPaginationModel"),o=W(e,xn),r=a.useRef(Qe(e)),l=Math.floor(t.rowHeight*o);e.current.registerControlState({stateId:"paginationModel",propModel:t.paginationModel,propOnChange:t.onPaginationModelChange,stateSelector:Je,changeEvent:"paginationModelChange"});const s=a.useCallback(H=>{const k=Je(e);H!==k.page&&(n.debug(`Setting page to ${H}`),e.current.setPaginationModel({page:H,pageSize:k.pageSize}))},[e,n]),i=a.useCallback(H=>{const k=Je(e);H!==k.pageSize&&(n.debug(`Setting page size to ${H}`),e.current.setPaginationModel({pageSize:H,page:k.page}))},[e,n]),c=a.useCallback(H=>{const k=Je(e);H!==k&&(n.debug("Setting 'paginationModel' to",H),e.current.setState($=>f({},$,{pagination:f({},$.pagination,{paginationModel:fo($.pagination,t.signature,H)})}),"setPaginationModel"))},[e,n,t.signature]);we(e,{setPage:s,setPageSize:i,setPaginationModel:c},"public");const u=a.useCallback((H,k)=>{var x,w;const $=Je(e);return!k.exportOnlyDirtyModels||t.paginationModel!=null||((w=(x=t.initialState)==null?void 0:x.pagination)==null?void 0:w.paginationModel)!=null||$.page!==0&&$.pageSize!==Ig(t.autoPageSize)?f({},H,{pagination:f({},H.pagination,{paginationModel:$})}):H},[e,t.paginationModel,(D=(I=t.initialState)==null?void 0:I.pagination)==null?void 0:D.paginationModel,t.autoPageSize]),g=a.useCallback((H,k)=>{var L,x;const $=(L=k.stateToRestore.pagination)!=null&&L.paginationModel?f({},hi(t.autoPageSize),(x=k.stateToRestore.pagination)==null?void 0:x.paginationModel):Je(e);return e.current.setState(w=>f({},w,{pagination:f({},w.pagination,{paginationModel:fo(w.pagination,t.signature,$)})}),"stateRestorePreProcessing"),H},[e,t.autoPageSize,t.signature]);_e(e,"exportState",u),_e(e,"restoreState",g);const h=()=>{var k;const H=Je(e);(k=e.current.virtualScrollerRef)!=null&&k.current&&e.current.scrollToIndexes({rowIndex:H.page*H.pageSize})},m=a.useCallback(()=>{if(!t.autoPageSize)return;const H=e.current.getRootDimensions(),k=Math.max(1,Math.floor(H.viewportInnerSize.height/l));e.current.setPageSize(k)},[e,t.autoPageSize,l]),S=a.useCallback(H=>{if(H==null)return;const k=Je(e);if(k.page===0)return;const $=pi(e);k.page>$-1&&e.current.setPage(Math.max(0,$-1))},[e]),y=a.useCallback(()=>{Je(e).page!==0&&e.current.setPage(0),e.current.getScrollPosition().top!==0&&e.current.scroll({top:0})},[e]),p=a.useCallback(H=>{const k=f({},H,{items:Dr(e)});ct(k,r.current)||(r.current=k,y())},[e,y]);le(e,"viewportInnerSizeChange",m),le(e,"paginationModelChange",h),le(e,"rowCountChange",S),le(e,"sortModelChange",y),le(e,"filterModelChange",p);const v=a.useRef(!0);a.useEffect(()=>{if(v.current){v.current=!1;return}t.pagination&&e.current.setState(H=>f({},H,{pagination:f({},H.pagination,{paginationModel:fo(H.pagination,t.signature,t.paginationModel)})}))},[e,t.paginationModel,t.signature,t.pagination]),a.useEffect(()=>{e.current.setState(H=>{const k=t.pagination===!0;return H.pagination.paginationMode===t.paginationMode||H.pagination.enabled===k?H:f({},H,{pagination:f({},H.pagination,{paginationMode:t.paginationMode,enabled:t.pagination===!0})})})},[e,t.paginationMode,t.pagination]),a.useEffect(m,[m])};function lw(){return new Promise(e=>{requestAnimationFrame(()=>{e()})})}function sw(e){const t=document.createElement("iframe");return t.style.position="absolute",t.style.width="0px",t.style.height="0px",t.title=e||document.title,t}const iw=(e,t)=>{const n=e.current.rootElementRef.current!==null,o=Ze(e,"useGridPrintExport"),r=a.useRef(null),l=a.useRef(null),s=a.useRef({}),i=a.useRef([]),c=a.useRef(null);a.useEffect(()=>{r.current=Zt(e.current.rootElementRef.current)},[e,n]);const d=a.useCallback((p,v,I)=>new Promise(D=>{const H=ec({apiRef:e,options:{fields:p,allColumns:v}}).map(L=>L.field),k=bt(e),$={};k.forEach(L=>{$[L.field]=H.includes(L.field)}),I&&($[Fn.field]=!0),e.current.setColumnVisibilityModel($),D()}),[e]),u=a.useCallback(p=>{const I=p({apiRef:e}).reduce((D,H)=>{const k=e.current.getRow(H);return k[Cn]||D.push(k),D},[]);e.current.setRows(I)},[e]),g=a.useCallback((p,v)=>{var F,E,P;const I=f({copyStyles:!0,hideToolbar:!1,hideFooter:!1,includeCheckboxes:!1},v),D=p.contentDocument;if(!D)return;const H=Zn(e),k=e.current.rootElementRef.current,$=k.cloneNode(!0),L=$.querySelector(`.${b.main}`);L.style.overflow="visible",$.style.contain="size";let x=((F=k.querySelector(`.${b.toolbarContainer}`))==null?void 0:F.offsetHeight)||0,w=((E=k.querySelector(`.${b.footerContainer}`))==null?void 0:E.offsetHeight)||0;const T=$.querySelector(`.${b.footerContainer}`);I.hideToolbar&&((P=$.querySelector(`.${b.toolbarContainer}`))==null||P.remove(),x=0),I.hideFooter&&T&&(T.remove(),w=0);const M=H.currentPageTotalHeight+$r(e,t)+x+w;$.style.height=`${M}px`,$.style.boxSizing="content-box",!I.hideFooter&&T&&(T.style.position="absolute",T.style.width="100%",T.style.top=`${M-w}px`);const O=document.createElement("div");O.appendChild($),D.body.style.marginTop="0px",D.body.innerHTML=O.innerHTML;const G=typeof I.pageStyle=="function"?I.pageStyle():I.pageStyle;if(typeof G=="string"){const z=D.createElement("style");z.appendChild(D.createTextNode(G)),D.head.appendChild(z)}I.bodyClassName&&D.body.classList.add(...I.bodyClassName.split(" "));let A=[];if(I.copyStyles){const z=k.getRootNode(),_=z.constructor.name==="ShadowRoot"?z:r.current;A=ow(D,_)}Promise.all(A).then(()=>{p.contentWindow.print()})},[e,r,t]),h=a.useCallback(p=>{var v,I;r.current.body.removeChild(p),e.current.restoreState(l.current||{}),(I=(v=l.current)==null?void 0:v.columns)!=null&&I.columnVisibilityModel||e.current.setColumnVisibilityModel(s.current),e.current.setState(D=>f({},D,{virtualization:c.current})),e.current.setRows(i.current),l.current=null,s.current={},i.current=[]},[e]),S={exportDataAsPrint:a.useCallback(async p=>{if(o.debug("Export data as Print"),!e.current.rootElementRef.current)throw new Error("MUI X: No grid root element available.");if(l.current=e.current.exportState(),s.current=yt(e),i.current=e.current.getSortedRows().filter(I=>!I[Cn]),t.pagination){const D={page:0,pageSize:kr(e)};e.current.setState(H=>f({},H,{pagination:f({},H.pagination,{paginationModel:fo(H.pagination,"DataGridPro",D)})}))}c.current=e.current.state.virtualization,e.current.setState(I=>f({},I,{virtualization:f({},I.virtualization,{enabled:!1,enabledForColumns:!1})})),await d(p==null?void 0:p.fields,p==null?void 0:p.allColumns,p==null?void 0:p.includeCheckboxes),u((p==null?void 0:p.getRowsToExport)??tc),await lw();const v=sw(p==null?void 0:p.fileName);v.onload=()=>{g(v,p),v.contentWindow.matchMedia("print").addEventListener("change",D=>{D.matches===!1&&h(v)})},r.current.body.appendChild(v)},[t,o,e,g,h,d,u])};we(e,S,"public");const y=a.useCallback((p,v)=>{var I;return(I=v.printOptions)!=null&&I.disableToolbarButton?p:[...p,{component:C.jsx(Ib,{options:v.printOptions}),componentName:"printExport"}]},[]);_e(e,"exportMenu",y)},cw=(e,t,n)=>{var r,l;const o=t.filterModel??((l=(r=t.initialState)==null?void 0:r.filter)==null?void 0:l.filterModel)??Rn();return f({},e,{filter:f({filterModel:Mr(o,t.disableMultipleColumnsFiltering,n)},Bs),visibleRowsLookup:{}})},aw=e=>e.filteredRowsLookup;function ns(e,t){return e.current.applyStrategyProcessor("visibleRowsLookupCreation",{tree:t.rows.tree,filteredRowsLookup:t.filter.filteredRowsLookup})}function uw(){return Es(Object.values)}const dw=(e,t)=>{var x,w,T;const n=Ze(e,"useGridFilter");e.current.registerControlState({stateId:"filter",propModel:t.filterModel,propOnChange:t.onFilterModelChange,stateSelector:Qe,changeEvent:"filterModelChange"});const o=a.useCallback(()=>{e.current.setState(M=>{const O=Qe(e),G=e.current.getFilterState(O),A=f({},M,{filter:f({},M.filter,G)}),F=ns(e,A);return f({},A,{visibleRowsLookup:F})}),e.current.publishEvent("filteredRowsSet")},[e]),r=a.useCallback((M,O)=>O==null||O.filterable===!1||t.disableColumnFilter?M:[...M,"columnMenuFilterItem"],[t.disableColumnFilter]),l=a.useCallback(M=>{const O=Qe(e),G=[...O.items],A=G.findIndex(F=>F.id===M.id);A===-1?G.push(M):G[A]=M,e.current.setFilterModel(f({},O,{items:G}),"upsertFilterItem")},[e]),s=a.useCallback(M=>{const O=Qe(e),G=[...O.items];M.forEach(A=>{const F=G.findIndex(E=>E.id===A.id);F===-1?G.push(A):G[F]=A}),e.current.setFilterModel(f({},O,{items:G}),"upsertFilterItems")},[e]),i=a.useCallback(M=>{const O=Qe(e),G=O.items.filter(A=>A.id!==M.id);G.length!==O.items.length&&e.current.setFilterModel(f({},O,{items:G}),"deleteFilterItem")},[e]),c=a.useCallback((M,O,G)=>{if(n.debug("Displaying filter panel"),M){const A=Qe(e),F=A.items.filter(_=>{var q;if(_.value!==void 0)return!(Array.isArray(_.value)&&_.value.length===0);const R=(q=e.current.getColumn(_.field).filterOperators)==null?void 0:q.find(X=>X.value===_.operator);return!(typeof(R==null?void 0:R.requiresFilterValue)>"u"?!0:R==null?void 0:R.requiresFilterValue)});let E;const P=F.find(_=>_.field===M),z=e.current.getColumn(M);P?E=F:t.disableMultipleColumnsFiltering?E=[sr({field:M,operator:z.filterOperators[0].value},e)]:E=[...F,sr({field:M,operator:z.filterOperators[0].value},e)],e.current.setFilterModel(f({},A,{items:E}))}e.current.showPreferences(lt.filters,O,G)},[e,n,t.disableMultipleColumnsFiltering]),d=a.useCallback(()=>{n.debug("Hiding filter panel"),e.current.hidePreferences()},[e,n]),u=a.useCallback(M=>{const O=Qe(e);O.logicOperator!==M&&e.current.setFilterModel(f({},O,{logicOperator:M}),"changeLogicOperator")},[e]),g=a.useCallback(M=>{const O=Qe(e);ct(O.quickFilterValues,M)||e.current.setFilterModel(f({},O,{quickFilterValues:[...M]}))},[e]),h=a.useCallback((M,O)=>{Qe(e)!==M&&(n.debug("Setting filter model"),e.current.updateControlState("filter",yl(M,t.disableMultipleColumnsFiltering,e),O),e.current.unstable_applyFilters())},[e,n,t.disableMultipleColumnsFiltering]),m=a.useCallback(M=>{const O=Mr(M,t.disableMultipleColumnsFiltering,e),G=t.filterMode==="client"?Td(O,e,t.disableEval):null,A=e.current.applyStrategyProcessor("filtering",{isRowMatchingFilters:G,filterModel:O??Rn()});return f({},A,{filterModel:O})},[t.disableMultipleColumnsFiltering,t.filterMode,t.disableEval,e]),S={setFilterLogicOperator:u,unstable_applyFilters:o,deleteFilterItem:i,upsertFilterItem:l,upsertFilterItems:s,setFilterModel:h,showFilterPanel:c,hideFilterPanel:d,setQuickFilterValues:g,ignoreDiacritics:t.ignoreDiacritics,getFilterState:m};we(e,S,"public");const y=a.useCallback((M,O)=>{var F,E;const G=Qe(e);return G.items.forEach(P=>{delete P.fromInput}),!O.exportOnlyDirtyModels||t.filterModel!=null||((E=(F=t.initialState)==null?void 0:F.filter)==null?void 0:E.filterModel)!=null||!ct(G,Rn())?f({},M,{filter:{filterModel:G}}):M},[e,t.filterModel,(w=(x=t.initialState)==null?void 0:x.filter)==null?void 0:w.filterModel]),p=a.useCallback((M,O)=>{var A;const G=(A=O.stateToRestore.filter)==null?void 0:A.filterModel;return G==null?M:(e.current.updateControlState("filter",yl(G,t.disableMultipleColumnsFiltering,e),"restoreState"),f({},M,{callbacks:[...M.callbacks,e.current.unstable_applyFilters]}))},[e,t.disableMultipleColumnsFiltering]),v=a.useCallback((M,O)=>{var G;if(O===lt.filters){const A=t.slots.filterPanel;return C.jsx(A,f({},(G=t.slotProps)==null?void 0:G.filterPanel))}return M},[t.slots.filterPanel,(T=t.slotProps)==null?void 0:T.filterPanel]),{getRowId:I}=t,D=Gt(uw),H=a.useCallback(M=>{var V;if(t.filterMode!=="client"||!M.isRowMatchingFilters||!M.filterModel.items.length&&!((V=M.filterModel.quickFilterValues)!=null&&V.length))return Bs;const O=Ct(e),G={},{isRowMatchingFilters:A}=M,F={},E={passingFilterItems:null,passingQuickFilterValues:null},P=D.current(e.current.state.rows.dataRowIdToModelLookup);for(let R=0;R<P.length;R+=1){const B=P[R],q=I?I(B):B.id;A(B,void 0,E);const X=Hd([E.passingFilterItems],[E.passingQuickFilterValues],M.filterModel,e,F);X||(G[q]=X)}const z="auto-generated-group-footer-root";return O[z]&&(G[z]=!0),{filteredRowsLookup:G,filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}}},[e,t.filterMode,I,D]);_e(e,"columnMenu",r),_e(e,"exportState",y),_e(e,"restoreState",p),_e(e,"preferencePanel",v),Bn(e,nn,"filtering",H),Bn(e,nn,"visibleRowsLookupCreation",aw);const k=a.useCallback(()=>{n.debug("onColUpdated - GridColumns changed, applying filters");const M=Qe(e),O=At(e),G=M.items.filter(A=>A.field&&O[A.field]);G.length<M.items.length&&e.current.setFilterModel(f({},M,{items:G}))},[e,n]),$=a.useCallback(M=>{M==="filtering"&&e.current.unstable_applyFilters()},[e]),L=a.useCallback(()=>{e.current.setState(M=>f({},M,{visibleRowsLookup:ns(e,M)}))},[e]);le(e,"rowsSet",o),le(e,"columnsChange",k),le(e,"activeStrategyProcessorChange",$),le(e,"rowExpansionChange",L),le(e,"columnVisibilityModelChange",()=>{const M=Qe(e);M.quickFilterValues&&Xs(M)&&e.current.unstable_applyFilters()}),Yn(()=>{e.current.unstable_applyFilters()}),nt(()=>{t.filterModel!==void 0&&e.current.setFilterModel(t.filterModel)},[e,n,t.filterModel])},gw=e=>f({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},tabIndex:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}),fw=(e,t)=>{const n=Ze(e,"useGridFocus"),o=a.useRef(null),r=e.current.rootElementRef.current!==null,l=a.useCallback((x,w)=>{x&&e.current.getRow(x.id)&&e.current.publishEvent("cellFocusOut",e.current.getCellParams(x.id,x.field),w)},[e]),s=a.useCallback((x,w)=>{const T=st(e);(T==null?void 0:T.id)===x&&(T==null?void 0:T.field)===w||(e.current.setState(M=>(n.debug(`Focusing on cell with id=${x} and field=${w}`),f({},M,{tabIndex:{cell:{id:x,field:w},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},focus:{cell:{id:x,field:w},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))),e.current.getRow(x)&&(T&&l(T,{}),e.current.publishEvent("cellFocusIn",e.current.getCellParams(x,w))))},[e,n,l]),i=a.useCallback((x,w={})=>{const T=st(e);l(T,w),e.current.setState(M=>(n.debug(`Focusing on column header with colIndex=${x}`),f({},M,{tabIndex:{columnHeader:{field:x},columnHeaderFilter:null,cell:null,columnGroupHeader:null},focus:{columnHeader:{field:x},columnHeaderFilter:null,cell:null,columnGroupHeader:null}})))},[e,n,l]),c=a.useCallback((x,w={})=>{const T=st(e);l(T,w),e.current.setState(M=>(n.debug(`Focusing on column header filter with colIndex=${x}`),f({},M,{tabIndex:{columnHeader:null,columnHeaderFilter:{field:x},cell:null,columnGroupHeader:null},focus:{columnHeader:null,columnHeaderFilter:{field:x},cell:null,columnGroupHeader:null}})))},[e,n,l]),d=a.useCallback((x,w,T={})=>{const M=st(e);M&&e.current.publishEvent("cellFocusOut",e.current.getCellParams(M.id,M.field),T),e.current.setState(O=>f({},O,{tabIndex:{columnGroupHeader:{field:x,depth:w},columnHeader:null,columnHeaderFilter:null,cell:null},focus:{columnGroupHeader:{field:x,depth:w},columnHeader:null,columnHeaderFilter:null,cell:null}}))},[e]),u=a.useCallback(()=>bo(e),[e]),g=a.useCallback((x,w,T)=>{let M=e.current.getColumnIndex(w);const O=We(e),G=Ut(e,{pagination:t.pagination,paginationMode:t.paginationMode}),A=vn(e),F=[].concat(A.top||[],G.rows,A.bottom||[]);let E=F.findIndex(V=>V.id===x);T==="right"?M+=1:T==="left"?M-=1:E+=1,M>=O.length?(E+=1,E<F.length&&(M=0)):M<0&&(E-=1,E>=0&&(M=O.length-1)),E=it(E,0,F.length-1);const P=F[E];if(!P)return;const z=e.current.unstable_getCellColSpanInfo(P.id,M);z&&z.spannedByColSpan&&(T==="left"||T==="below"?M=z.leftVisibleCellIndex:T==="right"&&(M=z.rightVisibleCellIndex)),M=it(M,0,O.length-1);const _=O[M];e.current.setCellFocus(P.id,_.field)},[e,t.pagination,t.paginationMode]),h=a.useCallback(({id:x,field:w})=>{e.current.setCellFocus(x,w)},[e]),m=a.useCallback((x,w)=>{w.key==="Enter"||w.key==="Tab"||w.key==="Shift"||xo(w.key)||e.current.setCellFocus(x.id,x.field)},[e]),S=a.useCallback(({field:x},w)=>{w.target===w.currentTarget&&e.current.setColumnHeaderFocus(x,w)},[e]),y=a.useCallback(({fields:x,depth:w},T)=>{if(T.target!==T.currentTarget)return;const M=bo(e);M!==null&&M.depth===w&&x.includes(M.field)||e.current.setColumnGroupHeaderFocus(x[0],w,T)},[e]),p=a.useCallback((x,w)=>{var T,M;(M=(T=w.relatedTarget)==null?void 0:T.getAttribute("class"))!=null&&M.includes(b.columnHeader)||(n.debug("Clearing focus"),e.current.setState(O=>f({},O,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})))},[n,e]),v=a.useCallback(x=>{o.current=x},[]),I=a.useCallback(x=>{const w=o.current;o.current=null;const T=st(e);if(!e.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:x,cell:w}))return;if(!T){w&&e.current.setCellFocus(w.id,w.field);return}if((w==null?void 0:w.id)===T.id&&(w==null?void 0:w.field)===T.field)return;const O=e.current.getCellElement(T.id,T.field);O!=null&&O.contains(x.target)||(w?e.current.setCellFocus(w.id,w.field):(e.current.setState(G=>f({},G,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})),l(T,x)))},[e,l]),D=a.useCallback(x=>{if(x.cellMode==="view")return;const w=st(e);((w==null?void 0:w.id)!==x.id||(w==null?void 0:w.field)!==x.field)&&e.current.setCellFocus(x.id,x.field)},[e]),H=a.useCallback(()=>{const x=st(e);if(x&&!e.current.getRow(x.id)){const w=x.id;let T=null;if(typeof w<"u"){const M=e.current.getRowElement(w),O=M!=null&&M.dataset.rowindex?Number(M==null?void 0:M.dataset.rowindex):0,G=Ut(e,{pagination:t.pagination,paginationMode:t.paginationMode}),A=G.rows[it(O,0,G.rows.length-1)];T=(A==null?void 0:A.id)??null}e.current.setState(M=>f({},M,{focus:{cell:T===null?null:{id:T,field:x.field},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))}},[e,t.pagination,t.paginationMode]),k=$e(()=>{const x=st(e);if(!x)return;const w=Ut(e,{pagination:t.pagination,paginationMode:t.paginationMode});if(w.rows.find(O=>O.id===x.id))return;const M=We(e);e.current.setState(O=>f({},O,{tabIndex:{cell:{id:w.rows[0].id,field:M[0].field},columnGroupHeader:null,columnHeader:null,columnHeaderFilter:null}}))}),$={setCellFocus:s,setColumnHeaderFocus:i,setColumnHeaderFilterFocus:c},L={moveFocusToRelativeCell:g,setColumnGroupHeaderFocus:d,getColumnGroupHeaderFocus:u};we(e,$,"public"),we(e,L,"private"),a.useEffect(()=>{const x=Zt(e.current.rootElementRef.current);return x.addEventListener("mouseup",I),()=>{x.removeEventListener("mouseup",I)}},[e,r,I]),le(e,"columnHeaderBlur",p),le(e,"cellDoubleClick",h),le(e,"cellMouseDown",v),le(e,"cellKeyDown",m),le(e,"cellModeChange",D),le(e,"columnHeaderFocus",S),le(e,"columnGroupHeaderFocus",y),le(e,"rowsSet",H),le(e,"paginationModelChange",k)},Jo=({currentColIndex:e,firstColIndex:t,lastColIndex:n,isRtl:o})=>{if(o){if(e<n)return e+1}else if(!o&&e>t)return e-1;return null},er=({currentColIndex:e,firstColIndex:t,lastColIndex:n,isRtl:o})=>{if(o){if(e>t)return e-1}else if(!o&&e<n)return e+1;return null};function hw(e,t,n,o){var i,c;const r=ki(e);if(!((i=r[t])!=null&&i[n]))return t;const l=ii(e);let s=l.indexOf(t)+(o==="down"?1:-1);for(;s>=0&&s<l.length;){const d=l[s];if(!((c=r[d])!=null&&c[n]))return d;s+=o==="down"?1:-1}return t}const mw=Te(qn,vn,(e,t)=>(t.top||[]).concat(e.rows,t.bottom||[])),Cw=(e,t)=>{const n=Ze(e,"useGridKeyboardNavigation"),o=Dt(),r=t.listView,l=a.useCallback(()=>mw(e),[e]),s=t.signature!=="DataGrid"&&t.headerFilters,i=a.useCallback((v,I,D="left",H="up")=>{const k=Xt(e),$=e.current.unstable_getCellColSpanInfo(I,v);$&&$.spannedByColSpan&&(D==="left"?v=$.leftVisibleCellIndex:D==="right"&&(v=$.rightVisibleCellIndex));const L=r?wn(e).field:In(e)[v],x=hw(e,I,L,H),w=k.findIndex(T=>T.id===x);n.debug(`Navigating to cell row ${w}, col ${v}`),e.current.scrollToIndexes({colIndex:v,rowIndex:w}),e.current.setCellFocus(x,L)},[e,n,r]),c=a.useCallback((v,I)=>{n.debug(`Navigating to header col ${v}`),e.current.scrollToIndexes({colIndex:v});const D=e.current.getVisibleColumns()[v].field;e.current.setColumnHeaderFocus(D,I)},[e,n]),d=a.useCallback((v,I)=>{n.debug(`Navigating to header filter col ${v}`),e.current.scrollToIndexes({colIndex:v});const D=e.current.getVisibleColumns()[v].field;e.current.setColumnHeaderFilterFocus(D,I)},[e,n]),u=a.useCallback((v,I,D)=>{n.debug(`Navigating to header col ${v}`),e.current.scrollToIndexes({colIndex:v});const{field:H}=e.current.getVisibleColumns()[v];e.current.setColumnGroupHeaderFocus(H,I,D)},[e,n]),g=a.useCallback(v=>{var I;return(I=l()[v])==null?void 0:I.id},[l]),h=a.useCallback((v,I)=>{const D=I.currentTarget.querySelector(`.${b.columnHeaderTitleContainerContent}`);if(!!D&&D.contains(I.target)&&v.field!==Fn.field)return;const k=l(),$=e.current.getViewportPageSize(),L=v.field?e.current.getColumnIndex(v.field):0,x=k.length>0?0:null,w=k.length-1,T=0,M=We(e).length-1,O=Qn(e);let G=!0;switch(I.key){case"ArrowDown":{s?d(L,I):x!==null&&i(L,g(x));break}case"ArrowRight":{const A=er({currentColIndex:L,firstColIndex:T,lastColIndex:M,isRtl:o});A!==null&&c(A,I);break}case"ArrowLeft":{const A=Jo({currentColIndex:L,firstColIndex:T,lastColIndex:M,isRtl:o});A!==null&&c(A,I);break}case"ArrowUp":{O>0&&u(L,O-1,I);break}case"PageDown":{x!==null&&w!==null&&i(L,g(Math.min(x+$,w)));break}case"Home":{c(T,I);break}case"End":{c(M,I);break}case"Enter":{(I.ctrlKey||I.metaKey)&&e.current.toggleColumnMenu(v.field);break}case" ":break;default:G=!1}G&&I.preventDefault()},[e,l,s,d,i,g,o,c,u]),m=a.useCallback((v,I)=>{const D=sf(e)===v.field,H=cf(e)===v.field;if(D||H||!xo(I.key))return;const k=l(),$=e.current.getViewportPageSize(),L=v.field?e.current.getColumnIndex(v.field):0,x=0,w=k.length-1,T=0,M=We(e).length-1;let O=!0;switch(I.key){case"ArrowDown":{const G=g(x);G!=null&&i(L,G);break}case"ArrowRight":{const G=er({currentColIndex:L,firstColIndex:T,lastColIndex:M,isRtl:o});G!==null&&d(G,I);break}case"ArrowLeft":{const G=Jo({currentColIndex:L,firstColIndex:T,lastColIndex:M,isRtl:o});G!==null?d(G,I):e.current.setColumnHeaderFilterFocus(v.field,I);break}case"ArrowUp":{c(L,I);break}case"PageDown":{w!==null&&i(L,g(Math.min(x+$,w)));break}case"Home":{d(T,I);break}case"End":{d(M,I);break}case" ":break;default:O=!1}O&&I.preventDefault()},[e,l,d,o,c,i,g]),S=a.useCallback((v,I)=>{const D=bo(e);if(D===null)return;const{field:H,depth:k}=D,{fields:$,depth:L,maxDepth:x}=v,w=l(),T=e.current.getViewportPageSize(),M=e.current.getColumnIndex(H),O=H?e.current.getColumnIndex(H):0,G=0,A=w.length-1,F=0,E=We(e).length-1;let P=!0;switch(I.key){case"ArrowDown":{L===x-1?c(M,I):u(M,k+1,I);break}case"ArrowUp":{L>0&&u(M,k-1,I);break}case"ArrowRight":{const z=$.length-$.indexOf(H)-1;M+z+1<=E&&u(M+z+1,k,I);break}case"ArrowLeft":{const z=$.indexOf(H);M-z-1>=F&&u(M-z-1,k,I);break}case"PageDown":{A!==null&&i(O,g(Math.min(G+T,A)));break}case"Home":{u(F,k,I);break}case"End":{u(E,k,I);break}case" ":break;default:P=!1}P&&I.preventDefault()},[e,l,c,u,i,g]),y=a.useCallback((v,I)=>{if(eo(I))return;const D=e.current.getCellParams(v.id,v.field);if(D.cellMode===Oe.Edit||!xo(I.key)||!e.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:I,cell:D}))return;const k=l();if(k.length===0)return;const $=e.current.getViewportPageSize(),L=r?()=>0:e.current.getColumnIndex,x=v.field?L(v.field):0,w=k.findIndex(E=>E.id===v.id),T=0,M=k.length-1,O=0,A=(r?[wn(e)]:We(e)).length-1;let F=!0;switch(I.key){case"ArrowDown":{w<M&&i(x,g(w+1),o?"right":"left","down");break}case"ArrowUp":{w>T?i(x,g(w-1)):s?d(x,I):c(x,I);break}case"ArrowRight":{const E=er({currentColIndex:x,firstColIndex:O,lastColIndex:A,isRtl:o});E!==null&&i(E,g(w),o?"left":"right");break}case"ArrowLeft":{const E=Jo({currentColIndex:x,firstColIndex:O,lastColIndex:A,isRtl:o});E!==null&&i(E,g(w),o?"right":"left");break}case"Tab":{I.shiftKey&&x>O?i(x-1,g(w),"left"):!I.shiftKey&&x<A&&i(x+1,g(w),"right");break}case" ":{if(v.field===To)break;const P=v.colDef;if(P&&(P.field===Qd||Th(P.field)))break;!I.shiftKey&&w<M&&i(x,g(Math.min(w+$,M)));break}case"PageDown":{w<M&&i(x,g(Math.min(w+$,M)));break}case"PageUp":{const E=Math.max(w-$,T);E!==w&&E>=T?i(x,g(E)):c(x,I);break}case"Home":{I.ctrlKey||I.metaKey||I.shiftKey?i(O,g(T)):i(O,g(w));break}case"End":{I.ctrlKey||I.metaKey||I.shiftKey?i(A,g(M)):i(A,g(w));break}default:F=!1}F&&I.preventDefault()},[e,l,o,i,g,s,d,c,r]),p=a.useCallback((v,{event:I})=>I.key===" "?!1:v,[]);_e(e,"canStartEditing",p),le(e,"columnHeaderKeyDown",h),le(e,"headerFilterKeyDown",m),le(e,"columnGroupHeaderKeyDown",S),le(e,"cellKeyDown",y)},bw=(e,t)=>{var S,y;const n=Ze(e,"useGridRowCount"),o=W(e,Tr),r=W(e,gn),l=W(e,On),s=W(e,Je),i=Gt(()=>Je(e).pageSize);e.current.registerControlState({stateId:"paginationRowCount",propModel:t.rowCount,propOnChange:t.onRowCountChange,stateSelector:gn,changeEvent:"rowCountChange"});const d={setRowCount:a.useCallback(p=>{r!==p&&(n.debug("Setting 'rowCount' to",p),e.current.setState(v=>f({},v,{pagination:f({},v.pagination,{rowCount:p})})))},[e,n,r])};we(e,d,"public");const u=a.useCallback((p,v)=>{var H,k;const I=gn(e);return!v.exportOnlyDirtyModels||t.rowCount!=null||((k=(H=t.initialState)==null?void 0:H.pagination)==null?void 0:k.rowCount)!=null?f({},p,{pagination:f({},p.pagination,{rowCount:I})}):p},[e,t.rowCount,(y=(S=t.initialState)==null?void 0:S.pagination)==null?void 0:y.rowCount]),g=a.useCallback((p,v)=>{var D;const I=(D=v.stateToRestore.pagination)!=null&&D.rowCount?v.stateToRestore.pagination.rowCount:gn(e);return e.current.setState(H=>f({},H,{pagination:f({},H.pagination,{rowCount:I})})),p},[e]);_e(e,"exportState",u),_e(e,"restoreState",g);const h=a.useCallback(p=>{t.paginationMode==="client"||!i.current||p.pageSize!==i.current&&(i.current=p.pageSize,r===-1&&e.current.setPage(0))},[t.paginationMode,i,r,e]);le(e,"paginationModelChange",h),a.useEffect(()=>{t.paginationMode==="client"?e.current.setRowCount(o):t.rowCount!=null&&e.current.setRowCount(t.rowCount)},[e,t.paginationMode,o,t.rowCount]);const m=l.hasNextPage===!1;a.useEffect(()=>{m&&r===-1&&e.current.setRowCount(s.pageSize*s.page+o)},[e,o,m,r,s])},pw=(e,t)=>{var c,d;const n=Ze(e,"useGridPaginationMeta"),o=W(e,On);e.current.registerControlState({stateId:"paginationMeta",propModel:t.paginationMeta,propOnChange:t.onPaginationMetaChange,stateSelector:On,changeEvent:"paginationMetaChange"});const l={setPaginationMeta:a.useCallback(u=>{o!==u&&(n.debug("Setting 'paginationMeta' to",u),e.current.setState(g=>f({},g,{pagination:f({},g.pagination,{meta:u})})))},[e,n,o])};we(e,l,"public");const s=a.useCallback((u,g)=>{var S,y;const h=On(e);return!g.exportOnlyDirtyModels||t.paginationMeta!=null||((y=(S=t.initialState)==null?void 0:S.pagination)==null?void 0:y.meta)!=null?f({},u,{pagination:f({},u.pagination,{meta:h})}):u},[e,t.paginationMeta,(d=(c=t.initialState)==null?void 0:c.pagination)==null?void 0:d.meta]),i=a.useCallback((u,g)=>{var m;const h=(m=g.stateToRestore.pagination)!=null&&m.meta?g.stateToRestore.pagination.meta:On(e);return e.current.setState(S=>f({},S,{pagination:f({},S.pagination,{meta:h})})),u},[e]);_e(e,"exportState",s),_e(e,"restoreState",i),a.useEffect(()=>{t.paginationMeta&&e.current.setPaginationMeta(t.paginationMeta)},[e,t.paginationMeta])},ww=(e,t)=>{var l,s,i,c,d,u,g;const n=f({},hi(t.autoPageSize),t.paginationModel??((s=(l=t.initialState)==null?void 0:l.pagination)==null?void 0:s.paginationModel));mi(n.pageSize,t.signature);const o=t.rowCount??((c=(i=t.initialState)==null?void 0:i.pagination)==null?void 0:c.rowCount)??(t.paginationMode==="client"?(d=e.rows)==null?void 0:d.totalRowCount:void 0),r=t.paginationMeta??((g=(u=t.initialState)==null?void 0:u.pagination)==null?void 0:g.meta)??{};return f({},e,{pagination:f({},e.pagination,{paginationModel:n,rowCount:o,meta:r,enabled:t.pagination===!0,paginationMode:t.paginationMode})})},Sw=(e,t)=>{pw(e,t),rw(e,t),bw(e,t)},xw=(e,t)=>{var n;return f({},e,{preferencePanel:((n=t.initialState)==null?void 0:n.preferencePanel)??{open:!1}})},yw=(e,t)=>{var i;const n=Ze(e,"useGridPreferencesPanel"),o=a.useCallback(()=>{e.current.setState(c=>{if(!c.preferencePanel.open)return c;n.debug("Hiding Preferences Panel");const d=tn(e);return e.current.publishEvent("preferencePanelClose",{openedPanelValue:d.openedPanelValue}),f({},c,{preferencePanel:{open:!1}})})},[e,n]),r=a.useCallback((c,d,u)=>{n.debug("Opening Preferences Panel"),e.current.setState(g=>f({},g,{preferencePanel:f({},g.preferencePanel,{open:!0,openedPanelValue:c,panelId:d,labelId:u})})),e.current.publishEvent("preferencePanelOpen",{openedPanelValue:c})},[n,e]);we(e,{showPreferences:r,hidePreferences:o},"public");const l=a.useCallback((c,d)=>{var h;const u=tn(e);return!d.exportOnlyDirtyModels||((h=t.initialState)==null?void 0:h.preferencePanel)!=null||u.open?f({},c,{preferencePanel:u}):c},[e,(i=t.initialState)==null?void 0:i.preferencePanel]),s=a.useCallback((c,d)=>{const u=d.stateToRestore.preferencePanel;return u!=null&&e.current.setState(g=>f({},g,{preferencePanel:u})),c},[e]);_e(e,"exportState",l),_e(e,"restoreState",s)},gr=e=>{switch(e.type){case"boolean":return!1;case"date":case"dateTime":case"number":return;case"singleSelect":return null;case"string":default:return""}},vw=["id","field"],Iw=["id","field"],Mw=(e,t)=>{const[n,o]=a.useState({}),r=a.useRef(n),l=a.useRef({}),{processRowUpdate:s,onProcessRowUpdateError:i,cellModesModel:c,onCellModesModelChange:d}=t,u=F=>(...E)=>{t.editMode===Tt.Cell&&F(...E)},g=a.useCallback((F,E)=>{const P=e.current.getCellParams(F,E);if(!e.current.isCellEditable(P))throw new Error(`MUI X: The cell with id=${F} and field=${E} is not editable.`)},[e]),h=a.useCallback((F,E,P)=>{if(e.current.getCellMode(F,E)!==P)throw new Error(`MUI X: The cell with id=${F} and field=${E} is not in ${P} mode.`)},[e]),m=a.useCallback((F,E)=>{if(!F.isEditable||F.cellMode===Oe.Edit)return;const P=f({},F,{reason:Ot.cellDoubleClick});e.current.publishEvent("cellEditStart",P,E)},[e]),S=a.useCallback((F,E)=>{if(F.cellMode===Oe.View||e.current.getCellMode(F.id,F.field)===Oe.View)return;const P=f({},F,{reason:St.cellFocusOut});e.current.publishEvent("cellEditStop",P,E)},[e]),y=a.useCallback((F,E)=>{if(F.cellMode===Oe.Edit){if(E.which===229)return;let P;if(E.key==="Escape"?P=St.escapeKeyDown:E.key==="Enter"?P=St.enterKeyDown:E.key==="Tab"&&(P=E.shiftKey?St.shiftTabKeyDown:St.tabKeyDown,E.preventDefault()),P){const z=f({},F,{reason:P});e.current.publishEvent("cellEditStop",z,E)}}else if(F.isEditable){let P;if(!e.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:E,cellParams:F,editMode:"cell"}))return;if(Ki(E)?P=Ot.printableKeyDown:Xi(E)?P=Ot.pasteKeyDown:E.key==="Enter"?P=Ot.enterKeyDown:(E.key==="Backspace"||E.key==="Delete")&&(P=Ot.deleteKeyDown),P){const _=f({},F,{reason:P,key:E.key});e.current.publishEvent("cellEditStart",_,E)}}},[e]),p=a.useCallback(F=>{const{id:E,field:P,reason:z}=F,_={id:E,field:P};(z===Ot.printableKeyDown||z===Ot.deleteKeyDown||z===Ot.pasteKeyDown)&&(_.deleteValue=!0),e.current.startCellEditMode(_)},[e]),v=a.useCallback(F=>{const{id:E,field:P,reason:z}=F;e.current.runPendingEditCellValueMutation(E,P);let _;z===St.enterKeyDown?_="below":z===St.tabKeyDown?_="right":z===St.shiftTabKeyDown&&(_="left");const V=z==="escapeKeyDown";e.current.stopCellEditMode({id:E,field:P,ignoreModifications:V,cellToFocusAfter:_})},[e]),I=F=>async(...E)=>{var P;if(F){const{id:z,field:_}=E[0];((P=e.current.state.editRows[z][_])==null?void 0:P.error)||F(...E)}};le(e,"cellDoubleClick",u(m)),le(e,"cellFocusOut",u(S)),le(e,"cellKeyDown",u(y)),le(e,"cellEditStart",u(p)),le(e,"cellEditStop",u(v)),Le(e,"cellEditStart",t.onCellEditStart),Le(e,"cellEditStop",I(t.onCellEditStop));const D=a.useCallback((F,E)=>{const P=Ye(e);return P[F]&&P[F][E]?Oe.Edit:Oe.View},[e]),H=$e(F=>{const E=F!==t.cellModesModel;d&&E&&d(F,{api:e.current}),!(t.cellModesModel&&E)&&(o(F),r.current=F,e.current.publishEvent("cellModesModelChange",F))}),k=a.useCallback((F,E,P)=>{const z=f({},r.current);if(P!==null)z[F]=f({},z[F],{[E]:f({},P)});else{const _=z[F],V=Q(_,[E].map(Nn));z[F]=V,Object.keys(z[F]).length===0&&delete z[F]}H(z)},[H]),$=a.useCallback((F,E,P)=>{e.current.setState(z=>{const _=f({},z.editRows);return P!==null?_[F]=f({},_[F],{[E]:f({},P)}):(delete _[F][E],Object.keys(_[F]).length===0&&delete _[F]),f({},z,{editRows:_})})},[e]),L=a.useCallback(F=>{const{id:E,field:P}=F,z=Q(F,vw);g(E,P),h(E,P,Oe.View),k(E,P,f({mode:Oe.Edit},z))},[g,h,k]),x=$e(async F=>{const{id:E,field:P,deleteValue:z,initialValue:_}=F,V=e.current.getCellValue(E,P);let R=V;z?R=gr(e.current.getColumn(P)):_&&(R=_);const B=e.current.getColumn(P),q=!!B.preProcessEditCellProps&&z;let X={value:R,error:!1,isProcessingProps:q};if($(E,P,X),e.current.setCellFocus(E,P),q&&(X=await Promise.resolve(B.preProcessEditCellProps({id:E,row:e.current.getRow(E),props:X,hasChanged:R!==V})),e.current.getCellMode(E,P)===Oe.Edit)){const re=Ye(e);$(E,P,f({},X,{value:re[E][P].value,isProcessingProps:!1}))}}),w=a.useCallback(F=>{const{id:E,field:P}=F,z=Q(F,Iw);h(E,P,Oe.Edit),k(E,P,f({mode:Oe.View},z))},[h,k]),T=$e(async F=>{var Y;const{id:E,field:P,ignoreModifications:z,cellToFocusAfter:_="none"}=F;h(E,P,Oe.Edit),e.current.runPendingEditCellValueMutation(E,P);const V=()=>{$(E,P,null),k(E,P,null),_!=="none"&&e.current.moveFocusToRelativeCell(E,P,_)};if(z){V();return}const R=Ye(e),{error:B,isProcessingProps:q}=R[E][P],X=e.current.getRow(E);if(B||q){l.current[E][P].mode=Oe.Edit,k(E,P,{mode:Oe.Edit});return}const re=e.current.getRowWithUpdatedValuesFromCellEditing(E,P);if((Y=t.dataSource)!=null&&Y.updateRow){if(ct(X,re)){V();return}const ae=()=>{l.current[E][P].mode=Oe.Edit,k(E,P,{mode:Oe.Edit})},ce={rowId:E,updatedRow:re,previousRow:X};try{await e.current.dataSource.editRow(ce),V()}catch{ae()}}else if(s){const ae=ce=>{l.current[E][P].mode=Oe.Edit,k(E,P,{mode:Oe.Edit}),i&&i(ce)};try{Promise.resolve(s(re,X,{rowId:E})).then(ce=>{e.current.updateRows([ce]),V()}).catch(ae)}catch(ce){ae(ce)}}else e.current.updateRows([re]),V()}),M=a.useCallback(async F=>{var Y,ae;const{id:E,field:P,value:z,debounceMs:_,unstable_skipValueParser:V}=F;g(E,P),h(E,P,Oe.Edit);const R=e.current.getColumn(P),B=e.current.getRow(E);let q=z;R.valueParser&&!V&&(q=R.valueParser(z,B,R,e));let X=Ye(e),re=f({},X[E][P],{value:q,changeReason:_?"debouncedSetEditCellValue":"setEditCellValue"});if(R.preProcessEditCellProps){const ce=z!==X[E][P].value;re=f({},re,{isProcessingProps:!0}),$(E,P,re),re=await Promise.resolve(R.preProcessEditCellProps({id:E,row:B,props:re,hasChanged:ce}))}return e.current.getCellMode(E,P)===Oe.View?!1:(X=Ye(e),re=f({},re,{isProcessingProps:!1}),re.value=R.preProcessEditCellProps?X[E][P].value:q,$(E,P,re),X=Ye(e),!((ae=(Y=X[E])==null?void 0:Y[P])!=null&&ae.error))},[e,g,h,$]),O=a.useCallback((F,E)=>{const P=e.current.getColumn(E),z=Ye(e),_=e.current.getRow(F);if(!z[F]||!z[F][E])return e.current.getRow(F);const{value:V}=z[F][E];return P.valueSetter?P.valueSetter(V,_,P,e):f({},_,{[E]:V})},[e]),G={getCellMode:D,startCellEditMode:L,stopCellEditMode:w},A={setCellEditingEditCellValue:M,getRowWithUpdatedValuesFromCellEditing:O};we(e,G,"public"),we(e,A,"private"),a.useEffect(()=>{c&&H(c)},[c,H]),nt(()=>{const F=Ct(e),E=l.current;l.current=zs(n),Object.entries(n).forEach(([P,z])=>{Object.entries(z).forEach(([_,V])=>{var q,X;const R=((X=(q=E[P])==null?void 0:q[_])==null?void 0:X.mode)||Oe.View,B=F[P]?e.current.getRowId(F[P]):P;V.mode===Oe.Edit&&R===Oe.View?x(f({id:B,field:_},V)):V.mode===Oe.View&&R===Oe.Edit&&T(f({id:B,field:_},V))})})},[e,n,x,T])},Pw=["id"],Fw=["id"],Ew=(e,t)=>{const[n,o]=a.useState({}),r=a.useRef(n),l=a.useRef({}),s=a.useRef({}),i=a.useRef(void 0),c=a.useRef(null),{processRowUpdate:d,onProcessRowUpdateError:u,rowModesModel:g,onRowModesModelChange:h}=t,m=V=>(...R)=>{t.editMode===Tt.Row&&V(...R)},S=a.useCallback((V,R)=>{const B=e.current.getCellParams(V,R);if(!e.current.isCellEditable(B))throw new Error(`MUI X: The cell with id=${V} and field=${R} is not editable.`)},[e]),y=a.useCallback((V,R)=>{if(e.current.getRowMode(V)!==R)throw new Error(`MUI X: The row with id=${V} is not in ${R} mode.`)},[e]),p=a.useCallback(V=>{const R=Ye(e);return Object.values(R[V]).some(B=>B.error)},[e]),v=a.useCallback((V,R)=>{if(!V.isEditable||e.current.getRowMode(V.id)===Ve.Edit)return;const B=e.current.getRowParams(V.id),q=f({},B,{field:V.field,reason:Vt.cellDoubleClick});e.current.publishEvent("rowEditStart",q,R)},[e]),I=a.useCallback(V=>{c.current=V},[]),D=a.useCallback((V,R)=>{V.isEditable&&e.current.getRowMode(V.id)!==Ve.View&&(c.current=null,i.current=setTimeout(()=>{var B;if(((B=c.current)==null?void 0:B.id)!==V.id){if(!e.current.getRow(V.id)||e.current.getRowMode(V.id)===Ve.View||p(V.id))return;const q=e.current.getRowParams(V.id),X=f({},q,{field:V.field,reason:Ft.rowFocusOut});e.current.publishEvent("rowEditStop",X,R)}}))},[e,p]);a.useEffect(()=>()=>{clearTimeout(i.current)},[]);const H=a.useCallback((V,R)=>{if(V.cellMode===Ve.Edit){if(R.which===229)return;let B;if(R.key==="Escape")B=Ft.escapeKeyDown;else if(R.key==="Enter")B=Ft.enterKeyDown;else if(R.key==="Tab"){const q=In(e).filter(X=>e.current.getColumn(X).type===Eo?!0:e.current.isCellEditable(e.current.getCellParams(V.id,X)));if(R.shiftKey?V.field===q[0]&&(B=Ft.shiftTabKeyDown):V.field===q[q.length-1]&&(B=Ft.tabKeyDown),R.preventDefault(),!B){const X=q.findIndex(Y=>Y===V.field),re=q[R.shiftKey?X-1:X+1];e.current.setCellFocus(V.id,re)}}if(B){if(B!==Ft.escapeKeyDown&&p(V.id))return;const q=f({},e.current.getRowParams(V.id),{reason:B,field:V.field});e.current.publishEvent("rowEditStop",q,R)}}else if(V.isEditable){let B;if(!e.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:R,cellParams:V,editMode:"row"}))return;if(Ki(R)||Xi(R)?B=Vt.printableKeyDown:R.key==="Enter"?B=Vt.enterKeyDown:(R.key==="Backspace"||R.key==="Delete")&&(B=Vt.deleteKeyDown),B){const X=e.current.getRowParams(V.id),re=f({},X,{field:V.field,reason:B});e.current.publishEvent("rowEditStart",re,R)}}},[e,p]),k=a.useCallback(V=>{const{id:R,field:B,reason:q}=V,X={id:R,fieldToFocus:B};(q===Vt.printableKeyDown||q===Vt.deleteKeyDown)&&(X.deleteValue=!!B),e.current.startRowEditMode(X)},[e]),$=a.useCallback(V=>{const{id:R,reason:B,field:q}=V;e.current.runPendingEditCellValueMutation(R);let X;B===Ft.enterKeyDown?X="below":B===Ft.tabKeyDown?X="right":B===Ft.shiftTabKeyDown&&(X="left");const re=B==="escapeKeyDown";e.current.stopRowEditMode({id:R,ignoreModifications:re,field:q,cellToFocusAfter:X})},[e]);le(e,"cellDoubleClick",m(v)),le(e,"cellFocusIn",m(I)),le(e,"cellFocusOut",m(D)),le(e,"cellKeyDown",m(H)),le(e,"rowEditStart",m(k)),le(e,"rowEditStop",m($)),Le(e,"rowEditStart",t.onRowEditStart),Le(e,"rowEditStop",t.onRowEditStop);const L=a.useCallback(V=>Li(e,{rowId:V,editMode:t.editMode})?Ve.Edit:Ve.View,[e,t.editMode]),x=$e(V=>{const R=V!==t.rowModesModel;h&&R&&h(V,{api:e.current}),!(t.rowModesModel&&R)&&(o(V),r.current=V,e.current.publishEvent("rowModesModelChange",V))}),w=a.useCallback((V,R)=>{const B=f({},r.current);R!==null?B[V]=f({},R):delete B[V],x(B)},[x]),T=a.useCallback((V,R)=>{e.current.setState(B=>{const q=f({},B.editRows);return R!==null?q[V]=R:delete q[V],f({},B,{editRows:q})})},[e]),M=a.useCallback((V,R,B)=>{e.current.setState(q=>{const X=f({},q.editRows);return B!==null?X[V]=f({},X[V],{[R]:f({},B)}):(delete X[V][R],Object.keys(X[V]).length===0&&delete X[V]),f({},q,{editRows:X})})},[e]),O=a.useCallback(V=>{const{id:R}=V,B=Q(V,Pw);y(R,Ve.View),w(R,f({mode:Ve.Edit},B))},[y,w]),G=$e(V=>{const{id:R,fieldToFocus:B,deleteValue:q,initialValue:X}=V,re=e.current.getRow(R),Y=bt(e),ae=Y.reduce((ce,j)=>{const K=j.field;if(!e.current.getCellParams(R,K).isEditable)return ce;const ne=e.current.getColumn(K);let U=e.current.getCellValue(R,K);return B===K&&(q||X)&&(q?U=gr(ne):X&&(U=X)),ce[K]={value:U,error:!1,isProcessingProps:ne.editable&&!!ne.preProcessEditCellProps&&q},ce},{});s.current[R]=re,T(R,ae),B&&e.current.setCellFocus(R,B),Y.filter(ce=>ce.editable&&!!ce.preProcessEditCellProps&&q).forEach(ce=>{const j=ce.field,K=e.current.getCellValue(R,j),te=q?gr(ce):X??K;Promise.resolve(ce.preProcessEditCellProps({id:R,row:re,props:ae[j],hasChanged:te!==K})).then(ne=>{if(e.current.getRowMode(R)===Ve.Edit){const U=Ye(e);M(R,j,f({},ne,{value:U[R][j].value,isProcessingProps:!1}))}})})}),A=a.useCallback(V=>{const{id:R}=V,B=Q(V,Fw);y(R,Ve.Edit),w(R,f({mode:Ve.View},B))},[y,w]),F=$e(async V=>{var K;const{id:R,ignoreModifications:B,field:q,cellToFocusAfter:X="none"}=V;e.current.runPendingEditCellValueMutation(R);const re=()=>{X!=="none"&&q&&e.current.moveFocusToRelativeCell(R,q,X),T(R,null),w(R,null),delete s.current[R]};if(B){re();return}const Y=Ye(e),ae=s.current[R];if(Object.values(Y[R]).some(te=>te.isProcessingProps)){l.current[R].mode=Ve.Edit;return}if(p(R)){l.current[R].mode=Ve.Edit,w(R,{mode:Ve.Edit});return}const j=e.current.getRowWithUpdatedValuesFromRowEditing(R);if((K=t.dataSource)!=null&&K.updateRow){if(ct(ae,j)){re();return}const te=()=>{l.current[R].mode=Ve.Edit,w(R,{mode:Ve.Edit})},ne={rowId:R,updatedRow:j,previousRow:ae};try{await e.current.dataSource.editRow(ne),re()}catch{te()}}else if(d){const te=ne=>{l.current[R]&&(l.current[R].mode=Ve.Edit,w(R,{mode:Ve.Edit})),u&&u(ne)};try{Promise.resolve(d(j,ae,{rowId:R})).then(ne=>{e.current.updateRows([ne]),re()}).catch(te)}catch(ne){te(ne)}}else e.current.updateRows([j]),re()}),E=a.useCallback(V=>{const{id:R,field:B,value:q,debounceMs:X,unstable_skipValueParser:re}=V;S(R,B);const Y=e.current.getColumn(B),ae=e.current.getRow(R);let ce=q;Y.valueParser&&!re&&(ce=Y.valueParser(q,ae,Y,e));let j=Ye(e),K=f({},j[R][B],{value:ce,changeReason:X?"debouncedSetEditCellValue":"setEditCellValue"});return Y.preProcessEditCellProps||M(R,B,K),new Promise(te=>{const ne=[];if(Y.preProcessEditCellProps){const U=K.value!==j[R][B].value;K=f({},K,{isProcessingProps:!0}),M(R,B,K);const J=j[R],ie=Q(J,[B].map(Nn)),fe=Promise.resolve(Y.preProcessEditCellProps({id:R,row:ae,props:K,hasChanged:U,otherFieldsProps:ie})).then(he=>{if(e.current.getRowMode(R)===Ve.View){te(!1);return}j=Ye(e),he=f({},he,{isProcessingProps:!1}),he.value=Y.preProcessEditCellProps?j[R][B].value:ce,M(R,B,he)});ne.push(fe)}Object.entries(j[R]).forEach(([U,J])=>{if(U===B)return;const ie=e.current.getColumn(U);if(!ie.preProcessEditCellProps)return;J=f({},J,{isProcessingProps:!0}),M(R,U,J),j=Ye(e);const fe=j[R],he=Q(fe,[U].map(Nn)),ge=Promise.resolve(ie.preProcessEditCellProps({id:R,row:ae,props:J,hasChanged:!1,otherFieldsProps:he})).then(be=>{if(e.current.getRowMode(R)===Ve.View){te(!1);return}be=f({},be,{isProcessingProps:!1}),M(R,U,be)});ne.push(ge)}),Promise.all(ne).then(()=>{e.current.getRowMode(R)===Ve.Edit?(j=Ye(e),te(!j[R][B].error)):te(!1)})})},[e,S,M]),P=a.useCallback(V=>{const R=Ye(e),B=e.current.getRow(V);if(!R[V])return e.current.getRow(V);let q=f({},s.current[V],B);return Object.entries(R[V]).forEach(([X,re])=>{const Y=e.current.getColumn(X);Y!=null&&Y.valueSetter?q=Y.valueSetter(re.value,q,Y,e):q[X]=re.value}),q},[e]),z={getRowMode:L,startRowEditMode:O,stopRowEditMode:A},_={setRowEditingEditCellValue:E,getRowWithUpdatedValuesFromRowEditing:P};we(e,z,"public"),we(e,_,"private"),a.useEffect(()=>{g&&x(g)},[g,x]),nt(()=>{const V=Ct(e),R=l.current;l.current=zs(n);const B=new Set([...Object.keys(n),...Object.keys(R)]);Array.from(B).forEach(q=>{var ae;const X=n[q]??{mode:Ve.View},re=((ae=R[q])==null?void 0:ae.mode)||Ve.View,Y=V[q]?e.current.getRowId(V[q]):q;X.mode===Ve.Edit&&re===Ve.View?G(f({id:Y},X)):X.mode===Ve.View&&re===Ve.Edit&&F(f({id:Y},X))})},[e,n,G,F])},kw=e=>f({},e,{editRows:{}}),Tw=(e,t)=>{Mw(e,t),Ew(e,t);const n=a.useRef({}),{isCellEditable:o}=t,r=a.useCallback(h=>bn(h.rowNode)||!h.colDef.editable||!h.colDef.renderEditCell?!1:o?o(h):!0,[o]),l=(h,m,S,y)=>{if(!S){y();return}if(n.current[h]||(n.current[h]={}),n.current[h][m]){const[I]=n.current[h][m];clearTimeout(I)}const p=()=>{const[I]=n.current[h][m];clearTimeout(I),y(),delete n.current[h][m]},v=setTimeout(()=>{y(),delete n.current[h][m]},S);n.current[h][m]=[v,p]};a.useEffect(()=>{const h=n.current;return()=>{Object.entries(h).forEach(([m,S])=>{Object.keys(S).forEach(y=>{const[p]=h[m][y];clearTimeout(p),delete h[m][y]})})}},[]);const s=a.useCallback((h,m)=>{if(n.current[h]){if(!m)Object.keys(n.current[h]).forEach(S=>{const[,y]=n.current[h][S];y()});else if(n.current[h][m]){const[,S]=n.current[h][m];S()}}},[]),i=a.useCallback(h=>{const{id:m,field:S,debounceMs:y}=h;return new Promise(p=>{l(m,S,y,async()=>{const v=t.editMode===Tt.Row?e.current.setRowEditingEditCellValue:e.current.setCellEditingEditCellValue;if(e.current.getCellMode(m,S)===Oe.Edit){const I=await v(h);p(I)}})})},[e,t.editMode]),c=a.useCallback((h,m)=>t.editMode===Tt.Cell?e.current.getRowWithUpdatedValuesFromCellEditing(h,m):e.current.getRowWithUpdatedValuesFromRowEditing(h),[e,t.editMode]),d=a.useCallback((h,m)=>{var y;return((y=Ye(e)[h])==null?void 0:y[m])??null},[e]),u={isCellEditable:r,setEditCellValue:i,getRowWithUpdatedValues:c,unstable_getEditCellMeta:d},g={runPendingEditCellValueMutation:s};we(e,u,"public"),we(e,g,"private")},Dw=(e,t,n)=>{const o=!!t.dataSource;return n.current.caches.rows=co({rows:o?[]:t.rows,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),f({},e,{rows:Js({apiRef:n,rowCountProp:t.rowCount,loadingProp:o?!0:t.loading,previousTree:null,previousTreeDepths:null})})},Hw=(e,t)=>{const n=Ze(e,"useGridRows"),o=a.useRef(Date.now()),r=a.useRef(t.rowCount),l=Sn(),s=a.useCallback(F=>{const E=Ct(e)[F];if(E)return E;const P=Et(e,F);return P&&bn(P)?{[Cn]:F}:null},[e]),i=a.useCallback(F=>Xn(e,F),[e]),c=a.useCallback(({cache:F,throttle:E})=>{const P=()=>{o.current=Date.now(),e.current.setState(_=>f({},_,{rows:Js({apiRef:e,rowCountProp:t.rowCount,loadingProp:t.loading,previousTree:tt(e),previousTreeDepths:pl(e),previousGroupsToFetch:sd(e)})})),e.current.publishEvent("rowsSet")};if(l.clear(),e.current.caches.rows=F,!E){P();return}const z=t.throttleRowsMs-(Date.now()-o.current);if(z>0){l.start(z,P);return}P()},[t.throttleRowsMs,t.rowCount,t.loading,e,l]),d=a.useCallback(F=>{if(n.debug(`Updating all rows, new length ${F.length}`),en(e)){e.current.updateNonPivotRows(F,!1);return}const E=co({rows:F,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),P=e.current.caches.rows;E.rowsBeforePartialUpdates=P.rowsBeforePartialUpdates,c({cache:E,throttle:!0})},[n,t.getRowId,t.loading,t.rowCount,c,e]),u=a.useCallback(F=>{if(t.signature===vt.DataGrid&&F.length>1)throw new Error(["MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join(`
`));if(en(e)){e.current.updateNonPivotRows(F);return}const E=kl(e,F,t.getRowId),P=El({updates:E,getRowId:t.getRowId,previousCache:e.current.caches.rows});c({cache:P,throttle:!0})},[t.signature,t.getRowId,c,e]),g=a.useCallback((F,E)=>{const P=kl(e,F,t.getRowId),z=El({updates:P,getRowId:t.getRowId,previousCache:e.current.caches.rows,groupKeys:E??[]});c({cache:z,throttle:!1})},[t.getRowId,c,e]),h=a.useCallback(F=>{n.debug(`Setting loading to ${F}`),e.current.setState(E=>f({},E,{rows:f({},E.rows,{loading:F})})),e.current.caches.rows.loadingPropBeforePartialUpdates=F},[e,n]),m=a.useCallback(()=>{const F=Yt(e),E=Ct(e);return new Map(F.map(P=>[P,E[P]??{}]))},[e]),S=a.useCallback(()=>_n(e),[e]),y=a.useCallback(()=>Yt(e),[e]),p=a.useCallback(F=>{const{rowIdToIndexMap:E}=Ut(e);return E.get(F)},[e]),v=a.useCallback((F,E)=>{const P=Et(e,F);if(!P)throw new Error(`MUI X: No row with id #${F} found.`);if(P.type!=="group")throw new Error("MUI X: Only group nodes can be expanded or collapsed.");const z=f({},P,{childrenExpanded:E});e.current.setState(_=>f({},_,{rows:f({},_.rows,{tree:f({},_.rows.tree,{[F]:z})})})),e.current.publishEvent("rowExpansionChange",z)},[e]),I=a.useCallback(F=>Et(e,F)??null,[e]),D=a.useCallback(({skipAutoGeneratedRows:F=!0,groupId:E,applySorting:P,applyFiltering:z})=>{const _=tt(e);let V;if(P){const R=_[E];if(!R)return[];const B=Wn(e);V=[];const q=B.findIndex(X=>X===E)+1;for(let X=q;X<B.length&&_[B[X]].depth>R.depth;X+=1){const re=B[X];(!F||!bn(_[re]))&&V.push(re)}}else V=Fr(_,E,F);if(z){const R=Pn(e);V=Do(R)?V:V.filter(B=>R[B]!==!1)}return V},[e]),H=a.useCallback((F,E)=>{const P=Et(e,F);if(!P)throw new Error(`MUI X: No row with id #${F} found.`);if(P.parent!==et)throw new Error("MUI X: The row reordering do not support reordering of grouped rows yet.");if(P.type!=="leaf")throw new Error("MUI X: The row reordering do not support reordering of footer or grouping rows.");e.current.setState(z=>{const _=tt(e)[et],V=_.children,R=V.findIndex(q=>q===F);if(R===-1||R===E)return z;n.debug(`Moving row ${F} to index ${E}`);const B=[...V];return B.splice(E,0,B.splice(R,1)[0]),f({},z,{rows:f({},z.rows,{tree:f({},z.rows.tree,{[et]:f({},_,{children:B})})})})}),e.current.publishEvent("rowsSet")},[e,n]),k=a.useCallback((F,E)=>{if(t.signature===vt.DataGrid&&E.length>1)throw new Error(["MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join(`
`));if(E.length===0)return;if(yn(e)>1)throw new Error("`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping");const z=f({},tt(e)),_=f({},Ct(e)),V=z[et],R=[...V.children],B=new Set;for(let X=0;X<E.length;X+=1){const re=E[X],Y=ko(re,t.getRowId,"A row was provided without id when calling replaceRows()."),[ae]=R.splice(F+X,1,Y);B.has(ae)||(delete _[ae],delete z[ae]);const ce={id:Y,depth:0,parent:et,type:"leaf",groupingKey:null};_[Y]=re,z[Y]=ce,B.add(Y)}z[et]=f({},V,{children:R});const q=R.filter(X=>{var re;return((re=z[X])==null?void 0:re.type)==="leaf"});e.current.caches.rows.dataRowIdToModelLookup=_,e.current.setState(X=>f({},X,{rows:f({},X.rows,{loading:t.loading,totalRowCount:Math.max(t.rowCount||0,R.length),dataRowIdToModelLookup:_,dataRowIds:q,tree:z})})),e.current.publishEvent("rowsSet")},[e,t.signature,t.getRowId,t.loading,t.rowCount]),$={getRow:s,setLoading:h,getRowId:i,getRowModels:m,getRowsCount:S,getAllRowIds:y,setRows:d,updateRows:u,getRowNode:I,getRowIndexRelativeToVisibleRows:p,unstable_replaceRows:k},L={setRowIndex:H,setRowChildrenExpansion:v,getRowGroupChildren:D},x={updateNestedRows:g},w=a.useCallback(()=>{n.info("Row grouping pre-processing have changed, regenerating the row tree");let F;e.current.caches.rows.rowsBeforePartialUpdates===t.rows?F=f({},e.current.caches.rows,{updates:{type:"full",rows:Yt(e)}}):F=co({rows:t.rows,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),c({cache:F,throttle:!1})},[n,e,t.rows,t.getRowId,t.loading,t.rowCount,c]),T=Gt(()=>t.dataSource),M=a.useCallback(F=>{if(t.dataSource&&t.dataSource!==T.current){T.current=t.dataSource;return}F==="rowTreeCreation"&&w()},[w,T,t.dataSource]),O=a.useCallback(()=>{e.current.getActiveStrategy(Wt.RowTree)!==id(e)&&w()},[e,w]);le(e,"activeStrategyProcessorChange",M),le(e,"strategyAvailabilityChange",O);const G=a.useCallback(()=>{e.current.setState(F=>{const E=e.current.unstable_applyPipeProcessors("hydrateRows",{tree:tt(e),treeDepths:pl(e),dataRowIds:Yt(e),dataRowIdToModelLookup:Ct(e)});return f({},F,{rows:f({},F.rows,E,{totalTopLevelRowCount:Zs({tree:E.tree,rowCountProp:t.rowCount})})})}),e.current.publishEvent("rowsSet")},[e,t.rowCount]);_r(e,"hydrateRows",G),we(e,$,"public"),we(e,L,t.signature===vt.DataGrid?"private":"public"),we(e,x,"private");const A=a.useRef(!0);a.useEffect(()=>{if(A.current){A.current=!1;return}let F=!1;t.rowCount!==r.current&&(F=!0,r.current=t.rowCount);const E=t.dataSource?cd(e):t.rows,P=e.current.caches.rows.rowsBeforePartialUpdates===E,z=e.current.caches.rows.loadingPropBeforePartialUpdates===t.loading,_=e.current.caches.rows.rowCountPropBeforePartialUpdates===t.rowCount;P&&(z||(e.current.setState(V=>f({},V,{rows:f({},V.rows,{loading:t.loading})})),e.current.caches.rows.loadingPropBeforePartialUpdates=t.loading),_||(e.current.setState(V=>f({},V,{rows:f({},V.rows,{totalRowCount:Math.max(t.rowCount||0,V.rows.totalRowCount),totalTopLevelRowCount:Math.max(t.rowCount||0,V.rows.totalTopLevelRowCount)})})),e.current.caches.rows.rowCountPropBeforePartialUpdates=t.rowCount),!F)||(n.debug(`Updating all rows, new length ${E==null?void 0:E.length}`),c({cache:co({rows:E,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),throttle:!1}))},[t.rows,t.rowCount,t.getRowId,t.loading,t.dataSource,n,c,e])},Ow=e=>{const t={[et]:f({},Kd(),{children:e})};for(let n=0;n<e.length;n+=1){const o=e[n];t[o]={id:o,depth:0,parent:et,type:"leaf",groupingKey:null}}return{groupingName:nn,tree:t,treeDepths:{0:e.length},dataRowIds:e}},Gw=({previousTree:e,actions:t})=>{const n=f({},e),o={};for(let s=0;s<t.remove.length;s+=1){const i=t.remove[s];o[i]=!0,delete n[i]}for(let s=0;s<t.insert.length;s+=1){const i=t.insert[s];n[i]={id:i,depth:0,parent:et,type:"leaf",groupingKey:null}}const r=n[et];let l=[...r.children,...t.insert];return Object.values(o).length&&(l=l.filter(s=>!o[s])),n[et]=f({},r,{children:l}),{groupingName:nn,tree:n,treeDepths:{0:l.length},dataRowIds:l}},Lw=e=>e.updates.type==="full"?Ow(e.updates.rows):Gw({previousTree:e.previousTree,actions:e.updates.actions}),$w=e=>{Bn(e,nn,"rowTreeCreation",Lw)};class tr extends Error{}function Aw(e,t){var S;const n=a.useCallback(y=>({field:y,colDef:e.current.getColumn(y)}),[e]),o=a.useCallback(y=>{const p=e.current.getRow(y);if(!p)throw new tr(`No row with id #${y} found`);return{id:y,columns:e.current.getAllColumns(),row:p}},[e]),r=a.useCallback((y,p,v,{cellMode:I,colDef:D,hasFocus:H,rowNode:k,tabIndex:$})=>{const L=v[p],x=D!=null&&D.valueGetter?D.valueGetter(L,v,D,e):L,w={id:y,field:p,row:v,rowNode:k,colDef:D,cellMode:I,hasFocus:H,tabIndex:$,value:x,formattedValue:x,isEditable:!1,api:e.current};return D&&D.valueFormatter&&(w.formattedValue=D.valueFormatter(x,v,D,e)),w.isEditable=D&&e.current.isCellEditable(w),w},[e]),l=a.useCallback((y,p)=>{var $;const v=e.current.getRow(y),I=Et(e,y);if(!v||!I)throw new tr(`No row with id #${y} found`);const D=st(e),H=Or(e),k=e.current.getCellMode(y,p);return e.current.getCellParamsForRow(y,p,v,{colDef:t.listView&&(($=t.listViewColumn)==null?void 0:$.field)===p?wn(e):e.current.getColumn(p),rowNode:I,hasFocus:D!==null&&D.field===p&&D.id===y,tabIndex:H&&H.field===p&&H.id===y?0:-1,cellMode:k})},[e,t.listView,(S=t.listViewColumn)==null?void 0:S.field]),s=a.useCallback((y,p)=>{const v=e.current.getColumn(p),I=e.current.getRow(y);if(!I)throw new tr(`No row with id #${y} found`);return!v||!v.valueGetter?I[p]:v.valueGetter(I[v.field],I,v,e)},[e]),i=a.useCallback((y,p)=>Xd(y,p,e),[e]),c=a.useCallback((y,p)=>{const v=i(y,p);return!p||!p.valueFormatter?v:p.valueFormatter(v,y,p,e)},[e,i]),d=a.useCallback(y=>e.current.rootElementRef.current?Im(e.current.rootElementRef.current,y):null,[e]),u=a.useCallback(y=>e.current.rootElementRef.current?Mm(e.current.rootElementRef.current,y):null,[e]),g=a.useCallback((y,p)=>e.current.rootElementRef.current?Pm(e.current.rootElementRef.current,{id:y,field:p}):null,[e]),h={getCellValue:s,getCellParams:l,getCellElement:g,getRowValue:i,getRowFormattedValue:c,getRowParams:o,getRowElement:u,getColumnHeaderParams:n,getColumnHeaderElement:d},m={getCellParamsForRow:r};we(e,h,"public"),we(e,m,"private")}const An={type:"include",ids:new Set},Rw=(e,t)=>f({},e,{rowSelection:t.rowSelection?t.rowSelectionModel??An:An}),zw=(e,t)=>{var V,R,B,q,X,re,Y,ae,ce;const n=Ze(e,"useGridSelection"),o=a.useCallback(j=>(...K)=>{t.rowSelection&&j(...K)},[t.rowSelection]),r=t.signature!==vt.DataGrid&&(((V=t.rowSelectionPropagation)==null?void 0:V.parents)||((R=t.rowSelectionPropagation)==null?void 0:R.descendants)),l=a.useMemo(()=>t.rowSelectionModel,[t.rowSelectionModel]),s=a.useRef(null);e.current.registerControlState({stateId:"rowSelection",propModel:l,propOnChange:t.onRowSelectionModelChange,stateSelector:xt,changeEvent:"rowSelectionChange"});const{checkboxSelection:i,disableRowSelectionOnClick:c,isRowSelectable:d}=t,u=Hr(t),g=W(e,tt),h=W(e,yn)>1,m=a.useCallback(j=>{let K=j;const te=s.current??j,ne=e.current.isRowSelected(j);if(ne){const U=mn(e),J=U.findIndex(fe=>fe===te),ie=U.findIndex(fe=>fe===K);if(J===ie)return;J>ie?K=U[ie+1]:K=U[ie-1]}s.current=j,e.current.selectRowRange({startId:te,endId:K},!ne)},[e]),S=$e(()=>t.pagination&&t.checkboxSelectionVisibleOnly&&t.paginationMode==="client"?wi(e):mn(e)),y=a.useCallback((j,K)=>{if(t.signature===vt.DataGrid&&!u&&(j.type!=="include"||j.ids.size>1))throw new Error(["MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection."].join(`
`));xt(e)!==j&&(n.debug("Setting selection model"),e.current.setState(ne=>f({},ne,{rowSelection:t.rowSelection?j:An}),K))},[e,n,t.rowSelection,t.signature,u]),p=a.useCallback(j=>Un(e).has(j),[e]),v=a.useCallback(j=>{if(t.rowSelection===!1||d&&!d(e.current.getRowParams(j)))return!1;const K=Et(e,j);return!((K==null?void 0:K.type)==="footer"||(K==null?void 0:K.type)==="pinnedRow")},[e,t.rowSelection,d]),I=a.useCallback(()=>ui(e),[e]),D=a.useCallback((j,K=!0,te=!1)=>{var ne,U,J,ie,fe,he;if(e.current.isRowSelectable(j))if(s.current=j,te){n.debug(`Setting selection for row ${j}`);const ge={type:"include",ids:new Set},be=Me=>{ge.ids.add(Me)};K&&(be(j),r&&Tn(e,g,j,((ne=t.rowSelectionPropagation)==null?void 0:ne.descendants)??!1,((U=t.rowSelectionPropagation)==null?void 0:U.parents)??!1,be)),e.current.setRowSelectionModel(ge,"singleRowSelection")}else{n.debug(`Toggling selection for row ${j}`);const ge=xt(e),be={type:ge.type,ids:new Set(ge.ids)},Me=_t(be);Me.unselect(j);const De=Se=>{Me.select(Se)},se=Se=>{Me.unselect(Se)};K?(De(j),r&&Tn(e,g,j,((J=t.rowSelectionPropagation)==null?void 0:J.descendants)??!1,((ie=t.rowSelectionPropagation)==null?void 0:ie.parents)??!1,De)):r&&Tl(e,g,j,((fe=t.rowSelectionPropagation)==null?void 0:fe.descendants)??!1,((he=t.rowSelectionPropagation)==null?void 0:he.parents)??!1,se),(be.type==="include"&&be.ids.size<2||u)&&e.current.setRowSelectionModel(be,"singleRowSelection")}},[e,n,r,g,(B=t.rowSelectionPropagation)==null?void 0:B.descendants,(q=t.rowSelectionPropagation)==null?void 0:q.parents,u]),H=a.useCallback((j,K=!0,te=!1)=>{var fe,he,ge,be,Me,De;if(n.debug("Setting selection for several rows"),t.rowSelection===!1)return;const ne=new Set;for(let se=0;se<j.length;se+=1){const Ce=j[se];e.current.isRowSelectable(Ce)&&ne.add(Ce)}const U=xt(e);let J;if(te){if(J={type:"include",ids:ne},K){const se=_t(J);if(r){const Ce=Se=>{se.select(Se)};for(const Se of ne)Tn(e,g,Se,((fe=t.rowSelectionPropagation)==null?void 0:fe.descendants)??!1,((he=t.rowSelectionPropagation)==null?void 0:he.parents)??!1,Ce)}}else J.ids=new Set;if(U.type===J.type&&J.ids.size===U.ids.size&&Array.from(J.ids).every(se=>U.ids.has(se)))return}else{J={type:U.type,ids:new Set(U.ids)};const se=_t(J),Ce=Pe=>{se.select(Pe)},Se=Pe=>{se.unselect(Pe)};for(const Pe of ne)K?(se.select(Pe),r&&Tn(e,g,Pe,((ge=t.rowSelectionPropagation)==null?void 0:ge.descendants)??!1,((be=t.rowSelectionPropagation)==null?void 0:be.parents)??!1,Ce)):(Se(Pe),r&&Tl(e,g,Pe,((Me=t.rowSelectionPropagation)==null?void 0:Me.descendants)??!1,((De=t.rowSelectionPropagation)==null?void 0:De.parents)??!1,Se))}(J.type==="include"&&J.ids.size<2||u)&&e.current.setRowSelectionModel(J,"multipleRowsSelection")},[n,r,u,e,g,(X=t.rowSelectionPropagation)==null?void 0:X.descendants,(re=t.rowSelectionPropagation)==null?void 0:re.parents,t.rowSelection]),k=a.useCallback(j=>{var U,J;if(!h||!r||j.ids.size===0&&j.type==="include")return j;const K={type:j.type,ids:new Set(j.ids)},te=_t(K),ne=ie=>{te.select(ie)};for(const ie of j.ids)Tn(e,g,ie,((U=t.rowSelectionPropagation)==null?void 0:U.descendants)??!1,((J=t.rowSelectionPropagation)==null?void 0:J.parents)??!1,ne,te);return K},[e,g,(Y=t.rowSelectionPropagation)==null?void 0:Y.descendants,(ae=t.rowSelectionPropagation)==null?void 0:ae.parents,h,r]),$=a.useCallback(({startId:j,endId:K},te=!0,ne=!1)=>{if(!e.current.getRow(j)||!e.current.getRow(K))return;n.debug(`Expanding selection from row ${j} to row ${K}`);const U=mn(e),J=U.indexOf(j),ie=U.indexOf(K),[fe,he]=J>ie?[ie,J]:[J,ie],ge=U.slice(fe,he+1);e.current.selectRows(ge,te,ne)},[e,n]),L={selectRow:D,setRowSelectionModel:y,getSelectedRows:I,isRowSelected:p,isRowSelectable:v},x={selectRows:H,selectRowRange:$,getPropagatedRowSelectionModel:k};we(e,L,"public"),we(e,x,t.signature===vt.DataGrid?"private":"public");const w=a.useRef(!0),T=a.useCallback((j=!1)=>{var ge,be;if(w.current)return;const K=xt(e),te=Ct(e),ne=Pn(e),U=Me=>t.filterMode==="server"?!te[Me]:!te[Me]||ne[Me]===!1,J={type:K.type,ids:new Set(K.ids)},ie=_t(J);let fe=!1;for(const Me of K.ids){if(U(Me)){if(t.keepNonExistentRowsSelected)continue;ie.unselect(Me),fe=!0;continue}if(!((ge=t.rowSelectionPropagation)!=null&&ge.parents))continue;const De=g[Me];if((De==null?void 0:De.type)==="group"){if(De.isAutoGenerated){ie.unselect(Me),fe=!0;continue}De.children.every(Ce=>ne[Ce]===!1)||(ie.unselect(Me),fe=!0)}}const he=h&&((be=t.rowSelectionPropagation)==null?void 0:be.parents)&&(J.ids.size>0||J.type==="exclude");if(fe||he&&!j)if(he)if(J.type==="exclude"){const Me=S(),De=[];for(let se=0;se<Me.length;se+=1){const Ce=Me[se];(t.keepNonExistentRowsSelected||!U(Ce))&&ie.has(Ce)&&De.push(Ce)}e.current.selectRows(De,!0,!0)}else e.current.selectRows(Array.from(J.ids),!0,!0);else e.current.setRowSelectionModel(J,"multipleRowsSelection")},[e,h,(ce=t.rowSelectionPropagation)==null?void 0:ce.parents,t.keepNonExistentRowsSelected,t.filterMode,g,S]),M=a.useCallback((j,K)=>{const te=K.metaKey||K.ctrlKey,ne=!i&&!te&&!gC(K),U=!u||ne,J=e.current.isRowSelected(j),he=Oo(e)>1&&U||!J;e.current.selectRow(j,he,U)},[e,u,i]),O=a.useCallback((j,K)=>{var U;if(c)return;const te=(U=K.target.closest(`.${b.cell}`))==null?void 0:U.getAttribute("data-field");if(te===Fn.field||te===To)return;if(te){const J=e.current.getColumn(te);if((J==null?void 0:J.type)===Eo)return}Et(e,j.id).type!=="pinnedRow"&&(K.shiftKey&&u?m(j.id):M(j.id,K))},[c,u,e,m,M]),G=a.useCallback((j,K)=>{var te;u&&K.shiftKey&&((te=window.getSelection())==null||te.removeAllRanges())},[u]),A=a.useCallback((j,K)=>{u&&K.nativeEvent.shiftKey?m(j.id):e.current.selectRow(j.id,j.value,!u)},[e,m,u]),F=a.useCallback(j=>{const K=Qe(e),te=li(e),ne=K.items.length>0||(te==null?void 0:te.some(U=>U.length));!t.isRowSelectable&&!t.checkboxSelectionVisibleOnly&&r&&!ne?e.current.setRowSelectionModel({type:j?"exclude":"include",ids:new Set}):e.current.selectRows(S(),j)},[e,r,S,t.checkboxSelectionVisibleOnly,t.isRowSelectable]),E=a.useCallback(j=>{F(j.value)},[F]),P=a.useCallback((j,K)=>{if(e.current.getCellMode(j.id,j.field)!==Oe.Edit&&!eo(K)){if(xo(K.key)&&K.shiftKey){const te=st(e);if(te&&te.id!==j.id){K.preventDefault();const ne=e.current.isRowSelected(te.id);if(!u){e.current.selectRow(te.id,!ne,!0);return}const U=e.current.getRowIndexRelativeToVisibleRows(te.id),J=e.current.getRowIndexRelativeToVisibleRows(j.id);let ie,fe;U>J?ne?(ie=J,fe=U-1):(ie=J,fe=U):ne?(ie=U+1,fe=J):(ie=U,fe=J);const he=Ut(e),ge=[];for(let be=ie;be<=fe;be+=1)ge.push(he.rows[be].id);e.current.selectRows(ge,!ne);return}}if(K.key===" "&&K.shiftKey){K.preventDefault(),M(j.id,K);return}String.fromCharCode(K.keyCode)==="A"&&(K.ctrlKey||K.metaKey)&&(K.preventDefault(),F(!0))}},[e,u,M,F]),z=$e(()=>{if(!t.rowSelection){e.current.setRowSelectionModel(An);return}if(l===void 0)return;if(!r||!h||l.type==="include"&&l.ids.size===0){e.current.setRowSelectionModel(l);return}const j=e.current.getPropagatedRowSelectionModel(l);if(j.type!==l.type||j.ids.size!==l.ids.size||!Array.from(l.ids).every(K=>j.ids.has(K))){e.current.setRowSelectionModel(j);return}e.current.setRowSelectionModel(l)});le(e,"sortedRowsSet",o(()=>T(!0))),le(e,"filteredRowsSet",o(()=>T())),le(e,"rowClick",o(O)),le(e,"rowSelectionCheckboxChange",o(A)),le(e,"headerSelectionCheckboxChange",E),le(e,"cellMouseDown",o(G)),le(e,"cellKeyDown",o(P)),a.useEffect(()=>{z()},[e,l,t.rowSelection,z]);const _=l!=null;a.useEffect(()=>{if(_||!t.rowSelection||typeof v!="function")return;const j=xt(e);if(j.type!=="include")return;const K=new Set;for(const te of j.ids)v(te)&&K.add(te);K.size<j.ids.size&&e.current.setRowSelectionModel({type:j.type,ids:K})},[e,v,_,t.rowSelection]),a.useEffect(()=>{if(!t.rowSelection||_)return;const j=xt(e);!u&&(j.type==="include"&&j.ids.size>1||j.type==="exclude")&&e.current.setRowSelectionModel(An)},[e,u,i,_,t.rowSelection]),a.useEffect(()=>{o(T)},[T,o]),a.useEffect(()=>{w.current&&(w.current=!1)},[])},Vw=e=>{const{classes:t}=e;return a.useMemo(()=>xe({cellCheckbox:["cellCheckbox"],columnHeaderCheckbox:["columnHeaderCheckbox"]},ye,t),[t])},Nw=(e,t)=>{const n={classes:t.classes},o=Vw(n),r=a.useCallback(l=>{const s=f({},Fn,{cellClassName:o.cellCheckbox,headerClassName:o.columnHeaderCheckbox,headerName:e.current.getLocaleText("checkboxSelectionHeaderName")}),i=t.checkboxSelection,c=l.lookup[ft]!=null;return i&&!c?(l.lookup[ft]=s,l.orderedFields=[ft,...l.orderedFields]):!i&&c?(delete l.lookup[ft],l.orderedFields=l.orderedFields.filter(d=>d!==ft)):i&&c&&(l.lookup[ft]=f({},s,l.lookup[ft]),t.columns.some(d=>d.field===ft)||(l.orderedFields=[ft,...l.orderedFields.filter(d=>d!==ft)])),l},[e,o,t.columns,t.checkboxSelection]);_e(e,"hydrateColumns",r)},Bw=(e,t)=>{var o,r;const n=t.sortModel??((r=(o=t.initialState)==null?void 0:o.sorting)==null?void 0:r.sortModel)??[];return f({},e,{sorting:{sortModel:$s(n,t.disableMultipleColumnsSorting),sortedRows:[]}})},jw=(e,t)=>{var k,$;const n=Ze(e,"useGridSorting");e.current.registerControlState({stateId:"sortModel",propModel:t.sortModel,propOnChange:t.onSortModelChange,stateSelector:ht,changeEvent:"sortModelChange"});const o=a.useCallback((L,x)=>{const w=ht(e),T=w.findIndex(O=>O.field===L);let M=[...w];return T>-1?(x==null?void 0:x.sort)==null?M.splice(T,1):M.splice(T,1,x):M=[...w,x],M},[e]),r=a.useCallback((L,x)=>{const T=ht(e).find(M=>M.field===L.field);if(T){const M=x===void 0?Sl(L.sortingOrder??t.sortingOrder,T.sort):x;return M===void 0?void 0:f({},T,{sort:M})}return{field:L.field,sort:x===void 0?Sl(L.sortingOrder??t.sortingOrder):x}},[e,t.sortingOrder]),l=a.useCallback((L,x)=>x==null||x.sortable===!1||t.disableColumnSorting?L:(x.sortingOrder||t.sortingOrder).some(T=>!!T)?[...L,"columnMenuSortItem"]:L,[t.sortingOrder,t.disableColumnSorting]),s=a.useCallback(()=>{e.current.setState(L=>{if(t.sortingMode==="server")return n.debug("Skipping sorting rows as sortingMode = server"),f({},L,{sorting:f({},L.sorting,{sortedRows:Fr(tt(e),et,!1)})});const x=ht(e),w=fd(x,e),T=e.current.applyStrategyProcessor("sorting",{sortRowList:w});return f({},L,{sorting:f({},L.sorting,{sortedRows:T})})}),e.current.publishEvent("sortedRowsSet")},[e,n,t.sortingMode]),i=a.useCallback(L=>{ht(e)!==L&&(n.debug("Setting sort model"),e.current.setState(wl(L,t.disableMultipleColumnsSorting)),e.current.applySorting())},[e,n,t.disableMultipleColumnsSorting]),c=a.useCallback((L,x,w)=>{const T=e.current.getColumn(L),M=r(T,x);let O;!w||t.disableMultipleColumnsSorting?O=(M==null?void 0:M.sort)==null?[]:[M]:O=o(T.field,M),e.current.setSortModel(O)},[e,o,r,t.disableMultipleColumnsSorting]),d=a.useCallback(()=>ht(e),[e]),u=a.useCallback(()=>Er(e).map(x=>x.model),[e]),g=a.useCallback(()=>Wn(e),[e]),h=a.useCallback(L=>e.current.getSortedRowIds()[L],[e]);we(e,{getSortModel:d,getSortedRows:u,getSortedRowIds:g,getRowIdFromRowIndex:h,setSortModel:i,sortColumn:c,applySorting:s},"public");const S=a.useCallback((L,x)=>{var M,O;const w=ht(e);return!x.exportOnlyDirtyModels||t.sortModel!=null||((O=(M=t.initialState)==null?void 0:M.sorting)==null?void 0:O.sortModel)!=null||w.length>0?f({},L,{sorting:{sortModel:w}}):L},[e,t.sortModel,($=(k=t.initialState)==null?void 0:k.sorting)==null?void 0:$.sortModel]),y=a.useCallback((L,x)=>{var T;const w=(T=x.stateToRestore.sorting)==null?void 0:T.sortModel;return w==null?L:(e.current.setState(wl(w,t.disableMultipleColumnsSorting)),f({},L,{callbacks:[...L.callbacks,e.current.applySorting]}))},[e,t.disableMultipleColumnsSorting]),p=a.useCallback(L=>{const x=tt(e),w=x[et],T=L.sortRowList?L.sortRowList(w.children.map(M=>x[M])):[...w.children];return w.footerId!=null&&T.push(w.footerId),T},[e]);_e(e,"exportState",S),_e(e,"restoreState",y),Bn(e,nn,"sorting",p);const v=a.useCallback(({field:L,colDef:x},w)=>{if(!x.sortable||t.disableColumnSorting)return;const T=t.multipleColumnsSortingMode==="always"||w.shiftKey||w.metaKey||w.ctrlKey;c(L,void 0,T)},[c,t.disableColumnSorting,t.multipleColumnsSortingMode]),I=a.useCallback(({field:L,colDef:x},w)=>{!x.sortable||t.disableColumnSorting||w.key==="Enter"&&!w.ctrlKey&&!w.metaKey&&c(L,void 0,t.multipleColumnsSortingMode==="always"||w.shiftKey)},[c,t.disableColumnSorting,t.multipleColumnsSortingMode]),D=a.useCallback(()=>{const L=ht(e),x=At(e);if(L.length>0){const w=L.filter(T=>x[T.field]);w.length<L.length&&e.current.setSortModel(w)}},[e]),H=a.useCallback(L=>{L==="sorting"&&e.current.applySorting()},[e]);_e(e,"columnMenu",l),le(e,"columnHeaderClick",v),le(e,"columnHeaderKeyDown",I),le(e,"rowsSet",e.current.applySorting),le(e,"columnsChange",D),le(e,"activeStrategyProcessorChange",H),Yn(()=>{e.current.applySorting()}),nt(()=>{t.sortModel!==void 0&&e.current.setSortModel(t.sortModel)},[e,t.sortModel])};function os(e){const{containerSize:t,scrollPosition:n,elementSize:o,elementOffset:r}=e,l=r+o;if(o>t)return r;if(l-t>n)return l-t;if(r<n)return r}const _w=(e,t)=>{const n=Dt(),o=Ze(e,"useGridScroll"),r=e.current.columnHeadersContainerRef,l=e.current.virtualScrollerRef,s=W(e,Xt),i=a.useCallback(g=>{var v;const h=Re(e),m=_n(e),S=t.listView?[wn(e)]:We(e);if(!(g.rowIndex==null)&&m===0||S.length===0)return!1;o.debug(`Scrolling to cell at row ${g.rowIndex}, col: ${g.colIndex} `);let p={};if(g.colIndex!==void 0){const I=qt(e);let D;if(typeof g.rowIndex<"u"){const H=(v=s[g.rowIndex])==null?void 0:v.id,k=e.current.unstable_getCellColSpanInfo(H,g.colIndex);k&&!k.spannedByColSpan&&(D=k.cellProps.width)}typeof D>"u"&&(D=S[g.colIndex].computedWidth),p.left=os({containerSize:h.viewportOuterSize.width,scrollPosition:Math.abs(l.current.scrollLeft),elementSize:D,elementOffset:I[g.colIndex]})}if(g.rowIndex!==void 0){const I=Zn(e),D=Fg(e),H=bi(e),k=t.pagination?g.rowIndex-D*H:g.rowIndex,$=I.positions[k+1]?I.positions[k+1]-I.positions[k]:I.currentPageTotalHeight-I.positions[k];p.top=os({containerSize:h.viewportInnerSize.height,scrollPosition:l.current.scrollTop,elementSize:$,elementOffset:I.positions[k]})}return p=e.current.unstable_applyPipeProcessors("scrollToIndexes",p,g),typeof p.left!==void 0||typeof p.top!==void 0?(e.current.scroll(p),!0):!1},[o,e,l,t.pagination,s,t.listView]),c=a.useCallback(g=>{if(l.current&&g.left!==void 0&&r.current){const h=n?-1:1;r.current.scrollLeft=g.left,l.current.scrollLeft=h*g.left,o.debug(`Scrolling left: ${g.left}`)}l.current&&g.top!==void 0&&(l.current.scrollTop=g.top,o.debug(`Scrolling top: ${g.top}`)),o.debug("Scrolling, updating container, and viewport")},[l,n,r,o]),d=a.useCallback(()=>l!=null&&l.current?{top:l.current.scrollTop,left:l.current.scrollLeft}:{top:0,left:0},[l]);we(e,{scroll:c,scrollToIndexes:i,getScrollPosition:d},"public")};function Ww(e,t){Le(e,"columnHeaderClick",t.onColumnHeaderClick),Le(e,"columnHeaderContextMenu",t.onColumnHeaderContextMenu),Le(e,"columnHeaderDoubleClick",t.onColumnHeaderDoubleClick),Le(e,"columnHeaderOver",t.onColumnHeaderOver),Le(e,"columnHeaderOut",t.onColumnHeaderOut),Le(e,"columnHeaderEnter",t.onColumnHeaderEnter),Le(e,"columnHeaderLeave",t.onColumnHeaderLeave),Le(e,"cellClick",t.onCellClick),Le(e,"cellDoubleClick",t.onCellDoubleClick),Le(e,"cellKeyDown",t.onCellKeyDown),Le(e,"preferencePanelClose",t.onPreferencePanelClose),Le(e,"preferencePanelOpen",t.onPreferencePanelOpen),Le(e,"menuOpen",t.onMenuOpen),Le(e,"menuClose",t.onMenuClose),Le(e,"rowDoubleClick",t.onRowDoubleClick),Le(e,"rowClick",t.onRowClick),Le(e,"stateChange",t.onStateChange)}function Uw(e,t=166){let n,o;const r=()=>{n=void 0,e(...o)};function l(...s){o=s,n===void 0&&(n=setTimeout(r,t))}return l.clear=()=>{clearTimeout(n),n=void 0},l}const fr={autoHeight:!1,autoPageSize:!1,autosizeOnMount:!1,checkboxSelection:!1,checkboxSelectionVisibleOnly:!1,clipboardCopyCellDelimiter:"	",columnBufferPx:150,columnHeaderHeight:56,disableAutosize:!1,disableColumnFilter:!1,disableColumnMenu:!1,disableColumnReorder:!1,disableColumnResize:!1,disableColumnSelector:!1,disableColumnSorting:!1,disableDensitySelector:!1,disableEval:!1,disableMultipleColumnsFiltering:!1,disableMultipleColumnsSorting:!1,disableMultipleRowSelection:!1,disableRowSelectionOnClick:!1,disableVirtualization:!1,editMode:Tt.Cell,filterDebounceMs:150,filterMode:"client",hideFooter:!1,hideFooterPagination:!1,hideFooterRowCount:!1,hideFooterSelectedRowCount:!1,ignoreDiacritics:!1,ignoreValueFormatterDuringExport:!1,keepColumnPositionIfDraggedOutside:!1,keepNonExistentRowsSelected:!1,loading:!1,logger:console,logLevel:"error",pageSizeOptions:[25,50,100],pagination:!1,paginationMode:"client",resizeThrottleMs:60,rowBufferPx:150,rowHeight:52,rows:[],rowSelection:!0,rowSpacingType:"margin",rowSpanning:!1,showCellVerticalBorder:!1,showColumnVerticalBorder:!1,showToolbar:!1,sortingMode:"client",sortingOrder:["asc","desc",null],throttleRowsMs:0,virtualizeColumnsWithAutoRowHeight:!1},un={width:0,height:0},Kw={isReady:!1,root:un,viewportOuterSize:un,viewportInnerSize:un,contentSize:un,minimumSize:un,hasScrollX:!1,hasScrollY:!1,scrollbarSize:0,headerHeight:0,groupHeaderHeight:0,headerFilterHeight:0,rowWidth:0,rowHeight:0,columnsTotalWidth:0,leftPinnedWidth:0,rightPinnedWidth:0,headersTotalHeight:0,topContainerHeight:0,bottomContainerHeight:0},qw=(e,t,n)=>{const o=Kw,r=xn(n);return f({},e,{dimensions:f({},o,oc(t,n,r,Mn(n)))})},nc=oe(We,qt,(e,t)=>{const n=e.length;return n===0?0:fn(t[n-1]+e[n-1].computedWidth,1)});function Xw(e,t){const n=Ze(e,"useResizeContainer"),o=a.useRef(!1),r=a.useRef(un),l=W(e,Mn),s=W(e,xn),i=W(e,nc),c=a.useRef(!0),{rowHeight:d,headerHeight:u,groupHeaderHeight:g,headerFilterHeight:h,headersTotalHeight:m,leftPinnedWidth:S,rightPinnedWidth:y}=oc(t,e,s,l),p=a.useCallback(()=>Re(e),[e]),v=a.useCallback(T=>{e.current.setState(M=>f({},M,{dimensions:T})),e.current.rootElementRef.current&&rs(e.current.rootElementRef.current,Re(e))},[e]),I=a.useCallback(()=>{const T=Re(e);if(!T.isReady)return 0;const M=Ut(e);if(t.getRowHeight){const G=Jn(e),A=G.lastRowIndex-G.firstRowIndex;return Math.min(A-1,M.rows.length)}const O=Math.floor(T.viewportInnerSize.height/d);return Math.min(O,M.rows.length)},[e,t.getRowHeight,d]),D=a.useCallback(()=>{var q,X;if(c.current)return;const T=Qw(e.current.mainElementRef.current,t.scrollbarSize),M=Zn(e),O=m+M.pinnedTopRowsTotalHeight,G=M.pinnedBottomRowsTotalHeight,A={width:i,height:fn(M.currentPageTotalHeight,1)};let F,E,P=!1,z=!1;if(t.autoHeight)z=!1,P=Math.round(i)>Math.round(r.current.width),F={width:r.current.width,height:O+G+A.height},E={width:Math.max(0,F.width-(z?T:0)),height:Math.max(0,F.height-(P?T:0))};else{F={width:r.current.width,height:r.current.height},E={width:Math.max(0,F.width),height:Math.max(0,F.height-O-G)};const re=A,Y=E,ae=re.width>Y.width,ce=re.height>Y.height;(ae||ce)&&(z=ce,P=re.width+(z?T:0)>Y.width,P&&(z=re.height+T>Y.height)),z&&(E.width-=T),P&&(E.height-=T)}const _=Math.max(F.width,i+(z?T:0)),V={width:i,height:O+A.height+G},R={isReady:!0,root:r.current,viewportOuterSize:F,viewportInnerSize:E,contentSize:A,minimumSize:V,hasScrollX:P,hasScrollY:z,scrollbarSize:T,headerHeight:u,groupHeaderHeight:g,headerFilterHeight:h,rowWidth:_,rowHeight:d,columnsTotalWidth:i,leftPinnedWidth:S,rightPinnedWidth:y,headersTotalHeight:m,topContainerHeight:O,bottomContainerHeight:G},B=e.current.state.dimensions;ct(B,R)||(v(R),Yw(R.viewportInnerSize,B.viewportInnerSize)||e.current.publishEvent("viewportInnerSizeChange",R.viewportInnerSize),(X=(q=e.current).updateRenderContext)==null||X.call(q))},[e,v,t.scrollbarSize,t.autoHeight,d,u,g,h,i,m,S,y]),H=$e(D),k=a.useMemo(()=>t.resizeThrottleMs>0?Uw(()=>{H(),e.current.publishEvent("debouncedResize",r.current)},t.resizeThrottleMs):void 0,[e,t.resizeThrottleMs,H]);a.useEffect(()=>k==null?void 0:k.clear,[k]);const $={getRootDimensions:p},L={updateDimensions:D,getViewportPageSize:I};nt(D,[D]),we(e,$,"public"),we(e,L,"private");const x=a.useCallback(T=>{rs(T,Re(e))},[e]),w=a.useCallback(T=>{if(r.current=T,T.height===0&&!o.current&&!t.autoHeight&&!wo&&(n.error(["The parent DOM element of the Data Grid has an empty height.","Please make sure that this element has an intrinsic height.","The grid displays with a height of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join(`
`)),o.current=!0),T.width===0&&!o.current&&!wo&&(n.error(["The parent DOM element of the Data Grid has an empty width.","Please make sure that this element has an intrinsic width.","The grid displays with a width of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join(`
`)),o.current=!0),c.current||!k){c.current=!1,D();return}k()},[D,t.autoHeight,k,n]);Le(e,"rootMount",x),Le(e,"resize",w),Le(e,"debouncedResize",t.onResize)}function rs(e,t){const n=(o,r)=>e.style.setProperty(o,r);n("--DataGrid-hasScrollX",`${Number(t.hasScrollX)}`),n("--DataGrid-hasScrollY",`${Number(t.hasScrollY)}`),n("--DataGrid-scrollbarSize",`${t.scrollbarSize}px`),n("--DataGrid-rowWidth",`${t.rowWidth}px`),n("--DataGrid-columnsTotalWidth",`${t.columnsTotalWidth}px`),n("--DataGrid-leftPinnedWidth",`${t.leftPinnedWidth}px`),n("--DataGrid-rightPinnedWidth",`${t.rightPinnedWidth}px`),n("--DataGrid-headerHeight",`${t.headerHeight}px`),n("--DataGrid-headersTotalHeight",`${t.headersTotalHeight}px`),n("--DataGrid-topContainerHeight",`${t.topContainerHeight}px`),n("--DataGrid-bottomContainerHeight",`${t.bottomContainerHeight}px`),n("--height",`${t.rowHeight}px`)}function oc(e,t,n,o){const r=ti(e.rowHeight,fr.rowHeight);return{rowHeight:Math.floor(r*n),headerHeight:Math.floor(e.columnHeaderHeight*n),groupHeaderHeight:Math.floor((e.columnGroupHeaderHeight??e.columnHeaderHeight)*n),headerFilterHeight:Math.floor((e.headerFilterHeight??e.columnHeaderHeight)*n),columnsTotalWidth:nc(t),headersTotalHeight:$r(t,e),leftPinnedWidth:o.left.reduce((l,s)=>l+s.computedWidth,0),rightPinnedWidth:o.right.reduce((l,s)=>l+s.computedWidth,0)}}const ls=new WeakMap;function Qw(e,t){if(t!==void 0)return t;if(e===null)return 0;const n=ls.get(e);if(n!==void 0)return n;const r=Zt(e).createElement("div");r.style.width="99px",r.style.height="99px",r.style.position="absolute",r.style.overflow="scroll",r.className="scrollDiv",e.appendChild(r);const l=r.offsetWidth-r.clientWidth;return e.removeChild(r),ls.set(e,l),l}function Yw(e,t){return e.width===t.width&&e.height===t.height}const Zw=typeof globalThis.ResizeObserver<"u"?globalThis.ResizeObserver:class{observe(){}unobserve(){}disconnect(){}},Jw=(e,t,n)=>{n.current.caches.rowsMeta={heights:new Map};const o=xr(n),r=_n(n),l=Kn(n),s=Math.min(l.enabled?l.paginationModel.pageSize:r,r);return f({},e,{rowsMeta:{currentPageTotalHeight:s*o,positions:Array.from({length:s},(i,c)=>c*o),pinnedTopRowsTotalHeight:0,pinnedBottomRowsTotalHeight:0}})},eS=(e,t)=>{const{getRowHeight:n,getRowSpacing:o,getEstimatedRowHeight:r}=t,l=e.current.caches.rowsMeta.heights,s=a.useRef(-1),i=a.useRef(!1),c=a.useRef(!1),d=W(e,xn),u=Ar(e),g=W(e,vn),h=W(e,xr),m=T=>{let M=l.get(T);return M===void 0&&(M={content:h,spacingTop:0,spacingBottom:0,detail:0,autoHeight:!1,needsFirstMeasurement:!0},l.set(T,M)),M},S=a.useCallback(T=>{const M=Re(e).rowHeight,O=e.current.getRowHeightEntry(T.id);if(!n)O.content=M,O.needsFirstMeasurement=!1;else{const G=n(f({},T,{densityFactor:d}));if(G==="auto"){if(O.needsFirstMeasurement){const A=r?r(f({},T,{densityFactor:d})):M;O.content=A??M}i.current=!0,O.autoHeight=!0}else O.content=ti(G,M),O.needsFirstMeasurement=!1,O.autoHeight=!1}if(o){const G=e.current.getRowIndexRelativeToVisibleRows(T.id),A=o(f({},T,{isFirstVisible:G===0,isLastVisible:G===u.rows.length-1,indexRelativeToCurrentPage:G}));O.spacingTop=A.top??0,O.spacingBottom=A.bottom??0}else O.spacingTop=0,O.spacingBottom=0;return e.current.unstable_applyPipeProcessors("rowHeight",O,T),O},[e,u.rows,n,r,h,o,d]),y=a.useCallback(()=>{i.current=!1;const T=g.top.reduce((E,P)=>{const z=S(P);return E+z.content+z.spacingTop+z.spacingBottom+z.detail},0),M=g.bottom.reduce((E,P)=>{const z=S(P);return E+z.content+z.spacingTop+z.spacingBottom+z.detail},0),O=[],G=u.rows.reduce((E,P)=>{O.push(E);const z=S(P),_=z.content+z.spacingTop+z.spacingBottom+z.detail;return E+_},0);i.current||(s.current=1/0);const A=T!==e.current.state.rowsMeta.pinnedTopRowsTotalHeight||M!==e.current.state.rowsMeta.pinnedBottomRowsTotalHeight||G!==e.current.state.rowsMeta.currentPageTotalHeight,F={currentPageTotalHeight:G,positions:O,pinnedTopRowsTotalHeight:T,pinnedBottomRowsTotalHeight:M};e.current.setState(E=>f({},E,{rowsMeta:F})),A&&e.current.updateDimensions(),c.current=!0},[e,g,u.rows,S]),p=T=>{var M;return((M=l.get(T))==null?void 0:M.content)??h},v=(T,M)=>{const O=e.current.getRowHeightEntry(T),G=O.content!==M;O.needsFirstMeasurement=!1,O.content=M,c.current&&(c.current=!G)},I=T=>{var M;return((M=l.get(T))==null?void 0:M.autoHeight)??!1},D=()=>s.current,H=T=>{i.current&&T>s.current&&(s.current=T)},k=()=>{l.clear(),y()},$=Gt(()=>new Zw(T=>{var M;for(let O=0;O<T.length;O+=1){const G=T[O],A=G.borderBoxSize&&G.borderBoxSize.length>0?G.borderBoxSize[0].blockSize:G.contentRect.height,F=G.target.__mui_id;if(((M=Ti(e))==null?void 0:M.id)===F&&A===0)return;e.current.unstable_storeRowHeightMeasurement(F,A)}c.current||requestAnimationFrame(()=>{e.current.requestPipeProcessorsApplication("rowHeight")})})).current,L=(T,M)=>(T.__mui_id=M,$.observe(T),()=>$.unobserve(T));_r(e,"rowHeight",y),nt(()=>{y()},[y]);const x={unstable_getRowHeight:p,unstable_setLastMeasuredRowIndex:H,unstable_storeRowHeightMeasurement:v,resetRowHeights:k},w={hydrateRowsMeta:y,observeRowHeight:L,rowHasAutoHeight:I,getRowHeightEntry:m,getLastMeasuredRowIndex:D};we(e,x,"public"),we(e,w,"private")},tS=e=>{const t=a.useCallback((r={})=>e.current.unstable_applyPipeProcessors("exportState",{},r),[e]),n=a.useCallback(r=>{e.current.unstable_applyPipeProcessors("restoreState",{callbacks:[]},{stateToRestore:r}).callbacks.forEach(s=>{s()})},[e]);we(e,{exportState:t,restoreState:n},"public")},nS=e=>{const t=a.useRef({}),n=(i,c)=>{var d;return(d=t.current[i])==null?void 0:d[c]},o=()=>{t.current={}},r=a.useCallback(({rowId:i,minFirstColumn:c,maxLastColumn:d,columns:u})=>{for(let g=c;g<d;g+=1){const h=oS({apiRef:e,lookup:t.current,columnIndex:g,rowId:i,minFirstColumnIndex:c,maxLastColumnIndex:d,columns:u});h.colSpan>1&&(g+=h.colSpan-1)}},[e]),l={unstable_getCellColSpanInfo:n},s={resetColSpan:o,calculateColSpan:r};we(e,l,"public"),we(e,s,"private"),le(e,"columnOrderChange",o)};function oS(e){const{apiRef:t,lookup:n,columnIndex:o,rowId:r,minFirstColumnIndex:l,maxLastColumnIndex:s,columns:i}=e,c=i.length,d=i[o],u=t.current.getRow(r),g=t.current.getRowValue(u,d),h=typeof d.colSpan=="function"?d.colSpan(g,u,d,t):d.colSpan;if(!h||h===1)return nr(n,r,o,{spannedByColSpan:!1,cellProps:{colSpan:1,width:d.computedWidth}}),{colSpan:1};let m=d.computedWidth;for(let S=1;S<h;S+=1){const y=o+S;if(y>=l&&y<s){const p=i[y];m+=p.computedWidth,nr(n,r,o+S,{spannedByColSpan:!0,rightVisibleCellIndex:Math.min(o+h,c-1),leftVisibleCellIndex:o})}nr(n,r,o,{spannedByColSpan:!1,cellProps:{colSpan:h,width:m}})}return{colSpan:h}}function nr(e,t,n,o){e[t]||(e[t]={}),e[t][n]=o}const rc=(e,t,n)=>{if(Ns(e)){if(n[e.field]!==void 0)throw new Error(["MUI X: columnGroupingModel contains duplicated field",`column field ${e.field} occurs two times in the grouping model:`,`- ${n[e.field].join(" > ")}`,`- ${t.join(" > ")}`].join(`
`));n[e.field]=t;return}const{groupId:o,children:r}=e;r.forEach(l=>{rc(l,[...t,o],n)})},hr=e=>{if(!e)return{};const t={};return e.forEach(n=>{rc(n,[],t)}),t},mr=(e,t,n)=>{const o=c=>t[c]??[],r=[],l=Math.max(...e.map(c=>o(c).length)),s=(c,d,u)=>ct(o(c).slice(0,u+1),o(d).slice(0,u+1)),i=(c,d)=>!!(n!=null&&n.left&&n.left.includes(c)&&!n.left.includes(d)||n!=null&&n.right&&!n.right.includes(c)&&n.right.includes(d));for(let c=0;c<l;c+=1){const d=e.reduce((u,g)=>{const h=o(g)[c]??null;if(u.length===0)return[{columnFields:[g],groupId:h}];const m=u[u.length-1],S=m.columnFields[m.columnFields.length-1];return m.groupId!==h||!s(S,g,c)||i(S,g)?[...u,{columnFields:[g],groupId:h}]:[...u.slice(0,u.length-1),{columnFields:[...m.columnFields,g],groupId:h}]},[]);r.push(d)}return r},rS=["groupId","children"],Wr=e=>{let t={};return e.forEach(n=>{if(Ns(n))return;const{groupId:o,children:r}=n,l=Q(n,rS);if(!o)throw new Error("MUI X: An element of the columnGroupingModel does not have either `field` or `groupId`.");const s=f({},l,{groupId:o}),i=Wr(r);if(i[o]!==void 0||t[o]!==void 0)throw new Error(`MUI X: The groupId ${o} is used multiple times in the columnGroupingModel.`);t=f({},t,i,{[o]:s})}),f({},t)},lS=(e,t,n)=>{if(!t.columnGroupingModel)return e;const o=kt(n),r=In(n),l=Wr(t.columnGroupingModel??[]),s=hr(t.columnGroupingModel??[]),i=mr(o,s,n.current.state.pinnedColumns??{}),c=r.length===0?0:Math.max(...r.map(d=>{var u;return((u=s[d])==null?void 0:u.length)??0}));return f({},e,{columnGrouping:{lookup:l,unwrappedGroupingModel:s,headerStructure:i,maxDepth:c}})},sS=(e,t)=>{const n=a.useCallback(i=>vi(e)[i]??[],[e]),o=a.useCallback(()=>Ii(e),[e]);we(e,{getColumnGroupPath:n,getAllGroupDetails:o},"public");const l=a.useCallback(()=>{const i=hr(t.columnGroupingModel??[]);e.current.setState(c=>{var h;const d=((h=c.columns)==null?void 0:h.orderedFields)??[],u=c.pinnedColumns??{},g=mr(d,i,u);return f({},c,{columnGrouping:f({},c.columnGrouping,{headerStructure:g})})})},[e,t.columnGroupingModel]),s=a.useCallback(i=>{var y,p;const c=((p=(y=e.current).getPinnedColumns)==null?void 0:p.call(y))??{},d=kt(e),u=In(e),g=Wr(i??[]),h=hr(i??[]),m=mr(d,h,c),S=u.length===0?0:Math.max(...u.map(v=>{var I;return((I=h[v])==null?void 0:I.length)??0}));e.current.setState(v=>f({},v,{columnGrouping:{lookup:g,unwrappedGroupingModel:h,headerStructure:m,maxDepth:S}}))},[e]);le(e,"columnIndexChange",l),le(e,"columnsChange",()=>{s(t.columnGroupingModel)}),le(e,"columnVisibilityModelChange",()=>{s(t.columnGroupingModel)}),a.useEffect(()=>{s(t.columnGroupingModel)},[s,t.columnGroupingModel])};function iS(){let e,t;const n=new Promise((o,r)=>{e=o,t=r});return n.resolve=e,n.reject=t,n}function ss(e,t){if(t!==void 0&&e.changedTouches){for(let n=0;n<e.changedTouches.length;n+=1){const o=e.changedTouches[n];if(o.identifier===t)return{x:o.clientX,y:o.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function is(e,t,n,o){let r=e;return o==="Right"?r+=t-n.left:r+=n.right-t,r}function cS(e,t,n){return n==="Left"?e-t.left:t.right-e}function aS(e){return e==="Right"?"Left":"Right"}function uS(e,t){const n=e.classList.contains(b["columnSeparator--sideRight"])?"Right":"Left";return t?aS(n):n}function cs(e){e.preventDefault(),e.stopImmediatePropagation()}function dS(e){const t=a.useRef(void 0),n=()=>Ei(e),o=W(e,n);return a.useEffect(()=>{t.current&&o===!1&&(t.current.resolve(),t.current=void 0)}),()=>{if(!t.current){if(n()===!1)return Promise.resolve();t.current=iS()}return t.current}}function gS(e,t){if(e.length<4)return e;const n=e.slice();n.sort((i,c)=>i-c);const o=n[Math.floor(n.length*.25)],r=n[Math.floor(n.length*.75)-1],l=r-o,s=l<5?5:l*t;return n.filter(i=>i>o-s&&i<r+s)}function fS(e,t,n){const o={},r=e.current.rootElementRef.current;return r.classList.add(b.autosizing),n.forEach(l=>{const i=Am(e.current,l.field).map(S=>S.getBoundingClientRect().width??0),c=t.includeOutliers?i:gS(i,t.outliersFactor);if(t.includeHeaders){const S=$m(e.current,l.field);if(S){const y=S.querySelector(`.${b.columnHeaderTitle}`),p=S.querySelector(`.${b.columnHeaderTitleContainerContent}`),v=S.querySelector(`.${b.iconButtonContainer}`),I=S.querySelector(`.${b.menuIcon}`),D=y??p,H=window.getComputedStyle(S,null),k=parseInt(H.paddingLeft,10)+parseInt(H.paddingRight,10),L=D.scrollWidth+1+k+((v==null?void 0:v.clientWidth)??0)+((I==null?void 0:I.clientWidth)??0);c.push(L)}}const d=l.minWidth!==-1/0&&l.minWidth!==void 0,u=l.maxWidth!==1/0&&l.maxWidth!==void 0,g=d?l.minWidth:0,h=u?l.maxWidth:1/0,m=c.length===0?0:Math.max(...c);o[l.field]=it(m,g,h)}),r.classList.remove(b.autosizing),o}const hS=e=>f({},e,{columnResize:{resizingColumnField:""}});function mS(){return{colDef:void 0,initialColWidth:0,initialTotalWidth:0,previousMouseClickEvent:void 0,columnHeaderElement:void 0,headerFilterElement:void 0,groupHeaderElements:[],cellElements:[],leftPinnedCellsAfter:[],rightPinnedCellsBefore:[],fillerLeft:void 0,fillerRight:void 0,leftPinnedHeadersAfter:[],rightPinnedHeadersBefore:[]}}const CS=(e,t)=>{const n=Dt(),o=Ze(e,"useGridColumnResize"),r=Gt(mS).current,l=a.useRef(null),s=a.useRef(null),i=Sn(),c=a.useRef(void 0),d=w=>{var F,E;o.debug(`Updating width to ${w} for col ${r.colDef.field}`);const T=r.columnHeaderElement.offsetWidth,M=w-T,O=w-r.initialColWidth;if(O>0){const P=r.initialTotalWidth+O;(E=(F=e.current.rootElementRef)==null?void 0:F.current)==null||E.style.setProperty("--DataGrid-rowWidth",`${P}px`)}r.colDef.computedWidth=w,r.colDef.width=w,r.colDef.flex=0,r.columnHeaderElement.style.width=`${w}px`;const G=r.headerFilterElement;G&&(G.style.width=`${w}px`),r.groupHeaderElements.forEach(P=>{const z=P;let _;z.getAttribute("aria-colspan")==="1"?_=`${w}px`:_=`${z.offsetWidth+M}px`,z.style.width=_}),r.cellElements.forEach(P=>{const z=P;let _;z.getAttribute("aria-colspan")==="1"?_=`${w}px`:_=`${z.offsetWidth+M}px`,z.style.setProperty("--width",_)});const A=e.current.unstable_applyPipeProcessors("isColumnPinned",!1,r.colDef.field);A===Nt.LEFT&&(cn(r.fillerLeft,"width",M),r.leftPinnedCellsAfter.forEach(P=>{cn(P,"left",M)}),r.leftPinnedHeadersAfter.forEach(P=>{cn(P,"left",M)})),A===Nt.RIGHT&&(cn(r.fillerRight,"width",M),r.rightPinnedCellsBefore.forEach(P=>{cn(P,"right",M)}),r.rightPinnedHeadersBefore.forEach(P=>{cn(P,"right",M)}))},u=w=>{if(v(),r.previousMouseClickEvent){const T=r.previousMouseClickEvent,M=T.timeStamp,O=T.clientX,G=T.clientY;if(w.timeStamp-M<300&&w.clientX===O&&w.clientY===G){r.previousMouseClickEvent=void 0,e.current.publishEvent("columnResizeStop",null,w);return}}if(r.colDef){e.current.setColumnWidth(r.colDef.field,r.colDef.width),o.debug(`Updating col ${r.colDef.field} with new width: ${r.colDef.width}`);const T=It(e);r.groupHeaderElements.forEach(M=>{const O=km(M),G=M,F=`${O.reduce((E,P)=>T.columnVisibilityModel[P]!==!1?E+T.lookup[P].computedWidth:E,0)}px`;G.style.width=F})}i.start(0,()=>{e.current.publishEvent("columnResizeStop",null,w)})},g=(w,T,M)=>{var F;const O=e.current.rootElementRef.current;r.initialColWidth=w.computedWidth,r.initialTotalWidth=e.current.getRootDimensions().rowWidth,r.colDef=w,r.columnHeaderElement=Em(e.current.columnHeadersContainerRef.current,w.field);const G=O.querySelector(`.${b.headerFilterRow} [data-field="${Rt(w.field)}"]`);G&&(r.headerFilterElement=G),r.groupHeaderElements=Tm((F=e.current.columnHeadersContainerRef)==null?void 0:F.current,w.field),r.cellElements=Dm(r.columnHeaderElement,e.current),r.fillerLeft=_l(e.current,n?"filler--pinnedRight":"filler--pinnedLeft"),r.fillerRight=_l(e.current,n?"filler--pinnedLeft":"filler--pinnedRight");const A=e.current.unstable_applyPipeProcessors("isColumnPinned",!1,r.colDef.field);r.leftPinnedCellsAfter=A!==Nt.LEFT?[]:Hm(e.current,r.columnHeaderElement,n),r.rightPinnedCellsBefore=A!==Nt.RIGHT?[]:Om(e.current,r.columnHeaderElement,n),r.leftPinnedHeadersAfter=A!==Nt.LEFT?[]:Gm(e.current,r.columnHeaderElement,n),r.rightPinnedHeadersBefore=A!==Nt.RIGHT?[]:Lm(e.current,r.columnHeaderElement,n),s.current=uS(T,n),l.current=cS(M,r.columnHeaderElement.getBoundingClientRect(),s.current)},h=$e(u),m=$e(w=>{if(w.buttons===0){h(w);return}let T=is(l.current,w.clientX,r.columnHeaderElement.getBoundingClientRect(),s.current);T=it(T,r.colDef.minWidth,r.colDef.maxWidth),d(T);const M={element:r.columnHeaderElement,colDef:r.colDef,width:T};e.current.publishEvent("columnResize",M,w)}),S=$e(w=>{ss(w,c.current)&&u(w)}),y=$e(w=>{const T=ss(w,c.current);if(!T)return;if(w.type==="mousemove"&&w.buttons===0){S(w);return}let M=is(l.current,T.x,r.columnHeaderElement.getBoundingClientRect(),s.current);M=it(M,r.colDef.minWidth,r.colDef.maxWidth),d(M);const O={element:r.columnHeaderElement,colDef:r.colDef,width:M};e.current.publishEvent("columnResize",O,w)}),p=$e(w=>{const T=So(w.target,b["columnSeparator--resizable"]);if(!T)return;const M=w.changedTouches[0];M!=null&&(c.current=M.identifier);const O=So(w.target,b.columnHeader),G=Fm(O),A=e.current.getColumn(G);o.debug(`Start Resize on col ${A.field}`),e.current.publishEvent("columnResizeStart",{field:G},w),g(A,T,M.clientX);const F=Zt(w.currentTarget);F.addEventListener("touchmove",y),F.addEventListener("touchend",S)}),v=a.useCallback(()=>{const w=Zt(e.current.rootElementRef.current);w.body.style.removeProperty("cursor"),w.removeEventListener("mousemove",m),w.removeEventListener("mouseup",h),w.removeEventListener("touchmove",y),w.removeEventListener("touchend",S),setTimeout(()=>{w.removeEventListener("click",cs,!0)},100),r.columnHeaderElement&&(r.columnHeaderElement.style.pointerEvents="unset")},[e,r,m,h,y,S]),I=a.useCallback(({field:w})=>{e.current.setState(T=>f({},T,{columnResize:f({},T.columnResize,{resizingColumnField:w})}))},[e]),D=a.useCallback(()=>{e.current.setState(w=>f({},w,{columnResize:f({},w.columnResize,{resizingColumnField:""})}))},[e]),H=$e(({colDef:w},T)=>{if(T.button!==0||!T.currentTarget.classList.contains(b["columnSeparator--resizable"]))return;T.preventDefault(),o.debug(`Start Resize on col ${w.field}`),e.current.publishEvent("columnResizeStart",{field:w.field},T),g(w,T.currentTarget,T.clientX);const M=Zt(e.current.rootElementRef.current);M.body.style.cursor="col-resize",r.previousMouseClickEvent=T.nativeEvent,M.addEventListener("mousemove",m),M.addEventListener("mouseup",h),M.addEventListener("click",cs,!0)}),k=$e((w,T)=>{if(t.disableAutosize||T.button!==0)return;const M=e.current.state.columns.lookup[w.field];M.resizable!==!1&&e.current.autosizeColumns(f({},t.autosizeOptions,{disableColumnVirtualization:!1,columns:[M.field]}))}),$=dS(e),L=a.useRef(!1),x=a.useCallback(async w=>{var A;if(!((A=e.current.rootElementRef)==null?void 0:A.current)||L.current)return;L.current=!0;const M=It(e),O=f({},gh,w,{columns:(w==null?void 0:w.columns)??M.orderedFields});O.columns=O.columns.filter(F=>M.columnVisibilityModel[F]!==!1);const G=O.columns.map(F=>e.current.state.columns.lookup[F]);try{!t.disableVirtualization&&O.disableColumnVirtualization&&(e.current.unstable_setColumnVirtualization(!1),await $());const F=fS(e,O,G),E=G.map(P=>f({},P,{width:F[P.field],computedWidth:F[P.field],flex:0}));if(O.expand){const z=M.orderedFields.map(B=>M.lookup[B]).filter(B=>M.columnVisibilityModel[B.field]!==!1).reduce((B,q)=>B+(F[q.field]??q.computedWidth??q.width),0),R=e.current.getRootDimensions().viewportInnerSize.width-z;if(R>0){const B=R/(E.length||1);E.forEach(q=>{q.width+=B,q.computedWidth+=B})}}e.current.updateColumns(E),E.forEach((P,z)=>{if(P.width!==G[z].width){const _=P.width;e.current.publishEvent("columnWidthChange",{element:e.current.getColumnHeaderElement(P.field),colDef:P,width:_})}})}finally{t.disableVirtualization||e.current.unstable_setColumnVirtualization(!0),L.current=!1}},[e,$,t.disableVirtualization]);a.useEffect(()=>v,[v]),Ss(()=>{t.autosizeOnMount&&Promise.resolve().then(()=>{e.current.autosizeColumns(t.autosizeOptions)})}),Fi(e,()=>{var w;return(w=e.current.columnHeadersContainerRef)==null?void 0:w.current},"touchstart",p,{passive:!0}),we(e,{autosizeColumns:x},"public"),le(e,"columnResizeStop",D),le(e,"columnResizeStart",I),le(e,"columnSeparatorMouseDown",H),le(e,"columnSeparatorDoubleClick",k),Le(e,"columnResize",t.onColumnResize),Le(e,"columnWidthChange",t.onColumnWidthChange)};function cn(e,t,n){e&&(e.style[t]=`${parseInt(e.style[t],10)+n}px`)}function bS(e,t){return e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?null:e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex>t.lastRowIndex?{firstRowIndex:t.lastRowIndex,lastRowIndex:e.lastRowIndex}:e.firstRowIndex<t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?{firstRowIndex:e.firstRowIndex,lastRowIndex:t.firstRowIndex-1}:e}function as(e){return e.firstRowIndex!==0||e.lastRowIndex!==0}const or=(e,t,n)=>{if(!e)return null;let o=e[t.field];const r=t.rowSpanValueGetter??t.valueGetter;return r&&(o=r(o,e,t,n)),o},hn={spannedCells:{},hiddenCells:{},hiddenCellOriginMap:{}},Io={firstRowIndex:0,lastRowIndex:0},pS=new Set([ft,"__reorder__",To]),us=20,lc=(e,t,n,o,r,l,s)=>{const i=l?{}:f({},e.current.state.rowSpanning.spannedCells),c=l?{}:f({},e.current.state.rowSpanning.hiddenCells),d=l?{}:f({},e.current.state.rowSpanning.hiddenCellOriginMap);return l&&(s=Io),t.forEach(u=>{var g;if(!pS.has(u.field)){for(let h=r.firstRowIndex;h<r.lastRowIndex;h+=1){const m=n[h];if((g=c[m.id])!=null&&g[u.field])continue;const S=or(m.model,u,e);if(S==null)continue;let y=m.id,p=h,v=0;const I=[];if(h===r.firstRowIndex){let H=h-1,k=n[H];for(;H>=o.firstRowIndex&&k&&or(k.model,u,e)===S;){const $=n[H+1];c[$.id]?c[$.id][u.field]=!0:c[$.id]={[u.field]:!0},I.push(h),v+=1,y=k.id,p=H,H-=1,k=n[H]}}I.forEach(H=>{d[H]?d[H][u.field]=p:d[H]={[u.field]:p}});let D=h+1;for(;D<=o.lastRowIndex&&n[D]&&or(n[D].model,u,e)===S;){const H=n[D];c[H.id]?c[H.id][u.field]=!0:c[H.id]={[u.field]:!0},d[D]?d[D][u.field]=p:d[D]={[u.field]:p},D+=1,v+=1}v>0&&(i[y]?i[y][u.field]=v+1:i[y]={[u.field]:v+1})}s={firstRowIndex:Math.min(s.firstRowIndex,r.firstRowIndex),lastRowIndex:Math.max(s.lastRowIndex,r.lastRowIndex)}}}),{spannedCells:i,hiddenCells:c,hiddenCellOriginMap:d,processedRange:s}},sc=(e,t)=>{const n=Yt(t).length;if(e.pagination){const o=bi(t);let r=us;return o>0&&(r=o-1),{firstRowIndex:0,lastRowIndex:Math.min(r,n)}}return{firstRowIndex:0,lastRowIndex:Math.min(us,n)}},wS=(e,t,n)=>{var S;if(!t.rowSpanning)return f({},e,{rowSpanning:hn});const o=e.rows.dataRowIds||[],r=e.columns.orderedFields||[],l=e.rows.dataRowIdToModelLookup,s=e.columns.lookup,i=!!e.filter.filterModel.items.length||!!((S=e.filter.filterModel.quickFilterValues)!=null&&S.length);if(!o.length||!r.length||!l||!s||i)return f({},e,{rowSpanning:hn});const c=sc(t,n),d=o.map(y=>({id:y,model:l[y]})),u=r.map(y=>s[y]),{spannedCells:g,hiddenCells:h,hiddenCellOriginMap:m}=lc(n,u,d,c,c,!0,Io);return f({},e,{rowSpanning:{spannedCells:g,hiddenCells:h,hiddenCellOriginMap:m}})},SS=(e,t)=>{const n=Gt(()=>e.current.state.rowSpanning!==hn?sc(t,e):Io),o=a.useCallback((l,s=!1)=>{const{range:i,rows:c}=Ut(e,{pagination:t.pagination,paginationMode:t.paginationMode});if(i===null||!as(l))return;s&&(n.current=Io);const d=bS({firstRowIndex:l.firstRowIndex,lastRowIndex:Math.min(l.lastRowIndex,i.lastRowIndex+1)},n.current);if(d===null)return;const u=We(e),{spannedCells:g,hiddenCells:h,hiddenCellOriginMap:m,processedRange:S}=lc(e,u,c,i,d,s,n.current);n.current=S;const y=Object.keys(g).length,p=Object.keys(h).length,v=Object.keys(e.current.state.rowSpanning.spannedCells).length,I=Object.keys(e.current.state.rowSpanning.hiddenCells).length;!(s||y!==v||p!==I)||y===0&&v===0||e.current.setState(k=>f({},k,{rowSpanning:{spannedCells:g,hiddenCells:h,hiddenCellOriginMap:m}}))},[e,n,t.pagination,t.paginationMode]),r=a.useCallback(()=>{const l=Jn(e);as(l)&&o(l,!0)},[e,o]);le(e,"renderedRowsIntervalChange",jt(t.rowSpanning,o)),le(e,"sortedRowsSet",jt(t.rowSpanning,r)),le(e,"paginationModelChange",jt(t.rowSpanning,r)),le(e,"filteredRowsSet",jt(t.rowSpanning,r)),le(e,"columnsChange",jt(t.rowSpanning,r)),a.useEffect(()=>{t.rowSpanning?e.current.state.rowSpanning===hn&&r():e.current.state.rowSpanning!==hn&&e.current.setState(l=>f({},l,{rowSpanning:hn}))},[e,r,t.rowSpanning])},xS=(e,t,n)=>f({},e,{listViewColumn:t.listViewColumn?f({},t.listViewColumn,{computedWidth:Cr(n)}):void 0});function yS(e,t){const n=()=>{e.current.setState(l=>l.listViewColumn?f({},l,{listViewColumn:f({},l.listViewColumn,{computedWidth:Cr(e)})}):l)},o=a.useRef(null);le(e,"viewportInnerSizeChange",l=>{o.current!==l.width&&(o.current=l.width,n())}),le(e,"columnVisibilityModelChange",n),nt(()=>{const l=t.listViewColumn;l&&e.current.setState(s=>f({},s,{listViewColumn:f({},l,{computedWidth:Cr(e)})}))},[e,t.listViewColumn]),a.useEffect(()=>{t.listView&&t.listViewColumn},[t.listView,t.listViewColumn])}function Cr(e){return Re(e).viewportInnerSize.width}const vS=oe(Qe,ht,Je,(e,t,n)=>({groupKeys:[],paginationModel:n,sortModel:t,filterModel:e,start:n.page*n.pageSize,end:n.page*n.pageSize+n.pageSize-1}));let rr=function(e){return e.Default="set-new-rows",e.LazyLoading="replace-row-range",e}({});class br{constructor(t){this.chunkSize=void 0,this.getCacheKeys=n=>{if(this.chunkSize<1||typeof n.start!="number")return[n];const o=[];for(let r=n.start;r<n.end;r+=this.chunkSize){const l=Math.min(r+this.chunkSize-1,n.end);o.push(f({},n,{start:r,end:l}))}return o},this.splitResponse=(n,o)=>{const r=this.getCacheKeys(n),l=new Map;return r.forEach(s=>{var d,u,g,h;const i=s.end===n.end,c=f({},o,{pageInfo:f({},o.pageInfo,{hasNextPage:((d=o.pageInfo)==null?void 0:d.hasNextPage)!==void 0&&!i?!0:(u=o.pageInfo)==null?void 0:u.hasNextPage,nextCursor:((g=o.pageInfo)==null?void 0:g.nextCursor)!==void 0&&!i?o.rows[s.end+1].id:(h=o.pageInfo)==null?void 0:h.nextCursor}),rows:typeof s.start!="number"||typeof n.start!="number"?o.rows:o.rows.slice(s.start-n.start,s.end-n.start+1)});l.set(s,c)}),l},this.chunkSize=t}}br.mergeResponses=e=>e.length===1?e[0]:e.reduce((t,n)=>({rows:[...t.rows,...n.rows],rowCount:n.rowCount,pageInfo:n.pageInfo}),{rows:[],rowCount:0,pageInfo:{}});const IS={clear:()=>{},get:()=>{},set:()=>{}};function ds(e,t={}){return e===null?IS:e??new Ch(t)}const MS=(e,t,n={})=>{var k,$;const o=a.useCallback(()=>{e.current.setStrategyAvailability(Wt.DataSource,rr.Default,t.dataSource?()=>!0:()=>!1)},[e,t.dataSource]),[r,l]=a.useState(!1),s=W(e,Je),i=a.useRef(0),c=t.onDataSourceError,d=Gt(()=>{const L=t.pageSizeOptions.map(w=>typeof w=="number"?w:w.value).sort((w,T)=>w-T),x=Math.min(s.pageSize,L[0]);return new br(x)}).current,[u,g]=a.useState(()=>ds(t.dataSourceCache,n.cacheOptions)),h=a.useCallback(async(L,x)=>{var A,F,E;const w=(A=t.dataSource)==null?void 0:A.getRows;if(!w)return;if(L&&L!==et&&t.signature!=="DataGrid"){(F=n.fetchRowChildren)==null||F.call(n,[L]);return}(E=n.clearDataSourceState)==null||E.call(n);const T=f({},vS(e),e.current.unstable_applyPipeProcessors("getRowsParams",{}),x),O=d.getCacheKeys(T).map(P=>u.get(P));if(O.every(P=>P!==void 0)){e.current.applyStrategyProcessor("dataSourceRowsUpdate",{response:br.mergeResponses(O),fetchParams:T});return}(r||e.current.getRowsCount()===0)&&e.current.setLoading(!0);const G=i.current+1;i.current=G;try{const P=await w(T);d.splitResponse(T,P).forEach((_,V)=>u.set(V,_)),i.current===G&&e.current.applyStrategyProcessor("dataSourceRowsUpdate",{response:P,fetchParams:T})}catch(P){i.current===G&&(e.current.applyStrategyProcessor("dataSourceRowsUpdate",{error:P,fetchParams:T}),typeof c=="function"&&c(new bh({message:P==null?void 0:P.message,params:T,cause:P})))}finally{r&&i.current===G&&e.current.setLoading(!1)}},[d,u,e,r,(k=t.dataSource)==null?void 0:k.getRows,c,n,t.signature]),m=a.useCallback(()=>{l(e.current.getActiveStrategy(Wt.DataSource)===rr.Default)},[e]),S=a.useCallback(L=>{if("error"in L){e.current.setRows([]);return}const{response:x}=L;x.rowCount!==void 0&&e.current.setRowCount(x.rowCount),e.current.setRows(x.rows),e.current.unstable_applyPipeProcessors("processDataSourceRows",{params:L.fetchParams,response:x},!0)},[e]),y=($=t.dataSource)==null?void 0:$.updateRow,p=n.handleEditRow,v=a.useCallback(async L=>{if(y)try{const x=await y(L);return typeof p=="function"?(p(L,x),x):(e.current.updateNestedRows([x],[]),x&&!ct(x,L.previousRow)&&e.current.dataSource.cache.clear(),x)}catch(x){throw typeof c=="function"&&c(new ph({message:x==null?void 0:x.message,params:L,cause:x})),x}},[e,y,c,p]),I={dataSource:{fetchRows:h,cache:u,editRow:v}},D=a.useMemo(()=>ys(h,0),[h]),H=a.useRef(!0);return a.useEffect(()=>{if(H.current){H.current=!1;return}if(t.dataSourceCache===void 0)return;const L=ds(t.dataSourceCache,n.cacheOptions);g(x=>x!==L?L:x)},[t.dataSourceCache,n.cacheOptions]),a.useEffect(()=>(t.dataSource&&(e.current.dataSource.cache.clear(),e.current.dataSource.fetchRows()),()=>{i.current+=1}),[e,t.dataSource]),{api:{public:I},debouncedFetchRows:D,strategyProcessor:{strategyName:rr.Default,group:"dataSourceRowsUpdate",processor:S},setStrategyAvailability:o,cacheChunkManager:d,cache:u,events:{strategyAvailabilityChange:m,sortModelChange:jt(r,()=>D()),filterModelChange:jt(r,()=>D()),paginationModelChange:jt(r,()=>D())}}},PS=(e,t)=>{const{api:n,strategyProcessor:o,events:r,setStrategyAvailability:l}=MS(e,t);we(e,n.public,"public"),Bn(e,o.strategyName,o.group,o.processor),Object.entries(r).forEach(([s,i])=>{le(e,s,i)}),a.useEffect(()=>{l()},[l])},FS=(e,t)=>{jp(e,t),Nw(e,t),$w(e),Xe(Np,e,t),Xe(Rw,e,t),Xe(Yp,e,t),Xe(Dw,e,t),Xe(ww,e,t),Xe(kw,e,t),Xe(gw,e,t),Xe(Bw,e,t),Xe(xw,e,t),Xe(cw,e,t),Xe(wS,e,t),Xe(Jp,e,t),Xe(hS,e,t),Xe(Xp,e,t),Xe(lS,e,t),Xe(Ff,e,t),Xe(qw,e,t),Xe(Jw,e,t),Xe(xS,e,t),Cw(e,t),zw(e,t),Zp(e,t),Hw(e,t),SS(e,t),Aw(e,t),nS(e),sS(e,t),Tw(e,t),fw(e,t),yw(e,t),dw(e,t),jw(e,t),ew(e,t),CS(e,t),Sw(e,t),eS(e,t),_w(e,t),Qp(e),nw(e,t),iw(e,t),qp(e,t),Xw(e,t),Ww(e,t),tS(e),Ef(e,t),yS(e,t),PS(e,t)},ES=e=>{const{classes:t,headerAlign:n,isDragging:o,isLastColumn:r,showLeftBorder:l,showRightBorder:s,groupId:i,pinnedPosition:c}=e,d={root:["columnHeader",n==="left"&&"columnHeader--alignLeft",n==="center"&&"columnHeader--alignCenter",n==="right"&&"columnHeader--alignRight",o&&"columnHeader--moving",s&&"columnHeader--withRightBorder",l&&"columnHeader--withLeftBorder","withBorderColor",i===null?"columnHeader--emptyGroup":"columnHeader--filledGroup",c===ve.LEFT&&"columnHeader--pinnedLeft",c===ve.RIGHT&&"columnHeader--pinnedRight",r&&"columnHeader--last"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer","withBorderColor"],titleContainerContent:["columnHeaderTitleContainerContent"]};return xe(d,ye,t)};function kS(e){var z;const{groupId:t,width:n,depth:o,maxDepth:r,fields:l,height:s,colIndex:i,hasFocus:c,tabIndex:d,isLastColumn:u,pinnedPosition:g,pinnedOffset:h}=e,m=Z(),S=Dt(),y=a.useRef(null),p=me(),v=W(p,Ii),I=t?v[t]:{},{headerName:D=t??"",description:H="",headerAlign:k=void 0}=I;let $;const L=t&&((z=v[t])==null?void 0:z.renderHeaderGroup),x=a.useMemo(()=>({groupId:t,headerName:D,description:H,depth:o,maxDepth:r,fields:l,colIndex:i,isLastColumn:u}),[t,D,H,o,r,l,i,u]);t&&L&&($=L(x));const w=f({},e,{classes:m.classes,headerAlign:k,depth:o,isDragging:!1}),T=D??t,M=Ae(),O=t===null?`empty-group-cell-${M}`:t,G=ES(w);a.useLayoutEffect(()=>{if(c){const V=y.current.querySelector('[tabindex="0"]')||y.current;V==null||V.focus()}},[p,c]);const A=a.useCallback(_=>V=>{eo(V)||p.current.publishEvent(_,x,V)},[p,x]),F=a.useMemo(()=>({onKeyDown:A("columnGroupHeaderKeyDown"),onFocus:A("columnGroupHeaderFocus"),onBlur:A("columnGroupHeaderBlur")}),[A]),E=typeof I.headerClassName=="function"?I.headerClassName(x):I.headerClassName,P=a.useMemo(()=>Ro(f({},e.style),S,g,h),[g,h,e.style,S]);return C.jsx(Wi,f({ref:y,classes:G,columnMenuOpen:!1,colIndex:i,height:s,isResizing:!1,sortDirection:null,hasFocus:!1,tabIndex:d,isDraggable:!1,headerComponent:$,headerClassName:E,description:H,elementId:O,width:n,columnMenuIconButton:null,columnTitleIconButtons:null,resizable:!1,label:T,"aria-colspan":l.length,"data-fields":`|-${l.join("-|-")}-|`,style:P},F))}const gs=Ne("div",{name:"MuiDataGrid",slot:"ColumnHeaderRow"})({display:"flex"}),TS=e=>{const{visibleColumns:t,sortColumnLookup:n,filterColumnLookup:o,columnHeaderTabIndexState:r,columnGroupHeaderTabIndexState:l,columnHeaderFocus:s,columnGroupHeaderFocus:i,headerGroupingMaxDepth:c,columnMenuState:d,columnVisibility:u,columnGroupsHeaderStructure:g,hasOtherElementInTabSequence:h}=e,[m,S]=a.useState(""),[y,p]=a.useState(""),v=ut(),I=Z(),D=W(v,vi),H=W(v,qt),k=W(v,Pf),$=W(v,Mn),L=W(v,At),x=Di(H,k,$.left.length),w=W(v,Mo),T=W(v,Ds),M=W(v,Lu),O=W(v,$u),G=W(v,Hs),A=a.useCallback(Y=>p(Y.field),[]),F=a.useCallback(()=>p(""),[]),E=a.useCallback(Y=>S(Y.field),[]),P=a.useCallback(()=>S(""),[]),z=a.useMemo(()=>$.left.length?{firstColumnIndex:0,lastColumnIndex:$.left.length}:null,[$.left.length]),_=a.useMemo(()=>$.right.length?{firstColumnIndex:t.length-$.right.length,lastColumnIndex:t.length}:null,[$.right.length,t.length]);le(v,"columnResizeStart",A),le(v,"columnResizeStop",F),le(v,"columnHeaderDragStart",E),le(v,"columnHeaderDragEndNative",P);const V=Y=>{const{renderContext:ae=k}=Y||{},ce=ae.firstColumnIndex,j=ae.lastColumnIndex;return{renderedColumns:t.slice(ce,j),firstColumnToRender:ce,lastColumnToRender:j}},R=(Y,ae,ce,j=!1)=>{const K=(Y==null?void 0:Y.position)===ve.RIGHT,te=(Y==null?void 0:Y.position)===void 0,ne=$.right.length>0&&K||$.right.length===0&&te,U=x-ce;return C.jsxs(a.Fragment,{children:[te&&C.jsx("div",{role:"presentation",style:{width:U}}),ae,te&&C.jsx("div",{role:"presentation",className:Fe(b.filler,j&&b["filler--borderBottom"])}),ne&&C.jsx(jr,{header:!0,pinnedRight:K,borderBottom:j,borderTop:!1})]})},B=(Y,ae={})=>{const{renderedColumns:ce,firstColumnToRender:j}=V(Y),K=[];for(let te=0;te<ce.length;te+=1){const ne=ce[te],U=j+te,J=U===0,ie=r!==null&&r.field===ne.field||J&&!h?0:-1,fe=s!==null&&s.field===ne.field,he=d.open&&d.field===ne.field,ge=Y==null?void 0:Y.position,be=$n(ge,ne.computedWidth,U,H,w,G),Me=ge===ve.RIGHT?ce[te-1]:ce[te+1],De=Me?s!==null&&s.field===Me.field:!1,se=U+1===H.length-$.right.length,Ce=te,Se=ce.length,Pe=vo(ge,Ce),Ge=yo(ge,Ce,Se,I.showColumnVerticalBorder,T);K.push(C.jsx(Qm,f({},n[ne.field],{columnMenuOpen:he,filterItemsCounter:o[ne.field]&&o[ne.field].length,headerHeight:M,isDragging:ne.field===m,colDef:ne,colIndex:U,isResizing:y===ne.field,isLast:U===H.length-1,hasFocus:fe,tabIndex:ie,pinnedPosition:ge,pinnedOffset:be,isLastUnpinned:se,isSiblingFocused:De,showLeftBorder:Pe,showRightBorder:Ge},ae),ne.field))}return R(Y,K,0)},q=()=>C.jsxs(gs,{role:"row","aria-rowindex":c+1,ownerState:I,className:b["row--borderBottom"],style:{height:M},children:[z&&B({position:ve.LEFT,renderContext:z},{disableReorder:!0}),B({renderContext:k}),_&&B({position:ve.RIGHT,renderContext:_},{disableReorder:!0,separatorSide:Nr.Left})]}),X=({depth:Y,params:ae})=>{var Se,Pe;const ce=V(ae);if(ce.renderedColumns.length===0)return null;const{firstColumnToRender:j,lastColumnToRender:K}=ce,te=g[Y],ne=t[j].field,U=((Se=D[ne])==null?void 0:Se[Y])??null,J=te.findIndex(({groupId:Ge,columnFields:ze})=>Ge===U&&ze.includes(ne)),ie=t[K-1].field,fe=((Pe=D[ie])==null?void 0:Pe[Y])??null,he=te.findIndex(({groupId:Ge,columnFields:ze})=>Ge===fe&&ze.includes(ie)),ge=te.slice(J,he+1).map(Ge=>f({},Ge,{columnFields:Ge.columnFields.filter(ze=>u[ze]!==!1)})).filter(Ge=>Ge.columnFields.length>0),be=ge[0].columnFields.indexOf(ne),De=ge[0].columnFields.slice(0,be).reduce((Ge,ze)=>{const Ie=L[ze];return Ge+(Ie.computedWidth??0)},0);let se=j;const Ce=ge.map(({groupId:Ge,columnFields:ze},Ie)=>{const de=i!==null&&i.depth===Y&&ze.includes(i.field),He=l!==null&&l.depth===Y&&ze.includes(l.field)?0:-1,Ee={width:ze.reduce((Ue,Ke)=>Ue+L[Ke].computedWidth,0),fields:ze,colIndex:se},ot=ae.position,dt=$n(ot,Ee.width,se,H,w,G);se+=ze.length;let pe=Ie;return ot===ve.LEFT&&(pe=se-1),C.jsx(kS,{groupId:Ge,width:Ee.width,fields:Ee.fields,colIndex:Ee.colIndex,depth:Y,isLastColumn:Ie===ge.length-1,maxDepth:c,height:O,hasFocus:de,tabIndex:He,pinnedPosition:ot,pinnedOffset:dt,showLeftBorder:vo(ot,pe),showRightBorder:yo(ot,pe,ge.length,I.showColumnVerticalBorder,T)},Ie)});return R(ae,Ce,De)};return{renderContext:k,leftRenderContext:z,rightRenderContext:_,pinnedColumns:$,visibleColumns:t,columnPositions:H,getFillers:R,getColumnHeadersRow:q,getColumnsToRender:V,getColumnGroupHeadersRows:()=>{if(c===0)return null;const Y=[];for(let ae=0;ae<c;ae+=1)Y.push(C.jsxs(gs,{role:"row","aria-rowindex":ae+1,ownerState:I,style:{height:O},children:[z&&X({depth:ae,params:{position:ve.LEFT,renderContext:z,maxLastColumn:z.lastColumnIndex}}),X({depth:ae,params:{renderContext:k}}),_&&X({depth:ae,params:{position:ve.RIGHT,renderContext:_,maxLastColumn:_.lastColumnIndex}})]},ae));return Y},getPinnedCellOffset:$n,isDragging:!!m,getInnerProps:()=>({role:"rowgroup"})}},DS=["className"],HS=e=>{const{classes:t}=e;return xe({root:["columnHeaders"]},ye,t)},OS=ke("div",{name:"MuiDataGrid",slot:"ColumnHeaders"})({display:"flex",flexDirection:"column",borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"}),GS=ue(function(t,n){const{className:o}=t,r=Q(t,DS),l=Z(),s=HS(l);return C.jsx(OS,f({className:Fe(s.root,o),ownerState:l},r,{role:"presentation",ref:n}))}),LS=["className","visibleColumns","sortColumnLookup","filterColumnLookup","columnHeaderTabIndexState","columnGroupHeaderTabIndexState","columnHeaderFocus","columnGroupHeaderFocus","headerGroupingMaxDepth","columnMenuState","columnVisibility","columnGroupsHeaderStructure","hasOtherElementInTabSequence"],$S=ue(function(t,n){const{visibleColumns:o,sortColumnLookup:r,filterColumnLookup:l,columnHeaderTabIndexState:s,columnGroupHeaderTabIndexState:i,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:u,columnMenuState:g,columnVisibility:h,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:S}=t,y=Q(t,LS),{getInnerProps:p,getColumnHeadersRow:v,getColumnGroupHeadersRows:I}=TS({visibleColumns:o,sortColumnLookup:r,filterColumnLookup:l,columnHeaderTabIndexState:s,columnGroupHeaderTabIndexState:i,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:u,columnMenuState:g,columnVisibility:h,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:S});return C.jsxs(GS,f({},y,p(),{ref:n,children:[I(),v()]}))}),AS=Lt($S);function RS(e){return null}function zS(e){return null}const VS=ue(function(t,n){const r=me().current.getLocaleText("noResultsOverlayLabel");return C.jsx(zo,f({},t,{ref:n,children:r}))}),NS=()=>xe({root:["bottomContainer"]},ye,{}),BS=ke("div")({position:"sticky",zIndex:40,bottom:"calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))"});function jS(e){const t=NS();return C.jsx(BS,f({},e,{className:Fe(t.root,b["container--bottom"]),role:"presentation"}))}const _S=f({},hu,{cell:Lh,skeletonCell:Bh,columnHeaderFilterIconButton:uC,columnHeaderSortIcon:cC,columnMenu:EC,columnHeaders:AS,detailPanels:RS,bottomContainer:jS,footer:sp,footerRowCount:yp,toolbar:tp,pinnedRows:zS,loadingOverlay:hp,noResultsOverlay:VS,noRowsOverlay:mp,noColumnsOverlay:Cp,pagination:pp,filterPanel:ob,columnsPanel:HC,columnsManagement:gb,panel:AC,row:Fp}),WS={disableMultipleColumnsFiltering:!0,disableMultipleColumnsSorting:!0,throttleRowsMs:void 0,hideFooterRowCount:!1,pagination:!0,checkboxSelectionVisibleOnly:!1,disableColumnReorder:!0,keepColumnPositionIfDraggedOutside:!1,signature:"DataGrid",listView:!1},US=e=>f({},WS,e.dataSource?{filterMode:"server",sortingMode:"server",paginationMode:"server"}:{}),KS=_S,qS=e=>{const t=hs(),n=a.useMemo(()=>jc({props:e,theme:t,name:"MuiDataGrid"}),[t,e]),o=a.useMemo(()=>f({},Vu,n.localeText),[n.localeText]),r=a.useMemo(()=>Eh({defaultSlots:KS,slots:n.slots}),[n.slots]),l=a.useMemo(()=>Object.keys(fr).reduce((s,i)=>(s[i]=n[i]??fr[i],s),{}),[n]);return a.useMemo(()=>f({},n,l,{localeText:o,slots:r},US(n)),[n,o,r,l])};class XS{constructor(){this.maxListeners=20,this.warnOnce=!1,this.events={}}on(t,n,o={}){let r=this.events[t];r||(r={highPriority:new Map,regular:new Map},this.events[t]=r),o.isFirst?r.highPriority.set(n,!0):r.regular.set(n,!0)}removeListener(t,n){this.events[t]&&(this.events[t].regular.delete(n),this.events[t].highPriority.delete(n))}removeAllListeners(){this.events={}}emit(t,...n){const o=this.events[t];if(!o)return;const r=Array.from(o.highPriority.keys()),l=Array.from(o.regular.keys());for(let s=r.length-1;s>=0;s-=1){const i=r[s];o.highPriority.has(i)&&i.apply(this,n)}for(let s=0;s<l.length;s+=1){const i=l[s];o.regular.has(i)&&i.apply(this,n)}}once(t,n){const o=this;this.on(t,function r(...l){o.removeListener(t,r),n.apply(o,l)})}}const ic=Symbol("mui.api_private"),QS=e=>e.isPropagationStopped!==void 0;let fs=0;function YS(e){var r;const t=(r=e.current)==null?void 0:r[ic];if(t)return t;const n={},o={state:n,store:wr.create(n),instanceId:{id:fs}};return fs+=1,o.getPublicApi=()=>e.current,o.register=(l,s)=>{Object.keys(s).forEach(i=>{const c=s[i],d=o[i];if((d==null?void 0:d.spying)===!0?d.target=c:o[i]=c,l==="public"){const u=e.current,g=u[i];(g==null?void 0:g.spying)===!0?g.target=c:u[i]=c}})},o.register("private",{caches:{},eventManager:new XS}),o}function ZS(e){return{get state(){return e.current.state},get store(){return e.current.store},get instanceId(){return e.current.instanceId},[ic]:e.current}}function JS(e,t){var s;const n=a.useRef(null),o=a.useRef(null);o.current||(o.current=YS(n)),n.current||(n.current=ZS(o));const r=a.useCallback((...i)=>{const[c,d,u={}]=i;if(u.defaultMuiPrevented=!1,QS(u)&&u.isPropagationStopped())return;const g=t.signature===vt.DataGridPro||t.signature===vt.DataGridPremium?{api:o.current.getPublicApi()}:{};o.current.eventManager.emit(c,d,u,g)},[o,t.signature]),l=a.useCallback((i,c,d)=>{o.current.eventManager.on(i,c,d);const u=o.current;return()=>{u.eventManager.removeListener(i,c)}},[o]);return we(o,{subscribeEvent:l,publishEvent:r},"public"),e&&!((s=e.current)!=null&&s.state)&&(e.current=n.current),a.useImperativeHandle(e,()=>n.current,[n]),a.useEffect(()=>{const i=o.current;return()=>{i.publishEvent("unmount")}},[o]),o}const ex={hooks:{useCSSVariables:ha,useGridAriaAttributes:Ep,useGridRowAriaAttributes:kp,useCellAggregationResult:()=>null}},cc=function(t,n){var l;const o=qS(t),r=JS(o.apiRef,o);return FS(r,o),C.jsx(Tp,{privateApiRef:r,configuration:ex,props:o,children:C.jsx(rm,f({className:o.className,style:o.style,sx:o.sx},(l=o.slotProps)==null?void 0:l.root,{ref:n}))})},sx=a.memo(ue(cc));cc.propTypes={apiRef:N.shape({current:N.object}),"aria-label":N.string,"aria-labelledby":N.string,autoHeight:N.bool,autoPageSize:N.bool,autosizeOnMount:N.bool,autosizeOptions:N.shape({columns:N.arrayOf(N.string),disableColumnVirtualization:N.bool,expand:N.bool,includeHeaders:N.bool,includeOutliers:N.bool,outliersFactor:N.number}),cellModesModel:N.object,checkboxSelection:N.bool,classes:N.object,className:N.string,clipboardCopyCellDelimiter:N.string,columnBufferPx:N.number,columnGroupHeaderHeight:N.number,columnGroupingModel:N.arrayOf(N.object),columnHeaderHeight:N.number,columns:N.arrayOf(N.object).isRequired,columnVisibilityModel:N.object,dataSource:N.shape({getRows:N.func.isRequired,updateRow:N.func}),dataSourceCache:N.shape({clear:N.func.isRequired,get:N.func.isRequired,set:N.func.isRequired}),density:N.oneOf(["comfortable","compact","standard"]),disableAutosize:N.bool,disableColumnFilter:N.bool,disableColumnMenu:N.bool,disableColumnResize:N.bool,disableColumnSelector:N.bool,disableColumnSorting:N.bool,disableDensitySelector:N.bool,disableEval:N.bool,disableMultipleRowSelection:N.bool,disableRowSelectionOnClick:N.bool,disableVirtualization:N.bool,editMode:N.oneOf(["cell","row"]),estimatedRowCount:N.number,experimentalFeatures:N.shape({warnIfFocusStateIsNotSynced:N.bool}),filterDebounceMs:N.number,filterMode:N.oneOf(["client","server"]),filterModel:N.shape({items:N.arrayOf(N.shape({field:N.string.isRequired,id:N.oneOfType([N.number,N.string]),operator:N.string.isRequired,value:N.any})).isRequired,logicOperator:N.oneOf(["and","or"]),quickFilterExcludeHiddenColumns:N.bool,quickFilterLogicOperator:N.oneOf(["and","or"]),quickFilterValues:N.array}),getCellClassName:N.func,getDetailPanelContent:N.func,getEstimatedRowHeight:N.func,getRowClassName:N.func,getRowHeight:N.func,getRowId:N.func,getRowSpacing:N.func,hideFooter:N.bool,hideFooterPagination:N.bool,hideFooterSelectedRowCount:N.bool,ignoreDiacritics:N.bool,ignoreValueFormatterDuringExport:N.oneOfType([N.shape({clipboardExport:N.bool,csvExport:N.bool}),N.bool]),initialState:N.object,isCellEditable:N.func,isRowSelectable:N.func,keepNonExistentRowsSelected:N.bool,label:N.string,loading:N.bool,localeText:N.object,logger:N.shape({debug:N.func.isRequired,error:N.func.isRequired,info:N.func.isRequired,warn:N.func.isRequired}),logLevel:N.oneOf(["debug","error","info","warn",!1]),nonce:N.string,onCellClick:N.func,onCellDoubleClick:N.func,onCellEditStart:N.func,onCellEditStop:N.func,onCellKeyDown:N.func,onCellModesModelChange:N.func,onClipboardCopy:N.func,onColumnHeaderClick:N.func,onColumnHeaderContextMenu:N.func,onColumnHeaderDoubleClick:N.func,onColumnHeaderEnter:N.func,onColumnHeaderLeave:N.func,onColumnHeaderOut:N.func,onColumnHeaderOver:N.func,onColumnOrderChange:N.func,onColumnResize:N.func,onColumnVisibilityModelChange:N.func,onColumnWidthChange:N.func,onDataSourceError:N.func,onDensityChange:N.func,onFilterModelChange:N.func,onMenuClose:N.func,onMenuOpen:N.func,onPaginationMetaChange:N.func,onPaginationModelChange:N.func,onPreferencePanelClose:N.func,onPreferencePanelOpen:N.func,onProcessRowUpdateError:N.func,onResize:N.func,onRowClick:N.func,onRowCountChange:N.func,onRowDoubleClick:N.func,onRowEditStart:N.func,onRowEditStop:N.func,onRowModesModelChange:N.func,onRowSelectionModelChange:N.func,onSortModelChange:N.func,onStateChange:N.func,pageSizeOptions:N.arrayOf(N.oneOfType([N.number,N.shape({label:N.string.isRequired,value:N.number.isRequired})]).isRequired),pagination:N.oneOf([!0]),paginationMeta:N.shape({hasNextPage:N.bool}),paginationMode:N.oneOf(["client","server"]),paginationModel:N.shape({page:N.number.isRequired,pageSize:N.number.isRequired}),processRowUpdate:N.func,resizeThrottleMs:N.number,rowBufferPx:N.number,rowCount:N.number,rowHeight:N.number,rowModesModel:N.object,rows:N.arrayOf(N.object),rowSelection:N.bool,rowSelectionModel:N.shape({ids:N.instanceOf(Set).isRequired,type:N.oneOf(["exclude","include"]).isRequired}),rowSpacingType:N.oneOf(["border","margin"]),rowSpanning:N.bool,scrollbarSize:N.number,showCellVerticalBorder:N.bool,showColumnVerticalBorder:N.bool,showToolbar:N.bool,slotProps:N.object,slots:N.object,sortingMode:N.oneOf(["client","server"]),sortingOrder:N.arrayOf(N.oneOf(["asc","desc"])),sortModel:N.arrayOf(N.shape({field:N.string.isRequired,sort:N.oneOf(["asc","desc"])})),style:N.object,sx:N.oneOfType([N.arrayOf(N.oneOfType([N.func,N.object,N.bool])),N.func,N.object]),virtualizeColumnsWithAutoRowHeight:N.bool};export{sx as D,Fn as G};
