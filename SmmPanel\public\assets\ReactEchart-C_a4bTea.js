import{c8 as j0,c9 as Q0,aT as J0,g as Np,ca as Na,h as t1,r as oc,_ as sc,j as e1,B as r1}from"./index-CP4gzJXp.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON>IGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Sl=function(r,t){return Sl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])},Sl(r,t)};function H(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Sl(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Lf=12,n1="sans-serif",dn=Lf+"px "+n1,i1=20,a1=100,o1="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function s1(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var n=String.fromCharCode(e+32),i=(r.charCodeAt(e)-i1)/a1;t[n]=i}return t}var u1=s1(o1),or={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,n){if(!r){var i=or.createCanvas();r=i&&i.getContext("2d")}if(r)return t!==n&&(t=r.font=n||dn),r.measureText(e);e=e||"",n=n||dn;var a=/((?:\d+)?\.?\d*)px/.exec(n),o=a&&+a[1]||Lf,s=0;if(n.indexOf("mono")>=0)s=o*e.length;else for(var u=0;u<e.length;u++){var l=u1[e[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(r,t,e){var n=new Image;return n.onload=t,n.onerror=e,n.src=r,n}};function zp(r){for(var t in or)r[t]&&(or[t]=r[t])}var Hp=Ue(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),Gp=Ue(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),jn=Object.prototype.toString,ss=Array.prototype,l1=ss.forEach,f1=ss.filter,If=ss.slice,h1=ss.map,uc=(function(){}).constructor,za=uc?uc.prototype:null,Pf="__proto__",c1=2311;function Rf(){return c1++}function us(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function et(r){if(r==null||typeof r!="object")return r;var t=r,e=jn.call(r);if(e==="[object Array]"){if(!Gn(r)){t=[];for(var n=0,i=r.length;n<i;n++)t[n]=et(r[n])}}else if(Gp[e]){if(!Gn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var n=0,i=r.length;n<i;n++)t[n]=r[n]}}}else if(!Hp[e]&&!Gn(r)&&!Xn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==Pf&&(t[o]=et(r[o]))}return t}function nt(r,t,e){if(!V(t)||!V(r))return e?et(t):r;for(var n in t)if(t.hasOwnProperty(n)&&n!==Pf){var i=r[n],a=t[n];V(a)&&V(i)&&!N(a)&&!N(i)&&!Xn(a)&&!Xn(i)&&!wl(a)&&!wl(i)&&!Gn(a)&&!Gn(i)?nt(i,a,e):(e||!(n in r))&&(r[n]=et(t[n]))}return r}function v1(r,t){for(var e=r[0],n=1,i=r.length;n<i;n++)e=nt(e,r[n],t);return e}function B(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==Pf&&(r[e]=t[e]);return r}function ft(r,t,e){for(var n=yt(t),i=0,a=n.length;i<a;i++){var o=n[i];(e?t[o]!=null:r[o]==null)&&(r[o]=t[o])}return r}var d1=or.createCanvas;function lt(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,n=r.length;e<n;e++)if(r[e]===t)return e}return-1}function Ef(r,t){var e=r.prototype;function n(){}n.prototype=t.prototype,r.prototype=new n;for(var i in e)e.hasOwnProperty(i)&&(r.prototype[i]=e[i]);r.prototype.constructor=r,r.superClass=t}function _e(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var n=Object.getOwnPropertyNames(t),i=0;i<n.length;i++){var a=n[i];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else ft(r,t,e)}function Yt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function C(r,t,e){if(r&&t)if(r.forEach&&r.forEach===l1)r.forEach(t,e);else if(r.length===+r.length)for(var n=0,i=r.length;n<i;n++)t.call(e,r[n],n,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function W(r,t,e){if(!r)return[];if(!t)return ls(r);if(r.map&&r.map===h1)return r.map(t,e);for(var n=[],i=0,a=r.length;i<a;i++)n.push(t.call(e,r[i],i,r));return n}function Ue(r,t,e,n){if(r&&t){for(var i=0,a=r.length;i<a;i++)e=t.call(n,e,r[i],i,r);return e}}function Tt(r,t,e){if(!r)return[];if(!t)return ls(r);if(r.filter&&r.filter===f1)return r.filter(t,e);for(var n=[],i=0,a=r.length;i<a;i++)t.call(e,r[i],i,r)&&n.push(r[i]);return n}function p1(r,t,e){if(r&&t){for(var n=0,i=r.length;n<i;n++)if(t.call(e,r[n],n,r))return r[n]}}function yt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function g1(r,t){for(var e=[],n=2;n<arguments.length;n++)e[n-2]=arguments[n];return function(){return r.apply(t,e.concat(If.call(arguments)))}}var pt=za&&j(za.bind)?za.call.bind(za.bind):g1;function Ct(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(If.call(arguments)))}}function N(r){return Array.isArray?Array.isArray(r):jn.call(r)==="[object Array]"}function j(r){return typeof r=="function"}function G(r){return typeof r=="string"}function Fo(r){return jn.call(r)==="[object String]"}function wt(r){return typeof r=="number"}function V(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function wl(r){return!!Hp[jn.call(r)]}function qt(r){return!!Gp[jn.call(r)]}function Xn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function pa(r){return r.colorStops!=null}function Vp(r){return r.image!=null}function y1(r){return jn.call(r)==="[object RegExp]"}function ji(r){return r!==r}function $n(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,n=r.length;e<n;e++)if(r[e]!=null)return r[e]}function J(r,t){return r??t}function Hi(r,t,e){return r??t??e}function ls(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return If.apply(r,t)}function Of(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function Re(r,t){if(!r)throw new Error(t)}function Ae(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Wp="__ec_primitive__";function No(r){r[Wp]=!0}function Gn(r){return r[Wp]}var m1=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return yt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},r}(),Up=typeof Map=="function";function _1(){return Up?new Map:new m1}var Yp=function(){function r(t){var e=N(t);this.data=_1();var n=this;t instanceof r?t.each(i):t&&C(t,i);function i(a,o){e?n.set(a,o):n.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(n,i){t.call(e,n,i)})},r.prototype.keys=function(){var t=this.data.keys();return Up?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function Z(r){return new Yp(r)}function qp(r,t){for(var e=new r.constructor(r.length+t.length),n=0;n<r.length;n++)e[n]=r[n];for(var i=r.length,n=0;n<t.length;n++)e[n+i]=t[n];return e}function ga(r,t){var e;if(Object.create)e=Object.create(r);else{var n=function(){};n.prototype=r,e=new n}return t&&B(e,t),e}function kf(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Dr(r,t){return r.hasOwnProperty(t)}function Wt(){}var Xp=180/Math.PI;const S1=Object.freeze(Object.defineProperty({__proto__:null,HashMap:Yp,RADIAN_TO_DEGREE:Xp,assert:Re,bind:pt,clone:et,concatArray:qp,createCanvas:d1,createHashMap:Z,createObject:ga,curry:Ct,defaults:ft,disableUserSelect:kf,each:C,eqNaN:ji,extend:B,filter:Tt,find:p1,guid:Rf,hasOwn:Dr,indexOf:lt,inherits:Ef,isArray:N,isArrayLike:Yt,isBuiltInObject:wl,isDom:Xn,isFunction:j,isGradientObject:pa,isImagePatternObject:Vp,isNumber:wt,isObject:V,isPrimitive:Gn,isRegExp:y1,isString:G,isStringSafe:Fo,isTypedArray:qt,keys:yt,logError:us,map:W,merge:nt,mergeAll:v1,mixin:_e,noop:Wt,normalizeCssArray:Of,reduce:Ue,retrieve:$n,retrieve2:J,retrieve3:Hi,setAsPrimitive:No,slice:ls,trim:Ae},Symbol.toStringTag,{value:"Module"}));var w1=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),x1=function(){function r(){this.browser=new w1,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),Y=new x1;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(Y.wxa=!0,Y.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?Y.worker=!0:!Y.hasGlobalWindow||"Deno"in window?(Y.node=!0,Y.svgSupported=!0):b1(navigator.userAgent,Y);function b1(r,t){var e=t.browser,n=r.match(/Firefox\/([\d.]+)/),i=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);n&&(e.firefox=!0,e.version=n[1]),i&&(e.ie=!0,e.version=i[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}var T1=".",Br="___EC__COMPONENT__CONTAINER___",$p="___EC__EXTENDED_CLASS___";function Ge(r){var t={main:"",sub:""};if(r){var e=r.split(T1);t.main=e[0]||"",t.sub=e[1]||""}return t}function C1(r){Re(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function M1(r){return!!(r&&r[$p])}function Bf(r,t){r.$constructor=r,r.extend=function(e){var n=this,i;return D1(n)?i=function(a){H(o,a);function o(){return a.apply(this,arguments)||this}return o}(n):(i=function(){(e.$constructor||n).apply(this,arguments)},Ef(i,this)),B(i.prototype,e),i[$p]=!0,i.extend=this.extend,i.superCall=I1,i.superApply=P1,i.superClass=n,i}}function D1(r){return j(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function Zp(r,t){r.extend=t.extend}var A1=Math.round(Math.random()*10);function L1(r){var t=["__\0is_clz",A1++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function I1(r,t){for(var e=[],n=2;n<arguments.length;n++)e[n-2]=arguments[n];return this.superClass.prototype[t].apply(r,e)}function P1(r,t,e){return this.superClass.prototype[t].apply(r,e)}function fs(r){var t={};r.registerClass=function(n){var i=n.type||n.prototype.type;if(i){C1(i),n.prototype.type=i;var a=Ge(i);if(!a.sub)t[a.main]=n;else if(a.sub!==Br){var o=e(a);o[a.sub]=n}}return n},r.getClass=function(n,i,a){var o=t[n];if(o&&o[Br]&&(o=i?o[i]:null),a&&!o)throw new Error(i?"Component "+n+"."+(i||"")+" is used but not imported.":n+".type should be specified.");return o},r.getClassesByMainType=function(n){var i=Ge(n),a=[],o=t[i.main];return o&&o[Br]?C(o,function(s,u){u!==Br&&a.push(s)}):a.push(o),a},r.hasClass=function(n){var i=Ge(n);return!!t[i.main]},r.getAllClassMainTypes=function(){var n=[];return C(t,function(i,a){n.push(a)}),n},r.hasSubTypes=function(n){var i=Ge(n),a=t[i.main];return a&&a[Br]};function e(n){var i=t[n.main];return(!i||!i[Br])&&(i=t[n.main]={},i[Br]=!0),i}}function Qi(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(n,i,a){for(var o={},s=0;s<r.length;s++){var u=r[s][1];if(!(i&&lt(i,u)>=0||a&&lt(a,u)<0)){var l=n.getShallow(u,t);l!=null&&(o[r[s][0]]=l)}}return o}}var R1=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],E1=Qi(R1),O1=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return E1(this,t,e)},r}(),Kp=function(){function r(t){this.value=t}return r}(),k1=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Kp(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),ya=function(){function r(t){this._list=new k1,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var n=this._list,i=this._map,a=null;if(i[t]==null){var o=n.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=n.head;n.remove(u),delete i[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new Kp(e),s.key=t,n.insertEntry(s),i[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],n=this._list;if(e!=null)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}(),xl=new ya(50);function B1(r){if(typeof r=="string"){var t=xl.get(r);return t&&t.image}else return r}function jp(r,t,e,n,i){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=xl.get(r),o={hostEl:e,cb:n,cbPayload:i};return a?(t=a.image,!hs(t)&&a.pending.push(o)):(t=or.loadImage(r,lc,lc),t.__zrImageSrc=r,xl.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function lc(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],n=e.cb;n&&n(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function hs(r){return r&&r.width&&r.height}function xr(){return[1,0,0,1,0,0]}function ma(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function Ff(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function br(r,t,e){var n=t[0]*e[0]+t[2]*e[1],i=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],u=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=n,r[1]=i,r[2]=a,r[3]=o,r[4]=s,r[5]=u,r}function zo(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function cs(r,t,e,n){n===void 0&&(n=[0,0]);var i=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=i*h+s*f,r[1]=-i*f+s*h,r[2]=a*h+u*f,r[3]=-a*f+h*u,r[4]=h*(o-n[0])+f*(l-n[1])+n[0],r[5]=h*(l-n[1])-f*(o-n[0])+n[1],r}function Qp(r,t,e){var n=e[0],i=e[1];return r[0]=t[0]*n,r[1]=t[1]*i,r[2]=t[2]*n,r[3]=t[3]*i,r[4]=t[4]*n,r[5]=t[5]*i,r}function _a(r,t){var e=t[0],n=t[2],i=t[4],a=t[1],o=t[3],s=t[5],u=e*o-a*n;return u?(u=1/u,r[0]=o*u,r[1]=-a*u,r[2]=-n*u,r[3]=e*u,r[4]=(n*s-o*i)*u,r[5]=(a*i-e*s)*u,r):null}function F1(r){var t=xr();return Ff(t,r),t}const N1=Object.freeze(Object.defineProperty({__proto__:null,clone:F1,copy:Ff,create:xr,identity:ma,invert:_a,mul:br,rotate:cs,scale:Qp,translate:zo},Symbol.toStringTag,{value:"Module"}));var vt=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,n){t.x=e,t.y=n},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},r.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},r.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},r.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},r.lerp=function(t,e,n,i){var a=1-i;t.x=a*e.x+i*n.x,t.y=a*e.y+i*n.y},r}(),Ha=Math.min,Ga=Math.max,Fr=new vt,Nr=new vt,zr=new vt,Hr=new vt,ai=new vt,oi=new vt,it=function(){function r(t,e,n,i){n<0&&(t=t+n,n=-n),i<0&&(e=e+i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return r.prototype.union=function(t){var e=Ha(t.x,this.x),n=Ha(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ga(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ga(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,a=xr();return zo(a,a,[-e.x,-e.y]),Qp(a,a,[n,i]),zo(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var n=this,i=n.x,a=n.x+n.width,o=n.y,s=n.y+n.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,v=!(a<u||l<i||s<f||h<o);if(e){var c=1/0,d=0,g=Math.abs(a-u),p=Math.abs(l-i),y=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(g,p),S=Math.min(y,m);a<u||l<i?_>d&&(d=_,g<p?vt.set(oi,-g,0):vt.set(oi,p,0)):_<c&&(c=_,g<p?vt.set(ai,g,0):vt.set(ai,-p,0)),s<f||h<o?S>d&&(d=S,y<m?vt.set(oi,0,-y):vt.set(oi,0,m)):_<c&&(c=_,y<m?vt.set(ai,0,y):vt.set(ai,0,-m))}return e&&vt.copy(e,v?ai:oi),v},r.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,n){if(!n){t!==e&&r.copy(t,e);return}if(n[1]<1e-5&&n[1]>-1e-5&&n[2]<1e-5&&n[2]>-1e-5){var i=n[0],a=n[3],o=n[4],s=n[5];t.x=e.x*i+o,t.y=e.y*a+s,t.width=e.width*i,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}Fr.x=zr.x=e.x,Fr.y=Hr.y=e.y,Nr.x=Hr.x=e.x+e.width,Nr.y=zr.y=e.y+e.height,Fr.transform(n),Hr.transform(n),Nr.transform(n),zr.transform(n),t.x=Ha(Fr.x,Nr.x,zr.x,Hr.x),t.y=Ha(Fr.y,Nr.y,zr.y,Hr.y);var u=Ga(Fr.x,Nr.x,zr.x,Hr.x),l=Ga(Fr.y,Nr.y,zr.y,Hr.y);t.width=u-t.x,t.height=l-t.y},r}(),fc={};function ne(r,t){t=t||dn;var e=fc[t];e||(e=fc[t]=new ya(500));var n=e.get(r);return n==null&&(n=or.measureText(r,t).width,e.put(r,n)),n}function hc(r,t,e,n){var i=ne(r,t),a=zf(t),o=Ri(0,i,e),s=Fn(0,a,n),u=new it(o,s,i,a);return u}function Nf(r,t,e,n){var i=((r||"")+"").split(`
`),a=i.length;if(a===1)return hc(i[0],t,e,n);for(var o=new it(0,0,0,0),s=0;s<i.length;s++){var u=hc(i[s],t,e,n);s===0?o.copy(u):o.union(u)}return o}function Ri(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function Fn(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function zf(r){return ne("国",r)}function pn(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Jp(r,t,e){var n=t.position||"inside",i=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,u=e.x,l=e.y,f="left",h="top";if(n instanceof Array)u+=pn(n[0],e.width),l+=pn(n[1],e.height),f=null,h=null;else switch(n){case"left":u-=i,l+=s,f="right",h="middle";break;case"right":u+=i+o,l+=s,h="middle";break;case"top":u+=o/2,l-=i,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+i,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=i,l+=s,h="middle";break;case"insideRight":u+=o-i,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=i,f="center";break;case"insideBottom":u+=o/2,l+=a-i,f="center",h="bottom";break;case"insideTopLeft":u+=i,l+=i;break;case"insideTopRight":u+=o-i,l+=i,f="right";break;case"insideBottomLeft":u+=i,l+=a-i,h="bottom";break;case"insideBottomRight":u+=o-i,l+=a-i,f="right",h="bottom";break}return r=r||{},r.x=u,r.y=l,r.align=f,r.verticalAlign=h,r}var eu=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function z1(r,t,e,n,i){var a={};return tg(a,r,t,e,n,i),a.text}function tg(r,t,e,n,i,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=eg(e,n,i,a);for(var s=!1,u={},l=0,f=o.length;l<f;l++)rg(u,o[l],a),o[l]=u.textLine,s=s||u.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function eg(r,t,e,n){n=n||{};var i=B({},n);i.font=t,e=J(e,"..."),i.maxIterations=J(n.maxIterations,2);var a=i.minChar=J(n.minChar,0);i.cnCharWidth=ne("国",t);var o=i.ascCharWidth=ne("a",t);i.placeholder=J(n.placeholder,"");for(var s=r=Math.max(0,r-1),u=0;u<a&&s>=o;u++)s-=o;var l=ne(e,t);return l>s&&(e="",l=0),s=r-l,i.ellipsis=e,i.ellipsisWidth=l,i.contentWidth=s,i.containerWidth=r,i}function rg(r,t,e){var n=e.containerWidth,i=e.font,a=e.contentWidth;if(!n){r.textLine="",r.isTruncated=!1;return}var o=ne(t,i);if(o<=n){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var u=s===0?H1(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,u),o=ne(t,i)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function H1(r,t,e,n){for(var i=0,a=0,o=r.length;a<o&&i<t;a++){var s=r.charCodeAt(a);i+=0<=s&&s<=127?e:n}return a}function G1(r,t){r!=null&&(r+="");var e=t.overflow,n=t.padding,i=t.font,a=e==="truncate",o=zf(i),s=J(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=!1,h=t.width,v;h!=null&&(e==="break"||e==="breakAll")?v=r?ng(r,t.font,h,e==="breakAll",0).lines:[]:v=r?r.split(`
`):[];var c=v.length*s,d=J(t.height,c);if(c>d&&l){var g=Math.floor(d/s);f=f||v.length>g,v=v.slice(0,g)}if(r&&a&&h!=null)for(var p=eg(h,i,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),y={},m=0;m<v.length;m++)rg(y,v[m],p),v[m]=y.textLine,f=f||y.isTruncated;for(var _=d,S=0,m=0;m<v.length;m++)S=Math.max(ne(v[m],i),S);h==null&&(h=S);var b=S;return n&&(_+=n[0]+n[2],b+=n[1]+n[3],h+=n[1]+n[3]),u&&(b=h),{lines:v,height:d,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:c,width:h,isTruncated:f}}var V1=function(){function r(){}return r}(),cc=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),W1=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function U1(r,t){var e=new W1;if(r!=null&&(r+=""),!r)return e;for(var n=t.width,i=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&n!=null?{width:n,accumWidth:0,breakAll:a==="breakAll"}:null,s=eu.lastIndex=0,u;(u=eu.exec(r))!=null;){var l=u.index;l>s&&ru(e,r.substring(s,l),t,o),ru(e,u[2],t,o,u[1]),s=eu.lastIndex}s<r.length&&ru(e,r.substring(s,r.length),t,o);var f=[],h=0,v=0,c=t.padding,d=a==="truncate",g=t.lineOverflow==="truncate",p={};function y(Q,ht,st){Q.width=ht,Q.lineHeight=st,h+=st,v=Math.max(v,ht)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var T=_.tokens[w],M=T.styleName&&t.rich[T.styleName]||{},A=T.textPadding=M.padding,L=A?A[1]+A[3]:0,I=T.font=M.font||t.font;T.contentHeight=zf(I);var E=J(M.height,T.contentHeight);if(T.innerHeight=E,A&&(E+=A[0]+A[2]),T.height=E,T.lineHeight=Hi(M.lineHeight,t.lineHeight,E),T.align=M&&M.align||t.align,T.verticalAlign=M&&M.verticalAlign||"middle",g&&i!=null&&h+T.lineHeight>i){var O=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),y(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<O;break t}var R=M.width,k=R==null||R==="auto";if(typeof R=="string"&&R.charAt(R.length-1)==="%")T.percentWidth=R,f.push(T),T.contentWidth=ne(T.text,I);else{if(k){var F=M.backgroundColor,K=F&&F.image;K&&(K=B1(K),hs(K)&&(T.width=Math.max(T.width,K.width*E/K.height)))}var U=d&&n!=null?n-b:null;U!=null&&U<T.width?!k||U<L?(T.text="",T.width=T.contentWidth=0):(tg(p,T.text,U-L,I,t.ellipsis,{minChar:t.truncateMinChar}),T.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,T.width=T.contentWidth=ne(T.text,I)):T.contentWidth=ne(T.text,I)}T.width+=L,b+=T.width,M&&(S=Math.max(S,T.lineHeight))}y(_,b,S)}e.outerWidth=e.width=J(n,v),e.outerHeight=e.height=J(i,h),e.contentHeight=h,e.contentWidth=v,c&&(e.outerWidth+=c[1]+c[3],e.outerHeight+=c[0]+c[2]);for(var m=0;m<f.length;m++){var T=f[m],q=T.percentWidth;T.width=parseInt(q,10)/100*e.width}return e}function ru(r,t,e,n,i){var a=t==="",o=i&&e.rich[i]||{},s=r.lines,u=o.font||e.font,l=!1,f,h;if(n){var v=o.padding,c=v?v[1]+v[3]:0;if(o.width!=null&&o.width!=="auto"){var d=pn(o.width,n.width)+c;s.length>0&&d+n.accumWidth>n.width&&(f=t.split(`
`),l=!0),n.accumWidth=d}else{var g=ng(t,u,n.width,n.breakAll,n.accumWidth);n.accumWidth=g.accumWidth+c,h=g.linesWidths,f=g.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var y=f[p],m=new V1;if(m.styleName=i,m.text=y,m.isLineHolder=!y&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[p]:ne(y,u),!p&&!l){var _=(s[s.length-1]||(s[0]=new cc)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(y||!S||a)&&_.push(m)}else s.push(new cc([m]))}}function Y1(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var q1=Ue(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function X1(r){return Y1(r)?!!q1[r]:!0}function ng(r,t,e,n,i){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<r.length;h++){var v=r.charAt(h);if(v===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var c=ne(v,t),d=n?!1:!X1(v);if(a.length?f+c>e:i+f+c>e){f?(s||u)&&(d?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=v,l+=c,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=v,f=c)):d?(a.push(u),o.push(l),u=v,l=c):(a.push(v),o.push(c));continue}f+=c,d?(u+=v,l+=c):(u&&(s+=u,u="",l=0),s+=v)}return!a.length&&!s&&(s=r,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=i),{accumWidth:f,lines:a,linesWidths:o}}function mn(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function $1(r,t){return r[0]=t[0],r[1]=t[1],r}function ig(r){return[r[0],r[1]]}function Z1(r,t,e){return r[0]=t,r[1]=e,r}function bl(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function K1(r,t,e,n){return r[0]=t[0]+e[0]*n,r[1]=t[1]+e[1]*n,r}function ag(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function Hf(r){return Math.sqrt(Gf(r))}var j1=Hf;function Gf(r){return r[0]*r[0]+r[1]*r[1]}var Q1=Gf;function J1(r,t,e){return r[0]=t[0]*e[0],r[1]=t[1]*e[1],r}function tS(r,t,e){return r[0]=t[0]/e[0],r[1]=t[1]/e[1],r}function eS(r,t){return r[0]*t[0]+r[1]*t[1]}function bo(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function og(r,t){var e=Hf(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function Ho(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var sg=Ho;function ug(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var ln=ug;function rS(r,t){return r[0]=-t[0],r[1]=-t[1],r}function nS(r,t,e,n){return r[0]=t[0]+n*(e[0]-t[0]),r[1]=t[1]+n*(e[1]-t[1]),r}function Ut(r,t,e){var n=t[0],i=t[1];return r[0]=e[0]*n+e[2]*i+e[4],r[1]=e[1]*n+e[3]*i+e[5],r}function yr(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function mr(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}const iS=Object.freeze(Object.defineProperty({__proto__:null,add:bl,applyTransform:Ut,clone:ig,copy:$1,create:mn,dist:sg,distSquare:ln,distance:Ho,distanceSquare:ug,div:tS,dot:eS,len:Hf,lenSquare:Gf,length:j1,lengthSquare:Q1,lerp:nS,max:mr,min:yr,mul:J1,negate:rS,normalize:og,scale:bo,scaleAndAdd:K1,set:Z1,sub:ag},Symbol.toStringTag,{value:"Module"}));var vc=ma,dc=5e-5;function Gr(r){return r>dc||r<-dc}var Vr=[],xn=[],nu=xr(),iu=Math.abs,Vf=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return Gr(this.rotation)||Gr(this.x)||Gr(this.y)||Gr(this.scaleX-1)||Gr(this.scaleY-1)||Gr(this.skewX)||Gr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;if(!(e||t)){n&&(vc(n),this.invTransform=null);return}n=n||xr(),e?this.getLocalTransform(n):vc(n),t&&(e?br(n,t,n):Ff(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(Vr);var n=Vr[0]<0?-1:1,i=Vr[1]<0?-1:1,a=((Vr[0]-n)*e+n)/Vr[0]||0,o=((Vr[1]-i)*e+i)/Vr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||xr(),_a(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),a=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||xr(),br(xn,t.invTransform,e),e=xn);var n=this.originX,i=this.originY;(n||i)&&(nu[4]=n,nu[5]=i,br(xn,e,nu),xn[4]-=n,xn[5]-=i,e=xn),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&Ut(n,n,i),n},r.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&Ut(n,n,i),n},r.prototype.getLineScale=function(){var t=this.transform;return t&&iu(t[0]-1)>1e-10&&iu(t[3]-1)>1e-10?Math.sqrt(iu(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){aS(this,t)},r.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,v=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(n||i||s||u){var d=n+s,g=i+u;e[4]=-d*a-v*g*o,e[5]=-g*o-c*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=c*a,e[2]=v*o,l&&cs(e,e,l),e[4]+=n+f,e[5]+=i+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),Ji=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function aS(r,t){for(var e=0;e<Ji.length;e++){var n=Ji[e];r[n]=t[n]}}var Gi={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,n=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=n/4):t=n*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/n)))},elasticOut:function(r){var t,e=.1,n=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=n/4):t=n*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/n)+1)},elasticInOut:function(r){var t,e=.1,n=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=n/4):t=n*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/n)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/n)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-Gi.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?Gi.bounceIn(r*2)*.5:Gi.bounceOut(r*2-1)*.5+.5}},Va=Math.pow,Tr=Math.sqrt,Go=1e-8,lg=1e-4,pc=Tr(3),Wa=1/3,He=mn(),de=mn(),Vn=mn();function _r(r){return r>-Go&&r<Go}function fg(r){return r>Go||r<-Go}function Ht(r,t,e,n,i){var a=1-i;return a*a*(a*r+3*i*t)+i*i*(i*n+3*a*e)}function gc(r,t,e,n,i){var a=1-i;return 3*(((t-r)*a+2*(e-t)*i)*a+(n-e)*i*i)}function hg(r,t,e,n,i,a){var o=n+3*(t-e)-r,s=3*(e-t*2+r),u=3*(t-r),l=r-i,f=s*s-3*o*u,h=s*u-9*o*l,v=u*u-3*s*l,c=0;if(_r(f)&&_r(h))if(_r(s))a[0]=0;else{var d=-u/s;d>=0&&d<=1&&(a[c++]=d)}else{var g=h*h-4*f*v;if(_r(g)){var p=h/f,d=-s/o+p,y=-p/2;d>=0&&d<=1&&(a[c++]=d),y>=0&&y<=1&&(a[c++]=y)}else if(g>0){var m=Tr(g),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-Va(-_,Wa):_=Va(_,Wa),S<0?S=-Va(-S,Wa):S=Va(S,Wa);var d=(-s-(_+S))/(3*o);d>=0&&d<=1&&(a[c++]=d)}else{var b=(2*f*s-3*o*h)/(2*Tr(f*f*f)),w=Math.acos(b)/3,T=Tr(f),M=Math.cos(w),d=(-s-2*T*M)/(3*o),y=(-s+T*(M+pc*Math.sin(w)))/(3*o),A=(-s+T*(M-pc*Math.sin(w)))/(3*o);d>=0&&d<=1&&(a[c++]=d),y>=0&&y<=1&&(a[c++]=y),A>=0&&A<=1&&(a[c++]=A)}}return c}function cg(r,t,e,n,i){var a=6*e-12*t+6*r,o=9*t+3*n-3*r-9*e,s=3*t-3*r,u=0;if(_r(o)){if(fg(a)){var l=-s/a;l>=0&&l<=1&&(i[u++]=l)}}else{var f=a*a-4*o*s;if(_r(f))i[0]=-a/(2*o);else if(f>0){var h=Tr(f),l=(-a+h)/(2*o),v=(-a-h)/(2*o);l>=0&&l<=1&&(i[u++]=l),v>=0&&v<=1&&(i[u++]=v)}}return u}function Vo(r,t,e,n,i,a){var o=(t-r)*i+r,s=(e-t)*i+t,u=(n-e)*i+e,l=(s-o)*i+o,f=(u-s)*i+s,h=(f-l)*i+l;a[0]=r,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=n}function oS(r,t,e,n,i,a,o,s,u,l,f){var h,v=.005,c=1/0,d,g,p,y;He[0]=u,He[1]=l;for(var m=0;m<1;m+=.05)de[0]=Ht(r,e,i,o,m),de[1]=Ht(t,n,a,s,m),p=ln(He,de),p<c&&(h=m,c=p);c=1/0;for(var _=0;_<32&&!(v<lg);_++)d=h-v,g=h+v,de[0]=Ht(r,e,i,o,d),de[1]=Ht(t,n,a,s,d),p=ln(de,He),d>=0&&p<c?(h=d,c=p):(Vn[0]=Ht(r,e,i,o,g),Vn[1]=Ht(t,n,a,s,g),y=ln(Vn,He),g<=1&&y<c?(h=g,c=y):v*=.5);return Tr(c)}function sS(r,t,e,n,i,a,o,s,u){for(var l=r,f=t,h=0,v=1/u,c=1;c<=u;c++){var d=c*v,g=Ht(r,e,i,o,d),p=Ht(t,n,a,s,d),y=g-l,m=p-f;h+=Math.sqrt(y*y+m*m),l=g,f=p}return h}function Kt(r,t,e,n){var i=1-n;return i*(i*r+2*n*t)+n*n*e}function yc(r,t,e,n){return 2*((1-n)*(t-r)+n*(e-t))}function uS(r,t,e,n,i){var a=r-2*t+e,o=2*(t-r),s=r-n,u=0;if(_r(a)){if(fg(o)){var l=-s/o;l>=0&&l<=1&&(i[u++]=l)}}else{var f=o*o-4*a*s;if(_r(f)){var l=-o/(2*a);l>=0&&l<=1&&(i[u++]=l)}else if(f>0){var h=Tr(f),l=(-o+h)/(2*a),v=(-o-h)/(2*a);l>=0&&l<=1&&(i[u++]=l),v>=0&&v<=1&&(i[u++]=v)}}return u}function vg(r,t,e){var n=r+e-2*t;return n===0?.5:(r-t)/n}function Wo(r,t,e,n,i){var a=(t-r)*n+r,o=(e-t)*n+t,s=(o-a)*n+a;i[0]=r,i[1]=a,i[2]=s,i[3]=s,i[4]=o,i[5]=e}function lS(r,t,e,n,i,a,o,s,u){var l,f=.005,h=1/0;He[0]=o,He[1]=s;for(var v=0;v<1;v+=.05){de[0]=Kt(r,e,i,v),de[1]=Kt(t,n,a,v);var c=ln(He,de);c<h&&(l=v,h=c)}h=1/0;for(var d=0;d<32&&!(f<lg);d++){var g=l-f,p=l+f;de[0]=Kt(r,e,i,g),de[1]=Kt(t,n,a,g);var c=ln(de,He);if(g>=0&&c<h)l=g,h=c;else{Vn[0]=Kt(r,e,i,p),Vn[1]=Kt(t,n,a,p);var y=ln(Vn,He);p<=1&&y<h?(l=p,h=y):f*=.5}}return Tr(h)}function fS(r,t,e,n,i,a,o){for(var s=r,u=t,l=0,f=1/o,h=1;h<=o;h++){var v=h*f,c=Kt(r,e,i,v),d=Kt(t,n,a,v),g=c-s,p=d-u;l+=Math.sqrt(g*g+p*p),s=c,u=d}return l}var hS=/cubic-bezier\(([0-9,\.e ]+)\)/;function dg(r){var t=r&&hS.exec(r);if(t){var e=t[1].split(","),n=+Ae(e[0]),i=+Ae(e[1]),a=+Ae(e[2]),o=+Ae(e[3]);if(isNaN(n+i+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:hg(0,n,a,1,u,s)&&Ht(0,i,o,1,s[0])}}}var cS=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Wt,this.ondestroy=t.ondestroy||Wt,this.onrestart=t.onrestart||Wt,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var n=this._life,i=t-this._startTime-this._pausedTime,a=i/n;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=i%n;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=j(t)?t:Gi[t]||dg(t)},r}(),mc={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Ie(r){return r=Math.round(r),r<0?0:r>255?255:r}function vS(r){return r=Math.round(r),r<0?0:r>360?360:r}function ta(r){return r<0?0:r>1?1:r}function au(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Ie(parseFloat(t)/100*255):Ie(parseInt(t,10))}function fn(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?ta(parseFloat(t)/100):ta(parseFloat(t))}function ou(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Sr(r,t,e){return r+(t-r)*e}function fe(r,t,e,n,i){return r[0]=t,r[1]=e,r[2]=n,r[3]=i,r}function Tl(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var pg=new ya(20),Ua=null;function bn(r,t){Ua&&Tl(Ua,t),Ua=pg.put(r,Ua||t.slice())}function ie(r,t){if(r){t=t||[];var e=pg.get(r);if(e)return Tl(t,e);r=r+"";var n=r.replace(/ /g,"").toLowerCase();if(n in mc)return Tl(t,mc[n]),bn(r,t),t;var i=n.length;if(n.charAt(0)==="#"){if(i===4||i===5){var a=parseInt(n.slice(1,4),16);if(!(a>=0&&a<=4095)){fe(t,0,0,0,1);return}return fe(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,i===5?parseInt(n.slice(4),16)/15:1),bn(r,t),t}else if(i===7||i===9){var a=parseInt(n.slice(1,7),16);if(!(a>=0&&a<=16777215)){fe(t,0,0,0,1);return}return fe(t,(a&16711680)>>16,(a&65280)>>8,a&255,i===9?parseInt(n.slice(7),16)/255:1),bn(r,t),t}return}var o=n.indexOf("("),s=n.indexOf(")");if(o!==-1&&s+1===i){var u=n.substr(0,o),l=n.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?fe(t,+l[0],+l[1],+l[2],1):fe(t,0,0,0,1);f=fn(l.pop());case"rgb":if(l.length>=3)return fe(t,au(l[0]),au(l[1]),au(l[2]),l.length===3?f:fn(l[3])),bn(r,t),t;fe(t,0,0,0,1);return;case"hsla":if(l.length!==4){fe(t,0,0,0,1);return}return l[3]=fn(l[3]),Cl(l,t),bn(r,t),t;case"hsl":if(l.length!==3){fe(t,0,0,0,1);return}return Cl(l,t),bn(r,t),t;default:return}}fe(t,0,0,0,1)}}function Cl(r,t){var e=(parseFloat(r[0])%360+360)%360/360,n=fn(r[1]),i=fn(r[2]),a=i<=.5?i*(n+1):i+n-i*n,o=i*2-a;return t=t||[],fe(t,Ie(ou(o,a,e+1/3)*255),Ie(ou(o,a,e)*255),Ie(ou(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function dS(r){if(r){var t=r[0]/255,e=r[1]/255,n=r[2]/255,i=Math.min(t,e,n),a=Math.max(t,e,n),o=a-i,s=(a+i)/2,u,l;if(o===0)u=0,l=0;else{s<.5?l=o/(a+i):l=o/(2-a-i);var f=((a-t)/6+o/2)/o,h=((a-e)/6+o/2)/o,v=((a-n)/6+o/2)/o;t===a?u=v-h:e===a?u=1/3+f-v:n===a&&(u=2/3+h-f),u<0&&(u+=1),u>1&&(u-=1)}var c=[u*360,l,s];return r[3]!=null&&c.push(r[3]),c}}function Ml(r,t){var e=ie(r);if(e){for(var n=0;n<3;n++)t<0?e[n]=e[n]*(1-t)|0:e[n]=(255-e[n])*t+e[n]|0,e[n]>255?e[n]=255:e[n]<0&&(e[n]=0);return Ir(e,e.length===4?"rgba":"rgb")}}function pS(r){var t=ie(r);if(t)return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}function gg(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){e=e||[];var n=r*(t.length-1),i=Math.floor(n),a=Math.ceil(n),o=t[i],s=t[a],u=n-i;return e[0]=Ie(Sr(o[0],s[0],u)),e[1]=Ie(Sr(o[1],s[1],u)),e[2]=Ie(Sr(o[2],s[2],u)),e[3]=ta(Sr(o[3],s[3],u)),e}}var gS=gg;function yg(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var n=r*(t.length-1),i=Math.floor(n),a=Math.ceil(n),o=ie(t[i]),s=ie(t[a]),u=n-i,l=Ir([Ie(Sr(o[0],s[0],u)),Ie(Sr(o[1],s[1],u)),Ie(Sr(o[2],s[2],u)),ta(Sr(o[3],s[3],u))],"rgba");return e?{color:l,leftIndex:i,rightIndex:a,value:n}:l}}var yS=yg;function mS(r,t,e,n){var i=ie(r);if(r)return i=dS(i),t!=null&&(i[0]=vS(t)),e!=null&&(i[1]=fn(e)),n!=null&&(i[2]=fn(n)),Ir(Cl(i),"rgba")}function _S(r,t){var e=ie(r);if(e&&t!=null)return e[3]=ta(t),Ir(e,"rgba")}function Ir(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function ea(r,t){var e=ie(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}function SS(){return Ir([Math.round(Math.random()*255),Math.round(Math.random()*255),Math.round(Math.random()*255)],"rgb")}var _c=new ya(100);function Dl(r){if(G(r)){var t=_c.get(r);return t||(t=Ml(r,-.1),_c.put(r,t)),t}else if(pa(r)){var e=B({},r);return e.colorStops=W(r.colorStops,function(n){return{offset:n.offset,color:Ml(n.color,-.1)}}),e}return r}const wS=Object.freeze(Object.defineProperty({__proto__:null,fastLerp:gg,fastMapToColor:gS,lerp:yg,lift:Ml,liftColor:Dl,lum:ea,mapToColor:yS,modifyAlpha:_S,modifyHSL:mS,parse:ie,random:SS,stringify:Ir,toHex:pS},Symbol.toStringTag,{value:"Module"}));function xS(r){return r.type==="linear"}function bS(r){return r.type==="radial"}(function(){return Y.hasGlobalWindow&&j(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var Al=Array.prototype.slice;function rr(r,t,e){return(t-r)*e+r}function su(r,t,e,n){for(var i=t.length,a=0;a<i;a++)r[a]=rr(t[a],e[a],n);return r}function TS(r,t,e,n){for(var i=t.length,a=i&&t[0].length,o=0;o<i;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=rr(t[o][s],e[o][s],n)}return r}function Ya(r,t,e,n){for(var i=t.length,a=0;a<i;a++)r[a]=t[a]+e[a]*n;return r}function Sc(r,t,e,n){for(var i=t.length,a=i&&t[0].length,o=0;o<i;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*n}return r}function CS(r,t){for(var e=r.length,n=t.length,i=e>n?t:r,a=Math.min(e,n),o=i[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,n);s++)i.push({offset:o.offset,color:o.color.slice()})}function MS(r,t,e){var n=r,i=t;if(!(!n.push||!i.push)){var a=n.length,o=i.length;if(a!==o){var s=a>o;if(s)n.length=o;else for(var u=a;u<o;u++)n.push(e===1?i[u]:Al.call(i[u]))}for(var l=n[0]&&n[0].length,u=0;u<n.length;u++)if(e===1)isNaN(n[u])&&(n[u]=i[u]);else for(var f=0;f<l;f++)isNaN(n[u][f])&&(n[u][f]=i[u][f])}}function To(r){if(Yt(r)){var t=r.length;if(Yt(r[0])){for(var e=[],n=0;n<t;n++)e.push(Al.call(r[n]));return e}return Al.call(r)}return r}function Co(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function DS(r){return Yt(r&&r[0])?2:1}var qa=0,Mo=1,mg=2,Ei=3,Ll=4,Il=5,wc=6;function xc(r){return r===Ll||r===Il}function Xa(r){return r===Mo||r===mg}var si=[0,0,0,0],AS=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,a=i.length,o=!1,s=wc,u=e;if(Yt(e)){var l=DS(e);s=l,(l===1&&!wt(e[0])||l===2&&!wt(e[0][0]))&&(o=!0)}else if(wt(e)&&!ji(e))s=qa;else if(G(e))if(!isNaN(+e))s=qa;else{var f=ie(e);f&&(u=f,s=Ei)}else if(pa(e)){var h=B({},u);h.colorStops=W(e.colorStops,function(c){return{offset:c.offset,color:ie(c.color)}}),xS(e)?s=Ll:bS(e)&&(s=Il),u=h}a===0?this.valType=s:(s!==this.valType||s===wc)&&(o=!0),this.discrete=this.discrete||o;var v={time:t,value:u,rawValue:e,percent:0};return n&&(v.easing=n,v.easingFunc=j(n)?n:Gi[n]||dg(n)),i.push(v),v},r.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort(function(g,p){return g.time-p.time});for(var i=this.valType,a=n.length,o=n[a-1],s=this.discrete,u=Xa(i),l=xc(i),f=0;f<a;f++){var h=n[f],v=h.value,c=o.value;h.percent=h.time/t,s||(u&&f!==a-1?MS(v,c,i):l&&CS(v.colorStops,c.colorStops))}if(!s&&i!==Il&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var d=n[0].value,f=0;f<a;f++)i===qa?n[f].additiveValue=n[f].value-d:i===Ei?n[f].additiveValue=Ya([],n[f].value,d,-1):Xa(i)&&(n[f].additiveValue=i===Mo?Ya([],n[f].value,d,-1):Sc([],n[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n=this._additiveTrack!=null,i=n?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===Ei,f,h=this._lastFr,v=Math.min,c,d;if(s===1)c=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var g=v(h+1,s-1);for(f=g;f>=0&&!(o[f].percent<=e);f--);f=v(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=v(f-1,s-2)}d=o[f+1],c=o[f]}if(c&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-c.percent,y=p===0?1:v((e-c.percent)/p,1);d.easingFunc&&(y=d.easingFunc(y));var m=n?this._additiveValue:l?si:t[u];if((Xa(a)||l)&&!m&&(m=this._additiveValue=[]),this.discrete)t[u]=y<1?c.rawValue:d.rawValue;else if(Xa(a))a===Mo?su(m,c[i],d[i],y):TS(m,c[i],d[i],y);else if(xc(a)){var _=c[i],S=d[i],b=a===Ll;t[u]={type:b?"linear":"radial",x:rr(_.x,S.x,y),y:rr(_.y,S.y,y),colorStops:W(_.colorStops,function(T,M){var A=S.colorStops[M];return{offset:rr(T.offset,A.offset,y),color:Co(su([],T.color,A.color,y))}}),global:S.global},b?(t[u].x2=rr(_.x2,S.x2,y),t[u].y2=rr(_.y2,S.y2,y)):t[u].r=rr(_.r,S.r,y)}else if(l)su(m,c[i],d[i],y),n||(t[u]=Co(m));else{var w=rr(c[i],d[i],y);n?this._additiveValue=w:t[u]=w}n&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;e===qa?t[n]=t[n]+i:e===Ei?(ie(t[n],si),Ya(si,si,i,1),t[n]=Co(si)):e===Mo?Ya(t[n],t[n],i,1):e===mg&&Sc(t[n],t[n],i,1)},r}(),Wf=function(){function r(t,e,n,i){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i){us("Can' use additive animation on looped animation.");return}this._additiveAnimators=i,this._allowDiscrete=n}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,yt(e),n)},r.prototype.whenWithKeys=function(t,e,n,i){for(var a=this._tracks,o=0;o<n.length;o++){var s=n[o],u=a[s];if(!u){u=a[s]=new AS(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,v=h[h.length-1];l=v&&v.value,f.valType===Ei&&l&&(l=Co(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,To(l),i),this._trackKeys.push(s)}u.addKeyframe(t,To(e[s]),i)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var a=n[i].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(i,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else n.push(s)}if(n.length||this._force){var v=new cS({life:i,loop:this._loop,delay:this._delay||0,onframe:function(c){e._started=2;var d=e._additiveAnimators;if(d){for(var g=!1,p=0;p<d.length;p++)if(d[p]._clip){g=!0;break}g||(e._additiveAnimators=null)}for(var p=0;p<n.length;p++)n[p].step(e._target,c);var y=e._onframeCbs;if(y)for(var p=0;p<y.length;p++)y[p](e._target,c)},ondestroy:function(){e._doneCallback()}});this._clip=v,this.animation&&this.animation.addClip(v),t&&v.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return W(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,a=0;a<t.length;a++){var o=n[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<i.length;a++)if(!n[i[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var a=e[i],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[n?0:s.length-1];u&&(t[a]=To(u.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||yt(t);for(var n=0;n<e.length;n++){var i=e[n],a=this._tracks[i];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[i]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}(),qe=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===n)return this;var u={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],a=0,o=n[t].length;a<o;a++)n[t][a].h!==e&&i.push(n[t][a]);n[t]=i}n[t]&&n[t].length===0&&delete n[t]}else delete n[t];return this},r.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],a=this._$eventProcessor;if(i)for(var o=e.length,s=i.length,u=0;u<s;u++){var l=i[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],a=this._$eventProcessor;if(i)for(var o=e.length,s=e[o-1],u=i.length,l=0;l<u;l++){var f=i[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}(),_g=1;Y.hasGlobalWindow&&(_g=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Uo=_g,Pl=.4,Rl="#333",El="#ccc",LS="#eee",re=1,Oi=2,Nn=4,uu="__zr_normal__",lu=Ji.concat(["ignore"]),IS=Ue(Ji,function(r,t){return r[t]=!0,r},{ignore:!1}),Tn={},PS=new it(0,0,0,0),vs=function(){function r(t){this.id=Rf(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,a=e.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=i?this:null;var l=!1;if(a.copyTransform(e),n.position!=null){var f=PS;n.layoutRect?f.copy(n.layoutRect):f.copy(this.getBoundingRect()),i||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Tn,n,f):Jp(Tn,n,f),a.x=Tn.x,a.y=Tn.y,o=Tn.align,s=Tn.verticalAlign;var h=n.origin;if(h&&n.rotation!=null){var v=void 0,c=void 0;h==="center"?(v=f.width*.5,c=f.height*.5):(v=pn(h[0],f.width),c=pn(h[1],f.height)),l=!0,a.originX=-a.x+v+(i?0:f.x),a.originY=-a.y+c+(i?0:f.y)}}n.rotation!=null&&(a.rotation=n.rotation);var d=n.offset;d&&(a.x+=d[0],a.y+=d[1],l||(a.originX=-d[0],a.originY=-d[1]));var g=n.inside==null?typeof n.position=="string"&&n.position.indexOf("inside")>=0:n.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,m=void 0,_=void 0;g&&this.canBeInsideText()?(y=n.insideFill,m=n.insideStroke,(y==null||y==="auto")&&(y=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(y),_=!0)):(y=n.outsideFill,m=n.outsideStroke,(y==null||y==="auto")&&(y=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(y),_=!0)),y=y||"#000",(y!==p.fill||m!==p.stroke||_!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(u=!0,p.fill=y,p.stroke=m,p.autoStroke=_,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=re,u&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?El:Rl},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n=typeof e=="string"&&ie(e);n||(n=[255,255,255,1]);for(var i=n[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(a?0:255)*(1-i);return n[3]=1,Ir(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},B(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(V(t))for(var n=t,i=yt(n),a=0;a<i.length;a++){var o=i[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],a=i.__fromStateTransition;if(!(i.getLoop()||a&&a!==uu)){var o=i.targetName,s=o?e[o]:e;i.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,lu)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var a=n[i];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(uu,!1,t)},r.prototype.useState=function(t,e,n,i){var a=t===uu,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(lt(s,t)>=0&&(e||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){us("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||i);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!n&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,v=this._textGuide;return h&&h.useState(t,e,n,f),v&&v.useState(t,e,n,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~re),l}}},r.prototype.useStates=function(t,e,n){if(!t.length)this.clearStates();else{var i=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&i.push(f)}var h=i[o-1],v=!!(h&&h.hoverLayer||n);v&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(i),d=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var g=this._textContent,p=this._textGuide;g&&g.useStates(t,e,v),p&&p.useStates(t,e,v),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!v&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~re)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=lt(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),a=lt(i,t),o=lt(i,e)>=0;a>=0?o?i.splice(a,1):i[a]=e:n&&!o&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},n,i=0;i<t.length;i++){var a=t[i];B(e,a),a.textConfig&&(n=n||{},B(n,a.textConfig))}return n&&(e.textConfig=n),e},r.prototype._applyStateObj=function(t,e,n,i,a,o){var s=!(e&&i);e&&e.textConfig?(this.textConfig=B({},i?this.textConfig:n.textConfig),B(this.textConfig,e.textConfig)):s&&n.textConfig&&(this.textConfig=n.textConfig);for(var u={},l=!1,f=0;f<lu.length;f++){var h=lu[f],v=a&&IS[h];e&&e[h]!=null?v?(l=!0,u[h]=e[h]):this[h]=e[h]:s&&n[h]!=null&&(v?(l=!0,u[h]=n[h]):this[h]=n[h])}if(!a)for(var f=0;f<this.animators.length;f++){var c=this.animators[f],d=c.targetName;c.getLoop()||c.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,u,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Vf,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),B(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=re;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,a=new Wf(i,e,n);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during(function(){i.updateDuringAnimation(e)}).done(function(){var a=i.animators,o=lt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,a=[],o=0;o<i;o++){var s=n[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,n){fu(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){fu(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var a=fu(this,e,n,i),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=re;function e(n,i,a,o){Object.defineProperty(t,n,{get:function(){if(!this[i]){var u=this[i]=[];s(this,u)}return this[i]},set:function(u){this[a]=u[0],this[o]=u[1],this[i]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();_e(vs,qe);_e(vs,Vf);function fu(r,t,e,n,i){e=e||{};var a=[];Sg(r,"",r,t,e,n,a,i);var o=a.length,s=!1,u=e.done,l=e.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&e.during&&a[0].during(function(d,g){e.during(g)});for(var v=0;v<a.length;v++){var c=a[v];f&&c.done(f),h&&c.aborted(h),e.force&&c.duration(e.duration),c.start(e.easing)}return a}function hu(r,t,e){for(var n=0;n<e;n++)r[n]=t[n]}function RS(r){return Yt(r[0])}function ES(r,t,e){if(Yt(t[e]))if(Yt(r[e])||(r[e]=[]),qt(t[e])){var n=t[e].length;r[e].length!==n&&(r[e]=new t[e].constructor(n),hu(r[e],t[e],n))}else{var i=t[e],a=r[e],o=i.length;if(RS(i))for(var s=i[0].length,u=0;u<o;u++)a[u]?hu(a[u],i[u],s):a[u]=Array.prototype.slice.call(i[u]);else hu(a,i,o);a.length=i.length}else r[e]=t[e]}function OS(r,t){return r===t||Yt(r)&&Yt(t)&&kS(r,t)}function kS(r,t){var e=r.length;if(e!==t.length)return!1;for(var n=0;n<e;n++)if(r[n]!==t[n])return!1;return!0}function Sg(r,t,e,n,i,a,o,s){for(var u=yt(n),l=i.duration,f=i.delay,h=i.additive,v=i.setToFinal,c=!V(a),d=r.animators,g=[],p=0;p<u.length;p++){var y=u[p],m=n[y];if(m!=null&&e[y]!=null&&(c||a[y]))if(V(m)&&!Yt(m)&&!pa(m)){if(t){s||(e[y]=m,r.updateDuringAnimation(t));continue}Sg(r,y,e[y],m,i,a&&a[y],o,s)}else g.push(y);else s||(e[y]=m,r.updateDuringAnimation(t),g.push(y))}var _=g.length;if(!h&&_)for(var S=0;S<d.length;S++){var b=d[S];if(b.targetName===t){var w=b.stopTracks(g);if(w){var T=lt(d,b);d.splice(T,1)}}}if(i.force||(g=Tt(g,function(I){return!OS(n[I],e[I])}),_=g.length),_>0||i.force&&!o.length){var M=void 0,A=void 0,L=void 0;if(s){A={},v&&(M={});for(var S=0;S<_;S++){var y=g[S];A[y]=e[y],v?M[y]=n[y]:e[y]=n[y]}}else if(v){L={};for(var S=0;S<_;S++){var y=g[S];L[y]=To(e[y]),ES(e,n,y)}}var b=new Wf(e,!1,!1,h?Tt(d,function(E){return E.targetName===t}):null);b.targetName=t,i.scope&&(b.scope=i.scope),v&&M&&b.whenWithKeys(0,M,g),L&&b.whenWithKeys(0,L,g),b.whenWithKeys(l??500,s?A:n,g).delay(f||0),r.addAnimator(b,t),o.push(b)}}var Ol="__zr_style_"+Math.round(Math.random()*10),hn={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ds={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};hn[Ol]=!0;var bc=["z","z2","invisible"],BS=["invisible"],Sa=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var n=yt(e),i=0;i<n.length;i++){var a=n[i];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,n,i,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&FS(this,e,n)||o&&!o[0]&&!o[3])return!1;if(i&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(e,n){return this.rectContain(e,n)},t.prototype.traverse=function(e,n){e.call(n,this)},t.prototype.rectContain=function(e,n){var i=this.transformCoordToLocal(e,n),a=this.getBoundingRect();return a.contain(i[0],i[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var n=this.transform,i=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new it(0,0,0,0)),n?it.applyTransform(e,i,n):e.copy(i),(o||s||u)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(u),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+u-o));var l=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-l),e.y=Math.floor(e.y-l),e.width=Math.ceil(e.width+1+l*2),e.height=Math.ceil(e.height+1+l*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new it(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,n){e!=="style"?r.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},t.prototype.setStyle=function(e,n){return typeof e=="string"?this.style[e]=n:B(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=Oi,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&Oi)},t.prototype.styleUpdated=function(){this.__dirty&=~Oi},t.prototype.createStyle=function(e){return ga(hn,e)},t.prototype.useStyle=function(e){e[Ol]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[Ol]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,bc)},t.prototype._applyStateObj=function(e,n,i,a,o,s){r.prototype._applyStateObj.call(this,e,n,i,a,o,s);var u=!(n&&a),l;if(n&&n.style?o?a?l=n.style:(l=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(l,n.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:i.style),this._mergeStyle(l,n.style)):u&&(l=i.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=yt(f),v=0;v<h.length;v++){var c=h[v];c in l&&(l[c]=l[c],this.style[c]=f[c])}for(var d=yt(l),v=0;v<d.length;v++){var c=d[v];this.style[c]=this.style[c]}this._transitionState(e,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var g=this.__inHover?BS:bc,v=0;v<g.length;v++){var c=g[v];n&&n[c]!=null?this[c]=n[c]:u&&i[c]!=null&&(this[c]=i[c])}},t.prototype._mergeStates=function(e){for(var n=r.prototype._mergeStates.call(this,e),i,a=0;a<e.length;a++){var o=e[a];o.style&&(i=i||{},this._mergeStyle(i,o.style))}return i&&(n.style=i),n},t.prototype._mergeStyle=function(e,n){return B(e,n),e},t.prototype.getAnimationStyleProps=function(){return ds},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=re|Oi}(),t}(vs),cu=new it(0,0,0,0),vu=new it(0,0,0,0);function FS(r,t,e){return cu.copy(r.getBoundingRect()),r.transform&&cu.applyTransform(r.transform),vu.width=t,vu.height=e,!cu.intersect(vu)}var pe=Math.min,ge=Math.max,du=Math.sin,pu=Math.cos,Wr=Math.PI*2,$a=mn(),Za=mn(),Ka=mn();function Tc(r,t,e,n,i,a){i[0]=pe(r,e),i[1]=pe(t,n),a[0]=ge(r,e),a[1]=ge(t,n)}var Cc=[],Mc=[];function NS(r,t,e,n,i,a,o,s,u,l){var f=cg,h=Ht,v=f(r,e,i,o,Cc);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var c=0;c<v;c++){var d=h(r,e,i,o,Cc[c]);u[0]=pe(d,u[0]),l[0]=ge(d,l[0])}v=f(t,n,a,s,Mc);for(var c=0;c<v;c++){var g=h(t,n,a,s,Mc[c]);u[1]=pe(g,u[1]),l[1]=ge(g,l[1])}u[0]=pe(r,u[0]),l[0]=ge(r,l[0]),u[0]=pe(o,u[0]),l[0]=ge(o,l[0]),u[1]=pe(t,u[1]),l[1]=ge(t,l[1]),u[1]=pe(s,u[1]),l[1]=ge(s,l[1])}function zS(r,t,e,n,i,a,o,s){var u=vg,l=Kt,f=ge(pe(u(r,e,i),1),0),h=ge(pe(u(t,n,a),1),0),v=l(r,e,i,f),c=l(t,n,a,h);o[0]=pe(r,i,v),o[1]=pe(t,a,c),s[0]=ge(r,i,v),s[1]=ge(t,a,c)}function HS(r,t,e,n,i,a,o,s,u){var l=yr,f=mr,h=Math.abs(i-a);if(h%Wr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-n,u[0]=r+e,u[1]=t+n;return}if($a[0]=pu(i)*e+r,$a[1]=du(i)*n+t,Za[0]=pu(a)*e+r,Za[1]=du(a)*n+t,l(s,$a,Za),f(u,$a,Za),i=i%Wr,i<0&&(i=i+Wr),a=a%Wr,a<0&&(a=a+Wr),i>a&&!o?a+=Wr:i<a&&o&&(i+=Wr),o){var v=a;a=i,i=v}for(var c=0;c<a;c+=Math.PI/2)c>i&&(Ka[0]=pu(c)*e+r,Ka[1]=du(c)*n+t,l(s,Ka,s),f(u,Ka,u))}var ut={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Ur=[],Yr=[],Be=[],fr=[],Fe=[],Ne=[],gu=Math.min,yu=Math.max,qr=Math.cos,Xr=Math.sin,je=Math.abs,kl=Math.PI,pr=kl*2,mu=typeof Float32Array<"u",ui=[];function _u(r){var t=Math.round(r/kl*1e8)/1e8;return t%2*kl}function GS(r,t){var e=_u(r[0]);e<0&&(e+=pr);var n=e-r[0],i=r[1];i+=n,!t&&i-e>=pr?i=e+pr:t&&e-i>=pr?i=e-pr:!t&&e>i?i=e+(pr-_u(e-i)):t&&e<i&&(i=e-(pr-_u(i-e))),r[0]=e,r[1]=i}var Zn=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,n){n=n||0,n>0&&(this._ux=je(n/Uo/t)||0,this._uy=je(n/Uo/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(ut.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var n=je(t-this._xi),i=je(e-this._yi),a=n>this._ux||i>this._uy;if(this.addData(ut.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,n,i,a,o){return this._drawPendingPt(),this.addData(ut.C,t,e,n,i,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(ut.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},r.prototype.arc=function(t,e,n,i,a,o){this._drawPendingPt(),ui[0]=i,ui[1]=a,GS(ui,o),i=ui[0],a=ui[1];var s=a-i;return this.addData(ut.A,t,e,n,n,i,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,a,o),this._xi=qr(a)*n+t,this._yi=Xr(a)*n+e,this},r.prototype.arcTo=function(t,e,n,i,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,a),this},r.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(ut.R,t,e,n,i),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(ut.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&mu&&(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,a=0;a<e;a++)n+=t[a].len();mu&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[i++]=o[s];this._len=i},r.prototype.addData=function(t,e,n,i,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,mu&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){Be[0]=Be[1]=Fe[0]=Fe[1]=Number.MAX_VALUE,fr[0]=fr[1]=Ne[0]=Ne[1]=-Number.MAX_VALUE;var t=this.data,e=0,n=0,i=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(e=t[o],n=t[o+1],i=e,a=n),s){case ut.M:e=i=t[o++],n=a=t[o++],Fe[0]=i,Fe[1]=a,Ne[0]=i,Ne[1]=a;break;case ut.L:Tc(e,n,t[o],t[o+1],Fe,Ne),e=t[o++],n=t[o++];break;case ut.C:NS(e,n,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Fe,Ne),e=t[o++],n=t[o++];break;case ut.Q:zS(e,n,t[o++],t[o++],t[o],t[o+1],Fe,Ne),e=t[o++],n=t[o++];break;case ut.A:var l=t[o++],f=t[o++],h=t[o++],v=t[o++],c=t[o++],d=t[o++]+c;o+=1;var g=!t[o++];u&&(i=qr(c)*h+l,a=Xr(c)*v+f),HS(l,f,h,v,c,d,g,Fe,Ne),e=qr(d)*h+l,n=Xr(d)*v+f;break;case ut.R:i=e=t[o++],a=n=t[o++];var p=t[o++],y=t[o++];Tc(i,a,i+p,a+y,Fe,Ne);break;case ut.Z:e=i,n=a;break}yr(Be,Be,Fe),mr(fr,fr,Ne)}return o===0&&(Be[0]=Be[1]=fr[0]=fr[1]=0),new it(Be[0],Be[1],fr[0]-Be[0],fr[1]-Be[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,v=0;v<e;){var c=t[v++],d=v===1;d&&(a=t[v],o=t[v+1],s=a,u=o);var g=-1;switch(c){case ut.M:a=s=t[v++],o=u=t[v++];break;case ut.L:{var p=t[v++],y=t[v++],m=p-a,_=y-o;(je(m)>n||je(_)>i||v===e-1)&&(g=Math.sqrt(m*m+_*_),a=p,o=y);break}case ut.C:{var S=t[v++],b=t[v++],p=t[v++],y=t[v++],w=t[v++],T=t[v++];g=sS(a,o,S,b,p,y,w,T,10),a=w,o=T;break}case ut.Q:{var S=t[v++],b=t[v++],p=t[v++],y=t[v++];g=fS(a,o,S,b,p,y,10),a=p,o=y;break}case ut.A:var M=t[v++],A=t[v++],L=t[v++],I=t[v++],E=t[v++],O=t[v++],R=O+E;v+=1,d&&(s=qr(E)*L+M,u=Xr(E)*I+A),g=yu(L,I)*gu(pr,Math.abs(O)),a=qr(R)*L+M,o=Xr(R)*I+A;break;case ut.R:{s=a=t[v++],u=o=t[v++];var k=t[v++],F=t[v++];g=k*2+F*2;break}case ut.Z:{var m=s-a,_=u-o;g=Math.sqrt(m*m+_*_),a=s,o=u;break}}g>=0&&(l[h++]=g,f+=g)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var n=this.data,i=this._ux,a=this._uy,o=this._len,s,u,l,f,h,v,c=e<1,d,g,p=0,y=0,m,_=0,S,b;if(!(c&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,g=this._pathLen,m=e*g,!m)))t:for(var w=0;w<o;){var T=n[w++],M=w===1;switch(M&&(l=n[w],f=n[w+1],s=l,u=f),T!==ut.L&&_>0&&(t.lineTo(S,b),_=0),T){case ut.M:s=l=n[w++],u=f=n[w++],t.moveTo(l,f);break;case ut.L:{h=n[w++],v=n[w++];var A=je(h-l),L=je(v-f);if(A>i||L>a){if(c){var I=d[y++];if(p+I>m){var E=(m-p)/I;t.lineTo(l*(1-E)+h*E,f*(1-E)+v*E);break t}p+=I}t.lineTo(h,v),l=h,f=v,_=0}else{var O=A*A+L*L;O>_&&(S=h,b=v,_=O)}break}case ut.C:{var R=n[w++],k=n[w++],F=n[w++],K=n[w++],U=n[w++],q=n[w++];if(c){var I=d[y++];if(p+I>m){var E=(m-p)/I;Vo(l,R,F,U,E,Ur),Vo(f,k,K,q,E,Yr),t.bezierCurveTo(Ur[1],Yr[1],Ur[2],Yr[2],Ur[3],Yr[3]);break t}p+=I}t.bezierCurveTo(R,k,F,K,U,q),l=U,f=q;break}case ut.Q:{var R=n[w++],k=n[w++],F=n[w++],K=n[w++];if(c){var I=d[y++];if(p+I>m){var E=(m-p)/I;Wo(l,R,F,E,Ur),Wo(f,k,K,E,Yr),t.quadraticCurveTo(Ur[1],Yr[1],Ur[2],Yr[2]);break t}p+=I}t.quadraticCurveTo(R,k,F,K),l=F,f=K;break}case ut.A:var Q=n[w++],ht=n[w++],st=n[w++],$=n[w++],Ft=n[w++],Oe=n[w++],te=n[w++],ke=!n[w++],se=st>$?st:$,Pt=je(st-$)>.001,xt=Ft+Oe,X=!1;if(c){var I=d[y++];p+I>m&&(xt=Ft+Oe*(m-p)/I,X=!0),p+=I}if(Pt&&t.ellipse?t.ellipse(Q,ht,st,$,te,Ft,xt,ke):t.arc(Q,ht,se,Ft,xt,ke),X)break t;M&&(s=qr(Ft)*st+Q,u=Xr(Ft)*$+ht),l=qr(xt)*st+Q,f=Xr(xt)*$+ht;break;case ut.R:s=l=n[w],u=f=n[w+1],h=n[w++],v=n[w++];var tt=n[w++],Ze=n[w++];if(c){var I=d[y++];if(p+I>m){var Dt=m-p;t.moveTo(h,v),t.lineTo(h+gu(Dt,tt),v),Dt-=tt,Dt>0&&t.lineTo(h+tt,v+gu(Dt,Ze)),Dt-=Ze,Dt>0&&t.lineTo(h+yu(tt-Dt,0),v+Ze),Dt-=tt,Dt>0&&t.lineTo(h,v+yu(Ze-Dt,0));break t}p+=I}t.rect(h,v,tt,Ze);break;case ut.Z:if(c){var I=d[y++];if(p+I>m){var E=(m-p)/I;t.lineTo(l*(1-E)+s*E,f*(1-E)+u*E);break t}p+=I}t.closePath(),l=s,f=u}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=ut,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function Cn(r,t,e,n,i,a,o){if(i===0)return!1;var s=i,u=0,l=r;if(o>t+s&&o>n+s||o<t-s&&o<n-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)u=(t-n)/(r-e),l=(r*n-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function VS(r,t,e,n,i,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>n+h&&f>a+h&&f>s+h||f<t-h&&f<n-h&&f<a-h&&f<s-h||l>r+h&&l>e+h&&l>i+h&&l>o+h||l<r-h&&l<e-h&&l<i-h&&l<o-h)return!1;var v=oS(r,t,e,n,i,a,o,s,l,f);return v<=h/2}function WS(r,t,e,n,i,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>n+l&&u>a+l||u<t-l&&u<n-l&&u<a-l||s>r+l&&s>e+l&&s>i+l||s<r-l&&s<e-l&&s<i-l)return!1;var f=lS(r,t,e,n,i,a,s,u);return f<=l/2}var Dc=Math.PI*2;function ja(r){return r%=Dc,r<0&&(r+=Dc),r}var li=Math.PI*2;function US(r,t,e,n,i,a,o,s,u){if(o===0)return!1;var l=o;s-=r,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>e||f+l<e)return!1;if(Math.abs(n-i)%li<1e-4)return!0;if(a){var h=n;n=ja(i),i=ja(h)}else n=ja(n),i=ja(i);n>i&&(i+=li);var v=Math.atan2(u,s);return v<0&&(v+=li),v>=n&&v<=i||v+li>=n&&v+li<=i}function nr(r,t,e,n,i,a){if(a>t&&a>n||a<t&&a<n||n===t)return 0;var o=(a-t)/(n-t),s=n<t?1:-1;(o===1||o===0)&&(s=n<t?.5:-.5);var u=o*(e-r)+r;return u===i?1/0:u>i?s:0}var hr=Zn.CMD,$r=Math.PI*2,YS=1e-4;function qS(r,t){return Math.abs(r-t)<YS}var Nt=[-1,-1,-1],ve=[-1,-1];function XS(){var r=ve[0];ve[0]=ve[1],ve[1]=r}function $S(r,t,e,n,i,a,o,s,u,l){if(l>t&&l>n&&l>a&&l>s||l<t&&l<n&&l<a&&l<s)return 0;var f=hg(t,n,a,s,l,Nt);if(f===0)return 0;for(var h=0,v=-1,c=void 0,d=void 0,g=0;g<f;g++){var p=Nt[g],y=p===0||p===1?.5:1,m=Ht(r,e,i,o,p);m<u||(v<0&&(v=cg(t,n,a,s,ve),ve[1]<ve[0]&&v>1&&XS(),c=Ht(t,n,a,s,ve[0]),v>1&&(d=Ht(t,n,a,s,ve[1]))),v===2?p<ve[0]?h+=c<t?y:-y:p<ve[1]?h+=d<c?y:-y:h+=s<d?y:-y:p<ve[0]?h+=c<t?y:-y:h+=s<c?y:-y)}return h}function ZS(r,t,e,n,i,a,o,s){if(s>t&&s>n&&s>a||s<t&&s<n&&s<a)return 0;var u=uS(t,n,a,s,Nt);if(u===0)return 0;var l=vg(t,n,a);if(l>=0&&l<=1){for(var f=0,h=Kt(t,n,a,l),v=0;v<u;v++){var c=Nt[v]===0||Nt[v]===1?.5:1,d=Kt(r,e,i,Nt[v]);d<o||(Nt[v]<l?f+=h<t?c:-c:f+=a<h?c:-c)}return f}else{var c=Nt[0]===0||Nt[0]===1?.5:1,d=Kt(r,e,i,Nt[0]);return d<o?0:a<t?c:-c}}function KS(r,t,e,n,i,a,o,s){if(s-=t,s>e||s<-e)return 0;var u=Math.sqrt(e*e-s*s);Nt[0]=-u,Nt[1]=u;var l=Math.abs(n-i);if(l<1e-4)return 0;if(l>=$r-1e-4){n=0,i=$r;var f=a?1:-1;return o>=Nt[0]+r&&o<=Nt[1]+r?f:0}if(n>i){var h=n;n=i,i=h}n<0&&(n+=$r,i+=$r);for(var v=0,c=0;c<2;c++){var d=Nt[c];if(d+r>o){var g=Math.atan2(s,d),f=a?1:-1;g<0&&(g=$r+g),(g>=n&&g<=i||g+$r>=n&&g+$r<=i)&&(g>Math.PI/2&&g<Math.PI*1.5&&(f=-f),v+=f)}}return v}function wg(r,t,e,n,i){for(var a=r.data,o=r.len(),s=0,u=0,l=0,f=0,h=0,v,c,d=0;d<o;){var g=a[d++],p=d===1;switch(g===hr.M&&d>1&&(e||(s+=nr(u,l,f,h,n,i))),p&&(u=a[d],l=a[d+1],f=u,h=l),g){case hr.M:f=a[d++],h=a[d++],u=f,l=h;break;case hr.L:if(e){if(Cn(u,l,a[d],a[d+1],t,n,i))return!0}else s+=nr(u,l,a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case hr.C:if(e){if(VS(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,n,i))return!0}else s+=$S(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case hr.Q:if(e){if(WS(u,l,a[d++],a[d++],a[d],a[d+1],t,n,i))return!0}else s+=ZS(u,l,a[d++],a[d++],a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case hr.A:var y=a[d++],m=a[d++],_=a[d++],S=a[d++],b=a[d++],w=a[d++];d+=1;var T=!!(1-a[d++]);v=Math.cos(b)*_+y,c=Math.sin(b)*S+m,p?(f=v,h=c):s+=nr(u,l,v,c,n,i);var M=(n-y)*S/_+y;if(e){if(US(y,m,S,b,b+w,T,t,M,i))return!0}else s+=KS(y,m,S,b,b+w,T,M,i);u=Math.cos(b+w)*_+y,l=Math.sin(b+w)*S+m;break;case hr.R:f=u=a[d++],h=l=a[d++];var A=a[d++],L=a[d++];if(v=f+A,c=h+L,e){if(Cn(f,h,v,h,t,n,i)||Cn(v,h,v,c,t,n,i)||Cn(v,c,f,c,t,n,i)||Cn(f,c,f,h,t,n,i))return!0}else s+=nr(v,h,v,c,n,i),s+=nr(f,c,f,h,n,i);break;case hr.Z:if(e){if(Cn(u,l,f,h,t,n,i))return!0}else s+=nr(u,l,f,h,n,i);u=f,l=h;break}}return!e&&!qS(l,h)&&(s+=nr(u,l,f,h,n,i)||0),s!==0}function jS(r,t,e){return wg(r,0,!1,t,e)}function QS(r,t,e,n){return wg(r,t,!0,e,n)}var xg=ft({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},hn),JS={style:ft({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ds.style)},Su=Ji.concat(["invisible","culling","z","z2","zlevel","parent"]),mt=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var n=this.style;if(n.decal){var i=this._decalEl=this._decalEl||new t;i.buildPath===t.prototype.buildPath&&(i.buildPath=function(u){e.buildPath(u,e.shape)}),i.silent=!0;var a=i.style;for(var o in n)a[o]!==n[o]&&(a[o]=n[o]);a.fill=n.fill?n.decal:null,a.decal=null,a.shadowColor=null,n.strokeFirst&&(a.stroke=null);for(var s=0;s<Su.length;++s)i[Su[s]]=this[Su[s]];i.__dirty|=re}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var n=yt(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var a=0;a<n.length;a++){var o=n[a],s=e[o];o==="style"?this.style?B(this.style,s):this.useStyle(s):o==="shape"?B(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(G(e)){var n=ea(e,0);return n>.5?Rl:n>.2?LS:El}else if(e)return El}return Rl},t.prototype.getInsideTextStroke=function(e){var n=this.style.fill;if(G(n)){var i=this.__zr,a=!!(i&&i.isDarkMode()),o=ea(e,0)<Pl;if(a===o)return n}},t.prototype.buildPath=function(e,n,i){},t.prototype.pathUpdated=function(){this.__dirty&=~Nn},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new Zn(!1)},t.prototype.hasStroke=function(){var e=this.style,n=e.stroke;return!(n==null||n==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,n=e.fill;return n!=null&&n!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,n=this.style,i=!e;if(i){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Nn)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||i){s.copy(e);var u=n.strokeNoScale?this.getLineScale():1,l=n.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return e},t.prototype.contain=function(e,n){var i=this.transformCoordToLocal(e,n),a=this.getBoundingRect(),o=this.style;if(e=i[0],n=i[1],a.contain(e,n)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),QS(s,u/l,e,n)))return!0}if(this.hasFill())return jS(s,e,n)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Nn,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,n){e==="shape"?this.setShape(n):r.prototype.attrKV.call(this,e,n)},t.prototype.setShape=function(e,n){var i=this.shape;return i||(i=this.shape={}),typeof e=="string"?i[e]=n:B(i,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Nn)},t.prototype.createStyle=function(e){return ga(xg,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=B({},this.shape))},t.prototype._applyStateObj=function(e,n,i,a,o,s){r.prototype._applyStateObj.call(this,e,n,i,a,o,s);var u=!(n&&a),l;if(n&&n.shape?o?a?l=n.shape:(l=B({},i.shape),B(l,n.shape)):(l=B({},a?this.shape:i.shape),B(l,n.shape)):u&&(l=i.shape),l)if(o){this.shape=B({},this.shape);for(var f={},h=yt(l),v=0;v<h.length;v++){var c=h[v];typeof l[c]=="object"?this.shape[c]=l[c]:f[c]=l[c]}this._transitionState(e,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var n=r.prototype._mergeStates.call(this,e),i,a=0;a<e.length;a++){var o=e[a];o.shape&&(i=i||{},this._mergeStyle(i,o.shape))}return i&&(n.shape=i),n},t.prototype.getAnimationStyleProps=function(){return JS},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var n=function(a){H(o,a);function o(s){var u=a.call(this,s)||this;return e.init&&e.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return et(e.style)},o.prototype.getDefaultShape=function(){return et(e.shape)},o}(t);for(var i in e)typeof e[i]=="function"&&(n.prototype[i]=e[i]);return n},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=re|Oi|Nn}(),t}(Sa),tw=ft({strokeFirst:!0,font:dn,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},xg),Yo=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,n=e.stroke;return n!=null&&n!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,n=e.fill;return n!=null&&n!=="none"},t.prototype.createStyle=function(e){return ga(tw,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var n=e.text;n!=null?n+="":n="";var i=Nf(n,e.font,e.textAlign,e.textBaseline);if(i.x+=e.x||0,i.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;i.x-=a/2,i.y-=a/2,i.width+=a,i.height+=a}this._rect=i}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(Sa);Yo.prototype.type="tspan";var ew=ft({x:0,y:0},hn),rw={style:ft({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ds.style)};function nw(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var Pr=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return ga(ew,e)},t.prototype._getSize=function(e){var n=this.style,i=n[e];if(i!=null)return i;var a=nw(n.image)?n.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=n[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return rw},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new it(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Sa);Pr.prototype.type="image";function iw(r,t){var e=t.x,n=t.y,i=t.width,a=t.height,o=t.r,s,u,l,f;i<0&&(e=e+i,i=-i),a<0&&(n=n+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>i&&(h=s+u,s*=i/h,u*=i/h),l+f>i&&(h=l+f,l*=i/h,f*=i/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,n),r.lineTo(e+i-u,n),u!==0&&r.arc(e+i-u,n+u,u,-Math.PI/2,0),r.lineTo(e+i,n+a-l),l!==0&&r.arc(e+i-l,n+a-l,l,0,Math.PI/2),r.lineTo(e+f,n+a),f!==0&&r.arc(e+f,n+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,n+s),s!==0&&r.arc(e+s,n+s,s,Math.PI,Math.PI*1.5)}var zn=Math.round;function bg(r,t,e){if(t){var n=t.x1,i=t.x2,a=t.y1,o=t.y2;r.x1=n,r.x2=i,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(zn(n*2)===zn(i*2)&&(r.x1=r.x2=an(n,s,!0)),zn(a*2)===zn(o*2)&&(r.y1=r.y2=an(a,s,!0))),r}}function Tg(r,t,e){if(t){var n=t.x,i=t.y,a=t.width,o=t.height;r.x=n,r.y=i,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=an(n,s,!0),r.y=an(i,s,!0),r.width=Math.max(an(n+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(an(i+o,s,!1)-r.y,o===0?0:1)),r}}function an(r,t,e){if(!t)return r;var n=zn(r*2);return(n+zn(t))%2===0?n/2:(n+(e?1:-1))/2}var aw=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),ow={},Lt=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new aw},t.prototype.buildPath=function(e,n){var i,a,o,s;if(this.subPixelOptimize){var u=Tg(ow,n,this.style);i=u.x,a=u.y,o=u.width,s=u.height,u.r=n.r,n=u}else i=n.x,a=n.y,o=n.width,s=n.height;n.r?iw(e,n):e.rect(i,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(mt);Lt.prototype.type="rect";var Ac={fill:"#000"},Lc=2,sw={style:ft({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ds.style)},Xt=function(r){H(t,r);function t(e){var n=r.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Ac,n.attr(e),n}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,cw(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new it(0,0,0,0),n=this._children,i=[],a=null,o=0;o<n.length;o++){var s=n[o],u=s.getBoundingRect(),l=s.getLocalTransform(i);l?(e.copy(u),e.applyTransform(l),a=a||e.clone(),a.union(e)):(a=a||u.clone(),a.union(u))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Ac},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,n){if(!n)return e;var i=n.rich,a=e.rich||i&&{};return B(e,n),i&&a?(this._mergeRich(a,i),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,n){for(var i=yt(n),a=0;a<i.length;a++){var o=i[a];e[o]=e[o]||{},B(e[o],n[o])}},t.prototype.getAnimationStyleProps=function(){return sw},t.prototype._getOrCreateChild=function(e){var n=this._children[this._childCursor];return(!n||!(n instanceof e))&&(n=new e),this._children[this._childCursor++]=n,n.__zr=this.__zr,n.parent=this,n},t.prototype._updatePlainTexts=function(){var e=this.style,n=e.font||dn,i=e.padding,a=Bc(e),o=G1(a,e),s=wu(e),u=!!e.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,v=o.lines,c=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var g=e.x||0,p=e.y||0,y=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",_=g,S=Fn(p,o.contentHeight,m);if(s||i){var b=Ri(g,f,y),w=Fn(p,l,m);s&&this._renderBackground(e,e,b,w,f,l)}S+=c/2,i&&(_=kc(g,y,i),m==="top"?S+=i[0]:m==="bottom"&&(S-=i[2]));for(var T=0,M=!1,A=Oc("fill"in e?e.fill:(M=!0,d.fill)),L=Ec("stroke"in e?e.stroke:!u&&(!d.autoStroke||M)?(T=Lc,d.stroke):null),I=e.textShadowBlur>0,E=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),O=o.calculatedLineHeight,R=0;R<v.length;R++){var k=this._getOrCreateChild(Yo),F=k.createStyle();k.useStyle(F),F.text=v[R],F.x=_,F.y=S,F.textAlign=y,F.textBaseline="middle",F.opacity=e.opacity,F.strokeFirst=!0,I&&(F.shadowBlur=e.textShadowBlur||0,F.shadowColor=e.textShadowColor||"transparent",F.shadowOffsetX=e.textShadowOffsetX||0,F.shadowOffsetY=e.textShadowOffsetY||0),F.stroke=L,F.fill=A,L&&(F.lineWidth=e.lineWidth||T,F.lineDash=e.lineDash,F.lineDashOffset=e.lineDashOffset||0),F.font=n,Pc(F,e),S+=c,E&&k.setBoundingRect(new it(Ri(F.x,h,F.textAlign),Fn(F.y,O,F.textBaseline),h,O))}},t.prototype._updateRichTexts=function(){var e=this.style,n=Bc(e),i=U1(n,e),a=i.width,o=i.outerWidth,s=i.outerHeight,u=e.padding,l=e.x||0,f=e.y||0,h=this._defaultStyle,v=e.align||h.align,c=e.verticalAlign||h.verticalAlign;this.isTruncated=!!i.isTruncated;var d=Ri(l,o,v),g=Fn(f,s,c),p=d,y=g;u&&(p+=u[3],y+=u[0]);var m=p+a;wu(e)&&this._renderBackground(e,e,d,g,o,s);for(var _=!!e.backgroundColor,S=0;S<i.lines.length;S++){for(var b=i.lines[S],w=b.tokens,T=w.length,M=b.lineHeight,A=b.width,L=0,I=p,E=m,O=T-1,R=void 0;L<T&&(R=w[L],!R.align||R.align==="left");)this._placeToken(R,e,M,y,I,"left",_),A-=R.width,I+=R.width,L++;for(;O>=0&&(R=w[O],R.align==="right");)this._placeToken(R,e,M,y,E,"right",_),A-=R.width,E-=R.width,O--;for(I+=(a-(I-p)-(m-E)-A)/2;L<=O;)R=w[L],this._placeToken(R,e,M,y,I+R.width/2,"center",_),I+=R.width,L++;y+=M}},t.prototype._placeToken=function(e,n,i,a,o,s,u){var l=n.rich[e.styleName]||{};l.text=e.text;var f=e.verticalAlign,h=a+i/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+i-e.height/2);var v=!e.isLineHolder&&wu(l);v&&this._renderBackground(l,n,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var c=!!l.backgroundColor,d=e.textPadding;d&&(o=kc(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var g=this._getOrCreateChild(Yo),p=g.createStyle();g.useStyle(p);var y=this._defaultStyle,m=!1,_=0,S=Oc("fill"in l?l.fill:"fill"in n?n.fill:(m=!0,y.fill)),b=Ec("stroke"in l?l.stroke:"stroke"in n?n.stroke:!c&&!u&&(!y.autoStroke||m)?(_=Lc,y.stroke):null),w=l.textShadowBlur>0||n.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,w&&(p.shadowBlur=l.textShadowBlur||n.textShadowBlur||0,p.shadowColor=l.textShadowColor||n.textShadowColor||"transparent",p.shadowOffsetX=l.textShadowOffsetX||n.textShadowOffsetX||0,p.shadowOffsetY=l.textShadowOffsetY||n.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||dn,p.opacity=Hi(l.opacity,n.opacity,1),Pc(p,l),b&&(p.lineWidth=Hi(l.lineWidth,n.lineWidth,_),p.lineDash=J(l.lineDash,n.lineDash),p.lineDashOffset=n.lineDashOffset||0,p.stroke=b),S&&(p.fill=S);var T=e.contentWidth,M=e.contentHeight;g.setBoundingRect(new it(Ri(p.x,T,p.textAlign),Fn(p.y,M,p.textBaseline),T,M))},t.prototype._renderBackground=function(e,n,i,a,o,s){var u=e.backgroundColor,l=e.borderWidth,f=e.borderColor,h=u&&u.image,v=u&&!h,c=e.borderRadius,d=this,g,p;if(v||e.lineHeight||l&&f){g=this._getOrCreateChild(Lt),g.useStyle(g.createStyle()),g.style.fill=null;var y=g.shape;y.x=i,y.y=a,y.width=o,y.height=s,y.r=c,g.dirtyShape()}if(v){var m=g.style;m.fill=u||null,m.fillOpacity=J(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(Pr),p.onload=function(){d.dirtyStyle()};var _=p.style;_.image=u.image,_.x=i,_.y=a,_.width=o,_.height=s}if(l&&f){var m=g.style;m.lineWidth=l,m.stroke=f,m.strokeOpacity=J(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,g.strokeContainThreshold=0,g.hasFill()&&g.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(g||p).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=Hi(e.opacity,n.opacity,1)},t.makeFont=function(e){var n="";return hw(e)&&(n=[e.fontStyle,e.fontWeight,fw(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),n&&Ae(n)||e.textFont||e.font},t}(Sa),uw={left:!0,right:1,center:1},lw={top:1,bottom:1,middle:1},Ic=["fontStyle","fontWeight","fontSize","fontFamily"];function fw(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Lf+"px":r+"px"}function Pc(r,t){for(var e=0;e<Ic.length;e++){var n=Ic[e],i=t[n];i!=null&&(r[n]=i)}}function hw(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function cw(r){return Rc(r),C(r.rich,Rc),r}function Rc(r){if(r){r.font=Xt.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||uw[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||lw[e]?e:"top";var n=r.padding;n&&(r.padding=Of(r.padding))}}function Ec(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Oc(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function kc(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function Bc(r){var t=r.text;return t!=null&&(t+=""),t}function wu(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}var Fc=1e-4,Cg=20;function vw(r){return r.replace(/^\s+|\s+$/g,"")}function Bl(r,t,e,n){var i=t[0],a=t[1],o=e[0],s=e[1],u=a-i,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(n)if(u>0){if(r<=i)return o;if(r>=a)return s}else{if(r>=i)return o;if(r<=a)return s}else{if(r===i)return o;if(r===a)return s}return(r-i)/u*l+o}function Gt(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return G(r)?vw(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function It(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),Cg),r=(+r).toFixed(t),e?r:+r}function dw(r){return r.sort(function(t,e){return t-e}),r}function Ve(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return Mg(r)}function Mg(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),n=e>0?+t.slice(e+1):0,i=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:i-1-a;return Math.max(0,o-n)}function Dg(r,t){var e=Math.log,n=Math.LN10,i=Math.floor(e(r[1]-r[0])/n),a=Math.round(e(Math.abs(t[1]-t[0]))/n),o=Math.min(Math.max(-i+a,0),20);return isFinite(o)?o:20}function pw(r,t,e){if(!r[t])return 0;var n=gw(r,e);return n[t]||0}function gw(r,t){var e=Ue(r,function(c,d){return c+(isNaN(d)?0:d)},0);if(e===0)return[];for(var n=Math.pow(10,t),i=W(r,function(c){return(isNaN(c)?0:c)/e*n*100}),a=n*100,o=W(i,function(c){return Math.floor(c)}),s=Ue(o,function(c,d){return c+d},0),u=W(i,function(c,d){return c-o[d]});s<a;){for(var l=Number.NEGATIVE_INFINITY,f=null,h=0,v=u.length;h<v;++h)u[h]>l&&(l=u[h],f=h);++o[f],u[f]=0,++s}return W(o,function(c){return c/n})}function yw(r,t){var e=Math.max(Ve(r),Ve(t)),n=r+t;return e>Cg?n:It(n,e)}var mw=9007199254740991;function Uf(r){var t=Math.PI*2;return(r%t+t)%t}function ra(r){return r>-Fc&&r<Fc}var _w=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function me(r){if(r instanceof Date)return r;if(G(r)){var t=_w.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function Ag(r){return Math.pow(10,ps(r))}function ps(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function Yf(r,t){var e=ps(r),n=Math.pow(10,e),i=r/n,a;return t?i<1.5?a=1:i<2.5?a=2:i<4?a=3:i<7?a=5:a=10:i<1?a=1:i<2?a=2:i<3?a=3:i<5?a=5:a=10,r=a*n,e>=-20?+r.toFixed(e<0?-e:0):r}function Sw(r,t){var e=(r.length-1)*t+1,n=Math.floor(e),i=+r[n-1],a=e-n;return a?i+a*(r[n]-i):i}function ww(r){r.sort(function(u,l){return s(u,l,0)?-1:1});for(var t=-1/0,e=1,n=0;n<r.length;){for(var i=r[n].interval,a=r[n].close,o=0;o<2;o++)i[o]<=t&&(i[o]=t,a[o]=o?1:1-e),t=i[o],e=a[o];i[0]===i[1]&&a[0]*a[1]!==1?r.splice(n,1):n++}return r;function s(u,l,f){return u.interval[f]<l.interval[f]||u.interval[f]===l.interval[f]&&(u.close[f]-l.close[f]===(f?-1:1)||!f&&s(u,l,1))}}function na(r){var t=parseFloat(r);return t==r&&(t!==0||!G(r)||r.indexOf("x")<=0)?t:NaN}function Lg(r){return!isNaN(na(r))}function Ig(){return Math.round(Math.random()*9)}function Pg(r,t){return t===0?r:Pg(t,r%t)}function Nc(r,t){return r==null?t:t==null?r:r*t/Pg(r,t)}function jt(r){throw new Error(r)}function zc(r,t,e){return(t-r)*e+r}var Rg="series\0",Eg="\0_ec_\0";function Bt(r){return r instanceof Array?r:r==null?[]:[r]}function Hc(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var n=0,i=e.length;n<i;n++){var a=e[n];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var Gc=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function wa(r){return V(r)&&!N(r)&&!(r instanceof Date)?r.value:r}function xw(r){return V(r)&&!(r instanceof Array)}function bw(r,t,e){var n=e==="normalMerge",i=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=Z();C(t,function(u,l){if(!V(u)){t[l]=null;return}});var s=Tw(r,o,e);return(n||i)&&Cw(s,r,o,t),n&&Mw(s,t),n||i?Dw(s,t,i):a&&Aw(s,t),Lw(s),s}function Tw(r,t,e){var n=[];if(e==="replaceAll")return n;for(var i=0;i<r.length;i++){var a=r[i];a&&a.id!=null&&t.set(a.id,i),n.push({existing:e==="replaceMerge"||ia(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return n}function Cw(r,t,e,n){C(n,function(i,a){if(!(!i||i.id==null)){var o=Vi(i.id),s=e.get(o);if(s!=null){var u=r[s];Re(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=i,u.existing=t[s],n[a]=null}}})}function Mw(r,t){C(t,function(e,n){if(!(!e||e.name==null))for(var i=0;i<r.length;i++){var a=r[i].existing;if(!r[i].newOption&&a&&(a.id==null||e.id==null)&&!ia(e)&&!ia(a)&&Og("name",a,e)){r[i].newOption=e,t[n]=null;return}}})}function Dw(r,t,e){C(t,function(n){if(n){for(var i,a=0;(i=r[a])&&(i.newOption||ia(i.existing)||i.existing&&n.id!=null&&!Og("id",n,i.existing));)a++;i?(i.newOption=n,i.brandNew=e):r.push({newOption:n,brandNew:e,existing:null,keyInfo:null}),a++}})}function Aw(r,t){C(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function Lw(r){var t=Z();C(r,function(e){var n=e.existing;n&&t.set(n.id,e)}),C(r,function(e){var n=e.newOption;Re(!n||n.id==null||!t.get(n.id)||t.get(n.id)===e,"id duplicates: "+(n&&n.id)),n&&n.id!=null&&t.set(n.id,e),!e.keyInfo&&(e.keyInfo={})}),C(r,function(e,n){var i=e.existing,a=e.newOption,o=e.keyInfo;if(V(a)){if(o.name=a.name!=null?Vi(a.name):i?i.name:Rg+n,i)o.id=Vi(i.id);else if(a.id!=null)o.id=Vi(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function Og(r,t,e){var n=Pe(t[r],null),i=Pe(e[r],null);return n!=null&&i!=null&&n===i}function Vi(r){return Pe(r,"")}function Pe(r,t){return r==null?t:G(r)?r:wt(r)||Fo(r)?r+"":t}function qf(r){var t=r.name;return!!(t&&t.indexOf(Rg))}function ia(r){return r&&r.id!=null&&Vi(r.id).indexOf(Eg)===0}function SI(r){return Eg+r}function Iw(r,t,e){C(r,function(n){var i=n.newOption;V(i)&&(n.keyInfo.mainType=t,n.keyInfo.subType=Pw(t,i,n.existing,e))})}function Pw(r,t,e,n){var i=t.type?t.type:e?e.subType:n.determineSubType(r,t);return i}function wI(r,t){var e={},n={};return i(r||[],e),i(t||[],n,e),[a(e),a(n)];function i(o,s,u){for(var l=0,f=o.length;l<f;l++){var h=Pe(o[l].seriesId,null);if(h==null)return;for(var v=Bt(o[l].dataIndex),c=u&&u[h],d=0,g=v.length;d<g;d++){var p=v[d];c&&c[p]?c[p]=null:(s[h]||(s[h]={}))[p]=1}}}function a(o,s){var u=[];for(var l in o)if(o.hasOwnProperty(l)&&o[l]!=null)if(s)u.push(+l);else{var f=a(o[l],!0);f.length&&u.push({seriesId:l,dataIndex:f})}return u}}function xa(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return N(t.dataIndex)?W(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return N(t.name)?W(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function Mt(){var r="__ec_inner_"+Rw++;return function(t){return t[r]||(t[r]={})}}var Rw=Ig();function xu(r,t,e){var n=Xf(t,e),i=n.mainTypeSpecified,a=n.queryOptionMap,o=n.others,s=o,u=e?e.defaultMainType:null;return!i&&u&&a.set(u,{}),a.each(function(l,f){var h=ba(r,f,l,{useDefault:u===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function Xf(r,t){var e;if(G(r)){var n={};n[r+"Index"]=0,e=n}else e=r;var i=Z(),a={},o=!1;return C(e,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&lt(t.includeMainTypes,f)<0)){o=o||!!f;var v=i.get(f)||i.set(f,{});v[h]=s}}),{mainTypeSpecified:o,queryOptionMap:i,others:a}}var Le={useDefault:!0,enableAll:!1,enableNone:!1},xI={useDefault:!1,enableAll:!0,enableNone:!0};function ba(r,t,e,n){n=n||Le;var i=e.index,a=e.id,o=e.name,s={models:null,specified:i!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=n.useDefault&&(u=r.getComponent(t))?[u]:[],s}return i==="none"||i===!1?(Re(n.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(i==="all"&&(Re(n.enableAll,'`"all"` is not a valid value on index option.'),i=a=o=null),s.models=r.queryComponents({mainType:t,index:i,id:a,name:o}),s)}function kg(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function Ew(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function Ow(r){return r==="auto"?Y.domSupported?"html":"richText":r||"html"}function kw(r,t,e,n,i){var a=t==null||t==="auto";if(n==null)return n;if(wt(n)){var o=zc(e||0,n,i);return It(o,a?Math.max(Ve(e||0),Ve(n)):t)}else{if(G(n))return i<1?e:n;for(var s=[],u=e,l=n,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var v=r.getDimensionInfo(h);if(v&&v.type==="ordinal")s[h]=(i<1&&u?u:l)[h];else{var c=u&&u[h]?u[h]:0,d=l[h],o=zc(c,d,i);s[h]=It(o,a?Math.max(Ve(c),Ve(d)):t)}}return s}}var dt=Mt(),Bw=function(r,t,e,n){if(n){var i=dt(n);i.dataIndex=e,i.dataType=t,i.seriesIndex=r,i.ssrType="chart",n.type==="group"&&n.traverse(function(a){var o=dt(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},Vc=1,Wc={},Bg=Mt(),$f=Mt(),Zf=0,gs=1,ys=2,Ar=["emphasis","blur","select"],Uc=["normal","emphasis","blur","select"],Fw=10,Nw=9,cn="highlight",Do="downplay",Wi="select",Ao="unselect",Ui="toggleSelect";function Mn(r){return r!=null&&r!=="none"}function ms(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function Fg(r){ms(r,"emphasis",ys)}function Ng(r){r.hoverState===ys&&ms(r,"normal",Zf)}function Kf(r){ms(r,"blur",gs)}function zg(r){r.hoverState===gs&&ms(r,"normal",Zf)}function zw(r){r.selected=!0}function Hw(r){r.selected=!1}function Yc(r,t,e){t(r,e)}function ur(r,t,e){Yc(r,t,e),r.isGroup&&r.traverse(function(n){Yc(n,t,e)})}function bI(r,t){switch(t){case"emphasis":r.hoverState=ys;break;case"normal":r.hoverState=Zf;break;case"blur":r.hoverState=gs;break;case"select":r.selected=!0}}function Gw(r,t,e,n){for(var i=r.style,a={},o=0;o<t.length;o++){var s=t[o],u=i[s];a[s]=u??(n&&n[s])}for(var o=0;o<r.animators.length;o++){var l=r.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(e)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function Vw(r,t,e,n){var i=e&&lt(e,"select")>=0,a=!1;if(r instanceof mt){var o=Bg(r),s=i&&o.selectFill||o.normalFill,u=i&&o.selectStroke||o.normalStroke;if(Mn(s)||Mn(u)){n=n||{};var l=n.style||{};l.fill==="inherit"?(a=!0,n=B({},n),l=B({},l),l.fill=s):!Mn(l.fill)&&Mn(s)?(a=!0,n=B({},n),l=B({},l),l.fill=Dl(s)):!Mn(l.stroke)&&Mn(u)&&(a||(n=B({},n),l=B({},l)),l.stroke=Dl(u)),n.style=l}}if(n&&n.z2==null){a||(n=B({},n));var f=r.z2EmphasisLift;n.z2=r.z2+(f??Fw)}return n}function Ww(r,t,e){if(e&&e.z2==null){e=B({},e);var n=r.z2SelectLift;e.z2=r.z2+(n??Nw)}return e}function Uw(r,t,e){var n=lt(r.currentStates,t)>=0,i=r.style.opacity,a=n?null:Gw(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=B({},e),o=B({opacity:n?i:a.opacity*.1},o),e.style=o),e}function bu(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return Vw(this,r,t,e);if(r==="blur")return Uw(this,r,e);if(r==="select")return Ww(this,r,e)}return e}function Yw(r){r.stateProxy=bu;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=bu),e&&(e.stateProxy=bu)}function qc(r,t){!Wg(r,t)&&!r.__highByOuter&&ur(r,Fg)}function Xc(r,t){!Wg(r,t)&&!r.__highByOuter&&ur(r,Ng)}function Fl(r,t){r.__highByOuter|=1<<(t||0),ur(r,Fg)}function Nl(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&ur(r,Ng)}function qw(r){ur(r,Kf)}function Hg(r){ur(r,zg)}function Gg(r){ur(r,zw)}function Vg(r){ur(r,Hw)}function Wg(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function Ug(r){var t=r.getModel(),e=[],n=[];t.eachComponent(function(i,a){var o=$f(a),s=i==="series",u=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&n.push(u),o.isBlured&&(u.group.traverse(function(l){zg(l)}),s&&e.push(a)),o.isBlured=!1}),C(n,function(i){i&&i.toggleBlurSeries&&i.toggleBlurSeries(e,!1,t)})}function zl(r,t,e,n){var i=n.getModel();e=e||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var v=l.getItemGraphicEl(f[h]);v&&Hg(v)}}if(r!=null&&!(!t||t==="none")){var o=i.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];i.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var v=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!v||t==="series"&&f)){var c=n.getViewOfSeriesModel(l);if(c.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||Kf(p)}),Yt(t))a(l.getData(),t);else if(V(t))for(var d=yt(t),g=0;g<d.length;g++)a(l.getData(d[g]),t[d[g]]);u.push(l),$f(l).isBlured=!0}}),i.eachComponent(function(l,f){if(l!=="series"){var h=n.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,i)}})}}function Hl(r,t,e){if(!(r==null||t==null)){var n=e.getModel().getComponent(r,t);if(n){$f(n).isBlured=!0;var i=e.getViewOfComponentModel(n);!i||!i.focusBlurEnabled||i.group.traverse(function(a){Kf(a)})}}}function Xw(r,t,e){var n=r.seriesIndex,i=r.getData(t.dataType);if(i){var a=xa(i,t);a=(N(a)?a[0]:a)||0;var o=i.getItemGraphicEl(a);if(!o)for(var s=i.count(),u=0;!o&&u<s;)o=i.getItemGraphicEl(u++);if(o){var l=dt(o);zl(n,l.focus,l.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&zl(n,f,h,e)}}}function jf(r,t,e,n){var i={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return i;var a=n.getModel().getComponent(r,t);if(!a)return i;var o=n.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return i;for(var s=o.findHighDownDispatchers(e),u,l=0;l<s.length;l++)if(dt(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function $w(r,t,e){var n=dt(r),i=jf(n.componentMainType,n.componentIndex,n.componentHighDownName,e),a=i.dispatchers,o=i.focusSelf;a?(o&&Hl(n.componentMainType,n.componentIndex,e),C(a,function(s){return qc(s,t)})):(zl(n.seriesIndex,n.focus,n.blurScope,e),n.focus==="self"&&Hl(n.componentMainType,n.componentIndex,e),qc(r,t))}function Zw(r,t,e){Ug(e);var n=dt(r),i=jf(n.componentMainType,n.componentIndex,n.componentHighDownName,e).dispatchers;i?C(i,function(a){return Xc(a,t)}):Xc(r,t)}function Kw(r,t,e){if(Vl(t)){var n=t.dataType,i=r.getData(n),a=xa(i,t);N(a)||(a=[a]),r[t.type===Ui?"toggleSelect":t.type===Wi?"select":"unselect"](a,n)}}function $c(r){var t=r.getAllData();C(t,function(e){var n=e.data,i=e.type;n.eachItemGraphicEl(function(a,o){r.isSelected(o,i)?Gg(a):Vg(a)})})}function jw(r){var t=[];return r.eachSeries(function(e){var n=e.getAllData();C(n,function(i){i.data;var a=i.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function qo(r,t,e){Yg(r,!0),ur(r,Yw),Jw(r,t,e)}function Qw(r){Yg(r,!1)}function TI(r,t,e,n){n?Qw(r):qo(r,t,e)}function Jw(r,t,e){var n=dt(r);t!=null?(n.focus=t,n.blurScope=e):n.focus&&(n.focus=null)}var Zc=["emphasis","blur","select"],tx={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function CI(r,t,e,n){e=e||"itemStyle";for(var i=0;i<Zc.length;i++){var a=Zc[i],o=t.getModel([a,e]),s=r.ensureState(a);s.style=o[tx[e]]()}}function Yg(r,t){var e=t===!1,n=r;r.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||n.__highDownDispatcher)&&(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Gl(r){return!!(r&&r.__highDownDispatcher)}function MI(r,t,e){var n=dt(r);n.componentMainType=t.mainType,n.componentIndex=t.componentIndex,n.componentHighDownName=e}function ex(r){var t=Wc[r];return t==null&&Vc<=32&&(t=Wc[r]=Vc++),t}function Vl(r){var t=r.type;return t===Wi||t===Ao||t===Ui}function Kc(r){var t=r.type;return t===cn||t===Do}function rx(r){var t=Bg(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var Dn=Zn.CMD,nx=[[],[],[]],jc=Math.sqrt,ix=Math.atan2;function ax(r,t){if(t){var e=r.data,n=r.len(),i,a,o,s,u,l,f=Dn.M,h=Dn.C,v=Dn.L,c=Dn.R,d=Dn.A,g=Dn.Q;for(o=0,s=0;o<n;){switch(i=e[o++],s=o,a=0,i){case f:a=1;break;case v:a=1;break;case h:a=3;break;case g:a=2;break;case d:var p=t[4],y=t[5],m=jc(t[0]*t[0]+t[1]*t[1]),_=jc(t[2]*t[2]+t[3]*t[3]),S=ix(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=p,e[o]*=_,e[o++]+=y,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case c:l[0]=e[o++],l[1]=e[o++],Ut(l,l,t),e[s++]=l[0],e[s++]=l[1],l[0]+=e[o++],l[1]+=e[o++],Ut(l,l,t),e[s++]=l[0],e[s++]=l[1]}for(u=0;u<a;u++){var b=nx[u];b[0]=e[o++],b[1]=e[o++],Ut(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var Tu=Math.sqrt,Qa=Math.sin,Ja=Math.cos,fi=Math.PI;function Qc(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Wl(r,t){return(r[0]*t[0]+r[1]*t[1])/(Qc(r)*Qc(t))}function Jc(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Wl(r,t))}function tv(r,t,e,n,i,a,o,s,u,l,f){var h=u*(fi/180),v=Ja(h)*(r-e)/2+Qa(h)*(t-n)/2,c=-1*Qa(h)*(r-e)/2+Ja(h)*(t-n)/2,d=v*v/(o*o)+c*c/(s*s);d>1&&(o*=Tu(d),s*=Tu(d));var g=(i===a?-1:1)*Tu((o*o*(s*s)-o*o*(c*c)-s*s*(v*v))/(o*o*(c*c)+s*s*(v*v)))||0,p=g*o*c/s,y=g*-s*v/o,m=(r+e)/2+Ja(h)*p-Qa(h)*y,_=(t+n)/2+Qa(h)*p+Ja(h)*y,S=Jc([1,0],[(v-p)/o,(c-y)/s]),b=[(v-p)/o,(c-y)/s],w=[(-1*v-p)/o,(-1*c-y)/s],T=Jc(b,w);if(Wl(b,w)<=-1&&(T=fi),Wl(b,w)>=1&&(T=0),T<0){var M=Math.round(T/fi*1e6)/1e6;T=fi*2+M%2*fi}f.addData(l,m,_,o,s,S,T,h,a)}var ox=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,sx=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function ux(r){var t=new Zn;if(!r)return t;var e=0,n=0,i=e,a=n,o,s=Zn.CMD,u=r.match(ox);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),v=void 0,c=f.match(sx)||[],d=c.length,g=0;g<d;g++)c[g]=parseFloat(c[g]);for(var p=0;p<d;){var y=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,T=void 0,M=e,A=n,L=void 0,I=void 0;switch(h){case"l":e+=c[p++],n+=c[p++],v=s.L,t.addData(v,e,n);break;case"L":e=c[p++],n=c[p++],v=s.L,t.addData(v,e,n);break;case"m":e+=c[p++],n+=c[p++],v=s.M,t.addData(v,e,n),i=e,a=n,h="l";break;case"M":e=c[p++],n=c[p++],v=s.M,t.addData(v,e,n),i=e,a=n,h="L";break;case"h":e+=c[p++],v=s.L,t.addData(v,e,n);break;case"H":e=c[p++],v=s.L,t.addData(v,e,n);break;case"v":n+=c[p++],v=s.L,t.addData(v,e,n);break;case"V":n=c[p++],v=s.L,t.addData(v,e,n);break;case"C":v=s.C,t.addData(v,c[p++],c[p++],c[p++],c[p++],c[p++],c[p++]),e=c[p-2],n=c[p-1];break;case"c":v=s.C,t.addData(v,c[p++]+e,c[p++]+n,c[p++]+e,c[p++]+n,c[p++]+e,c[p++]+n),e+=c[p-2],n+=c[p-1];break;case"S":y=e,m=n,L=t.len(),I=t.data,o===s.C&&(y+=e-I[L-4],m+=n-I[L-3]),v=s.C,M=c[p++],A=c[p++],e=c[p++],n=c[p++],t.addData(v,y,m,M,A,e,n);break;case"s":y=e,m=n,L=t.len(),I=t.data,o===s.C&&(y+=e-I[L-4],m+=n-I[L-3]),v=s.C,M=e+c[p++],A=n+c[p++],e+=c[p++],n+=c[p++],t.addData(v,y,m,M,A,e,n);break;case"Q":M=c[p++],A=c[p++],e=c[p++],n=c[p++],v=s.Q,t.addData(v,M,A,e,n);break;case"q":M=c[p++]+e,A=c[p++]+n,e+=c[p++],n+=c[p++],v=s.Q,t.addData(v,M,A,e,n);break;case"T":y=e,m=n,L=t.len(),I=t.data,o===s.Q&&(y+=e-I[L-4],m+=n-I[L-3]),e=c[p++],n=c[p++],v=s.Q,t.addData(v,y,m,e,n);break;case"t":y=e,m=n,L=t.len(),I=t.data,o===s.Q&&(y+=e-I[L-4],m+=n-I[L-3]),e+=c[p++],n+=c[p++],v=s.Q,t.addData(v,y,m,e,n);break;case"A":_=c[p++],S=c[p++],b=c[p++],w=c[p++],T=c[p++],M=e,A=n,e=c[p++],n=c[p++],v=s.A,tv(M,A,e,n,w,T,_,S,b,v,t);break;case"a":_=c[p++],S=c[p++],b=c[p++],w=c[p++],T=c[p++],M=e,A=n,e+=c[p++],n+=c[p++],v=s.A,tv(M,A,e,n,w,T,_,S,b,v,t);break}}(h==="z"||h==="Z")&&(v=s.Z,t.addData(v),e=i,n=a),o=v}return t.toStatic(),t}var qg=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(mt);function Xg(r){return r.setData!=null}function $g(r,t){var e=ux(r),n=B({},t);return n.buildPath=function(i){if(Xg(i)){i.setData(e.data);var a=i.getContext();a&&i.rebuildPath(a,1)}else{var a=i;e.rebuildPath(a,1)}},n.applyTransform=function(i){ax(e,i),this.dirtyShape()},n}function lx(r,t){return new qg($g(r,t))}function fx(r,t){var e=$g(r,t),n=function(i){H(a,i);function a(o){var s=i.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(qg);return n}function hx(r,t){for(var e=[],n=r.length,i=0;i<n;i++){var a=r[i];e.push(a.getUpdatedPathProxy(!0))}var o=new mt(t);return o.createPathProxy(),o.buildPath=function(s){if(Xg(s)){s.appendPath(e);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}var ae=function(r){H(t,r);function t(e){var n=r.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var n=this._children,i=0;i<n.length;i++)if(n[i].name===e)return n[i]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,n){if(e&&e!==this&&e.parent!==this&&n&&n.parent===this){var i=this._children,a=i.indexOf(n);a>=0&&(i.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,n){var i=lt(this._children,e);return i>=0&&this.replaceAt(n,i),this},t.prototype.replaceAt=function(e,n){var i=this._children,a=i[n];if(e&&e!==this&&e.parent!==this&&e!==a){i[n]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var n=this.__zr;n&&n!==e.__zr&&e.addSelfToZr(n),n&&n.refresh()},t.prototype.remove=function(e){var n=this.__zr,i=this._children,a=lt(i,e);return a<0?this:(i.splice(a,1),e.parent=null,n&&e.removeSelfFromZr(n),n&&n.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,n=this.__zr,i=0;i<e.length;i++){var a=e[i];n&&a.removeSelfFromZr(n),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,n){for(var i=this._children,a=0;a<i.length;a++){var o=i[a];e.call(n,o,a)}return this},t.prototype.traverse=function(e,n){for(var i=0;i<this._children.length;i++){var a=this._children[i],o=e.call(n,a);a.isGroup&&!o&&a.traverse(e,n)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var n=new it(0,0,0,0),i=e||this._children,a=[],o=null,s=0;s<i.length;s++){var u=i[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(it.applyTransform(n,l,f),o=o||n.clone(),o.union(n)):(o=o||l.clone(),o.union(l))}}return o||n},t}(vs);ae.prototype.type="group";var cx=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),Ta=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new cx},t.prototype.buildPath=function(e,n){e.moveTo(n.cx+n.r,n.cy),e.arc(n.cx,n.cy,n.r,0,Math.PI*2)},t}(mt);Ta.prototype.type="circle";var vx=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),_s=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new vx},t.prototype.buildPath=function(e,n){var i=.5522848,a=n.cx,o=n.cy,s=n.rx,u=n.ry,l=s*i,f=u*i;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),e.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),e.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),e.closePath()},t}(mt);_s.prototype.type="ellipse";var Zg=Math.PI,Cu=Zg*2,Zr=Math.sin,An=Math.cos,dx=Math.acos,Rt=Math.atan2,ev=Math.abs,Yi=Math.sqrt,ki=Math.max,ze=Math.min,Ce=1e-4;function px(r,t,e,n,i,a,o,s){var u=e-r,l=n-t,f=o-i,h=s-a,v=h*u-f*l;if(!(v*v<Ce))return v=(f*(t-a)-h*(r-i))/v,[r+v*u,t+v*l]}function to(r,t,e,n,i,a,o){var s=r-e,u=t-n,l=(o?a:-a)/Yi(s*s+u*u),f=l*u,h=-l*s,v=r+f,c=t+h,d=e+f,g=n+h,p=(v+d)/2,y=(c+g)/2,m=d-v,_=g-c,S=m*m+_*_,b=i-a,w=v*g-d*c,T=(_<0?-1:1)*Yi(ki(0,b*b*S-w*w)),M=(w*_-m*T)/S,A=(-w*m-_*T)/S,L=(w*_+m*T)/S,I=(-w*m+_*T)/S,E=M-p,O=A-y,R=L-p,k=I-y;return E*E+O*O>R*R+k*k&&(M=L,A=I),{cx:M,cy:A,x0:-f,y0:-h,x1:M*(i/b-1),y1:A*(i/b-1)}}function gx(r){var t;if(N(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function yx(r,t){var e,n=ki(t.r,0),i=ki(t.r0||0,0),a=n>0,o=i>0;if(!(!a&&!o)){if(a||(n=i,i=0),i>n){var s=n;n=i,i=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,v=!!t.clockwise,c=ev(l-u),d=c>Cu&&c%Cu;if(d>Ce&&(c=d),!(n>Ce))r.moveTo(f,h);else if(c>Cu-Ce)r.moveTo(f+n*An(u),h+n*Zr(u)),r.arc(f,h,n,u,l,!v),i>Ce&&(r.moveTo(f+i*An(l),h+i*Zr(l)),r.arc(f,h,i,l,u,v));else{var g=void 0,p=void 0,y=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,T=void 0,M=void 0,A=void 0,L=void 0,I=void 0,E=void 0,O=void 0,R=void 0,k=n*An(u),F=n*Zr(u),K=i*An(l),U=i*Zr(l),q=c>Ce;if(q){var Q=t.cornerRadius;Q&&(e=gx(Q),g=e[0],p=e[1],y=e[2],m=e[3]);var ht=ev(n-i)/2;if(_=ze(ht,y),S=ze(ht,m),b=ze(ht,g),w=ze(ht,p),A=T=ki(_,S),L=M=ki(b,w),(T>Ce||M>Ce)&&(I=n*An(l),E=n*Zr(l),O=i*An(u),R=i*Zr(u),c<Zg)){var st=px(k,F,O,R,I,E,K,U);if(st){var $=k-st[0],Ft=F-st[1],Oe=I-st[0],te=E-st[1],ke=1/Zr(dx(($*Oe+Ft*te)/(Yi($*$+Ft*Ft)*Yi(Oe*Oe+te*te)))/2),se=Yi(st[0]*st[0]+st[1]*st[1]);A=ze(T,(n-se)/(ke+1)),L=ze(M,(i-se)/(ke-1))}}}if(!q)r.moveTo(f+k,h+F);else if(A>Ce){var Pt=ze(y,A),xt=ze(m,A),X=to(O,R,k,F,n,Pt,v),tt=to(I,E,K,U,n,xt,v);r.moveTo(f+X.cx+X.x0,h+X.cy+X.y0),A<T&&Pt===xt?r.arc(f+X.cx,h+X.cy,A,Rt(X.y0,X.x0),Rt(tt.y0,tt.x0),!v):(Pt>0&&r.arc(f+X.cx,h+X.cy,Pt,Rt(X.y0,X.x0),Rt(X.y1,X.x1),!v),r.arc(f,h,n,Rt(X.cy+X.y1,X.cx+X.x1),Rt(tt.cy+tt.y1,tt.cx+tt.x1),!v),xt>0&&r.arc(f+tt.cx,h+tt.cy,xt,Rt(tt.y1,tt.x1),Rt(tt.y0,tt.x0),!v))}else r.moveTo(f+k,h+F),r.arc(f,h,n,u,l,!v);if(!(i>Ce)||!q)r.lineTo(f+K,h+U);else if(L>Ce){var Pt=ze(g,L),xt=ze(p,L),X=to(K,U,I,E,i,-xt,v),tt=to(k,F,O,R,i,-Pt,v);r.lineTo(f+X.cx+X.x0,h+X.cy+X.y0),L<M&&Pt===xt?r.arc(f+X.cx,h+X.cy,L,Rt(X.y0,X.x0),Rt(tt.y0,tt.x0),!v):(xt>0&&r.arc(f+X.cx,h+X.cy,xt,Rt(X.y0,X.x0),Rt(X.y1,X.x1),!v),r.arc(f,h,i,Rt(X.cy+X.y1,X.cx+X.x1),Rt(tt.cy+tt.y1,tt.cx+tt.x1),v),Pt>0&&r.arc(f+tt.cx,h+tt.cy,Pt,Rt(tt.y1,tt.x1),Rt(tt.y0,tt.x0),!v))}else r.lineTo(f+K,h+U),r.arc(f,h,i,l,u,v)}r.closePath()}}}var mx=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),Ss=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new mx},t.prototype.buildPath=function(e,n){yx(e,n)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(mt);Ss.prototype.type="sector";var _x=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),ws=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new _x},t.prototype.buildPath=function(e,n){var i=n.cx,a=n.cy,o=Math.PI*2;e.moveTo(i+n.r,a),e.arc(i,a,n.r,0,o,!1),e.moveTo(i+n.r0,a),e.arc(i,a,n.r0,0,o,!0)},t}(mt);ws.prototype.type="ring";function Sx(r,t,e,n){var i=[],a=[],o=[],s=[],u,l,f,h;if(n){f=[1/0,1/0],h=[-1/0,-1/0];for(var v=0,c=r.length;v<c;v++)yr(f,f,r[v]),mr(h,h,r[v]);yr(f,f,n[0]),mr(h,h,n[1])}for(var v=0,c=r.length;v<c;v++){var d=r[v];if(e)u=r[v?v-1:c-1],l=r[(v+1)%c];else if(v===0||v===c-1){i.push(ig(r[v]));continue}else u=r[v-1],l=r[v+1];ag(a,l,u),bo(a,a,t);var g=Ho(d,u),p=Ho(d,l),y=g+p;y!==0&&(g/=y,p/=y),bo(o,a,-g),bo(s,a,p);var m=bl([],d,o),_=bl([],d,s);n&&(mr(m,m,f),yr(m,m,h),mr(_,_,f),yr(_,_,h)),i.push(m),i.push(_)}return e&&i.push(i.shift()),i}function Kg(r,t,e){var n=t.smooth,i=t.points;if(i&&i.length>=2){if(n){var a=Sx(i,n,e,t.smoothConstraint);r.moveTo(i[0][0],i[0][1]);for(var o=i.length,s=0;s<(e?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=i[(s+1)%o];r.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{r.moveTo(i[0][0],i[0][1]);for(var s=1,h=i.length;s<h;s++)r.lineTo(i[s][0],i[s][1])}e&&r.closePath()}}var xx=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),xs=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new xx},t.prototype.buildPath=function(e,n){Kg(e,n,!0)},t}(mt);xs.prototype.type="polygon";var bx=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),bs=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new bx},t.prototype.buildPath=function(e,n){Kg(e,n,!1)},t}(mt);bs.prototype.type="polyline";var Tx={},Cx=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),sr=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Cx},t.prototype.buildPath=function(e,n){var i,a,o,s;if(this.subPixelOptimize){var u=bg(Tx,n,this.style);i=u.x1,a=u.y1,o=u.x2,s=u.y2}else i=n.x1,a=n.y1,o=n.x2,s=n.y2;var l=n.percent;l!==0&&(e.moveTo(i,a),l<1&&(o=i*(1-l)+o*l,s=a*(1-l)+s*l),e.lineTo(o,s))},t.prototype.pointAt=function(e){var n=this.shape;return[n.x1*(1-e)+n.x2*e,n.y1*(1-e)+n.y2*e]},t}(mt);sr.prototype.type="line";var $t=[],Mx=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function rv(r,t,e){var n=r.cpx2,i=r.cpy2;return n!=null||i!=null?[(e?gc:Ht)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?gc:Ht)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?yc:Kt)(r.x1,r.cpx1,r.x2,t),(e?yc:Kt)(r.y1,r.cpy1,r.y2,t)]}var Ts=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Mx},t.prototype.buildPath=function(e,n){var i=n.x1,a=n.y1,o=n.x2,s=n.y2,u=n.cpx1,l=n.cpy1,f=n.cpx2,h=n.cpy2,v=n.percent;v!==0&&(e.moveTo(i,a),f==null||h==null?(v<1&&(Wo(i,u,o,v,$t),u=$t[1],o=$t[2],Wo(a,l,s,v,$t),l=$t[1],s=$t[2]),e.quadraticCurveTo(u,l,o,s)):(v<1&&(Vo(i,u,f,o,v,$t),u=$t[1],f=$t[2],o=$t[3],Vo(a,l,h,s,v,$t),l=$t[1],h=$t[2],s=$t[3]),e.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(e){return rv(this.shape,e,!1)},t.prototype.tangentAt=function(e){var n=rv(this.shape,e,!0);return og(n,n)},t}(mt);Ts.prototype.type="bezier-curve";var Dx=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),Ca=function(r){H(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Dx},t.prototype.buildPath=function(e,n){var i=n.cx,a=n.cy,o=Math.max(n.r,0),s=n.startAngle,u=n.endAngle,l=n.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+i,h*o+a),e.arc(i,a,o,s,u,!l)},t}(mt);Ca.prototype.type="arc";var jg=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,n=this.shapeChanged(),i=0;i<e.length;i++)n=n||e[i].shapeChanged();n&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],n=this.getGlobalScale(),i=0;i<e.length;i++)e[i].path||e[i].createPathProxy(),e[i].path.setScale(n[0],n[1],e[i].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,n){for(var i=n.paths||[],a=0;a<i.length;a++)i[a].buildPath(e,i[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],n=0;n<e.length;n++)e[n].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),mt.prototype.getBoundingRect.call(this)},t}(mt),Qg=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}(),Jg=function(r){H(t,r);function t(e,n,i,a,o,s){var u=r.call(this,o)||this;return u.x=e??0,u.y=n??0,u.x2=i??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(Qg),ty=function(r){H(t,r);function t(e,n,i,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=n??.5,s.r=i??.5,s.type="radial",s.global=o||!1,s}return t}(Qg),Kr=[0,0],jr=[0,0],eo=new vt,ro=new vt,Xo=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new vt;for(var n=0;n<2;n++)this._axes[n]=new vt;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(n[0].set(a,o),n[1].set(s,o),n[2].set(s,u),n[3].set(a,u),e)for(var l=0;l<4;l++)n[l].transform(e);vt.sub(i[0],n[1],n[0]),vt.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(var l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},r.prototype.intersect=function(t,e){var n=!0,i=!e;return eo.set(1/0,1/0),ro.set(0,0),!this._intersectCheckOneSide(this,t,eo,ro,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,eo,ro,i,-1)&&(n=!1,i)||i||vt.copy(e,n?eo:ro),n},r.prototype._intersectCheckOneSide=function(t,e,n,i,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,Kr),this._getProjMinMaxOnAxis(u,e._corners,jr),Kr[1]<jr[0]||Kr[0]>jr[1]){if(s=!1,a)return s;var f=Math.abs(jr[0]-Kr[1]),h=Math.abs(Kr[0]-jr[1]);Math.min(f,h)>i.len()&&(f<h?vt.scale(i,l,-f*o):vt.scale(i,l,h*o))}else if(n){var f=Math.abs(jr[0]-Kr[1]),h=Math.abs(Kr[0]-jr[1]);Math.min(f,h)<n.len()&&(f<h?vt.scale(n,l,f*o):vt.scale(n,l,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],a=this._origin,o=e[0].dot(i)+a[t],s=o,u=o,l=1;l<e.length;l++){var f=e[l].dot(i)+a[t];s=Math.min(f,s),u=Math.max(f,u)}n[0]=s,n[1]=u},r}(),Ax=[],ey=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,n){e.call(n,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,n){n?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,n){n=n||!1;for(var i=0;i<e.length;i++)this.addDisplayable(e[i],n)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var n=this._cursor;n<this._displayables.length;n++)e&&e(this._displayables[n]);for(var n=0;n<this._temporaryDisplayables.length;n++)e&&e(this._temporaryDisplayables[n])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var n=this._displayables[e];n.parent=this,n.update(),n.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var n=this._temporaryDisplayables[e];n.parent=this,n.update(),n.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new it(1/0,1/0,-1/0,-1/0),n=0;n<this._displayables.length;n++){var i=this._displayables[n],a=i.getBoundingRect().clone();i.needLocalTransform()&&a.applyTransform(i.getLocalTransform(Ax)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,n){var i=this.transformCoordToLocal(e,n),a=this.getBoundingRect();if(a.contain(i[0],i[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,n))return!0}return!1},t}(Sa),Lx=Mt();function Ix(r,t,e,n,i){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=r==="update";if(s){var l=void 0,f=void 0,h=void 0;n?(l=J(n.duration,200),f=J(n.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),j(h)&&(h=h(e,i)),j(l)&&(l=l(e));var v={duration:l||0,delay:h,easing:f};return v}else return null}function Qf(r,t,e,n,i,a,o){var s=!1,u;j(i)?(o=a,a=i,i=null):V(i)&&(a=i.cb,o=i.during,s=i.isFrom,u=i.removeOpt,i=i.dataIndex);var l=r==="leave";l||t.stopAnimation("leave");var f=Ix(r,n,i,l?u||{}:null,n&&n.getAnimationDelayParams?n.getAnimationDelayParams(t,i):null);if(f&&f.duration>0){var h=f.duration,v=f.delay,c=f.easing,d={duration:h,delay:v||0,easing:c,done:a,force:!!a||!!o,setToFinal:!l,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function Qn(r,t,e,n,i,a){Qf("update",r,t,e,n,i,a)}function Jf(r,t,e,n,i,a){Qf("enter",r,t,e,n,i,a)}function qi(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function ry(r,t,e,n,i,a){qi(r)||Qf("leave",r,t,e,n,i,a)}function nv(r,t,e,n){r.removeTextContent(),r.removeTextGuideLine(),ry(r,{style:{opacity:0}},t,e,n)}function Px(r,t,e){function n(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(i){i.isGroup||nv(i,t,e,n)}):nv(r,t,e,n)}function DI(r){Lx(r).oldStyle=r.style}var $o=Math.max,Zo=Math.min,Ul={};function ny(r){return mt.extend(r)}var Rx=fx;function iy(r,t){return Rx(r,t)}function Se(r,t){Ul[r]=t}function ay(r){if(Ul.hasOwnProperty(r))return Ul[r]}function Cs(r,t,e,n){var i=lx(r,t);return e&&(n==="center"&&(e=oy(e,i.getBoundingRect())),eh(i,e)),i}function th(r,t,e){var n=new Pr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(i){if(e==="center"){var a={width:i.width,height:i.height};n.setStyle(oy(t,a))}}});return n}function oy(r,t){var e=t.width/t.height,n=r.height*e,i;n<=r.width?i=r.height:(n=r.width,i=n/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-n/2,y:o-i/2,width:n,height:i}}var sy=hx;function eh(r,t){if(r.applyTransform){var e=r.getBoundingRect(),n=e.calculateTransform(t);r.applyTransform(n)}}function aa(r,t){return bg(r,r,{lineWidth:t}),r}function Ex(r){return Tg(r.shape,r.shape,r.style),r}var Ox=an;function uy(r,t){for(var e=ma([]);r&&r!==t;)br(e,r.getLocalTransform(),e),r=r.parent;return e}function rh(r,t,e){return t&&!Yt(t)&&(t=Vf.getLocalTransform(t)),e&&(t=_a([],t)),Ut([],r,t)}function kx(r,t,e){var n=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),i=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-n:r==="right"?n:0,r==="top"?-i:r==="bottom"?i:0];return a=rh(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function iv(r){return!r.isGroup}function Bx(r){return r.shape!=null}function ly(r,t,e){if(!r||!t)return;function n(o){var s={};return o.traverse(function(u){iv(u)&&u.anid&&(s[u.anid]=u)}),s}function i(o){var s={x:o.x,y:o.y,rotation:o.rotation};return Bx(o)&&(s.shape=B({},o.shape)),s}var a=n(r);t.traverse(function(o){if(iv(o)&&o.anid){var s=a[o.anid];if(s){var u=i(o);o.attr(i(s)),Qn(o,u,e,dt(o).dataIndex)}}})}function fy(r,t){return W(r,function(e){var n=e[0];n=$o(n,t.x),n=Zo(n,t.x+t.width);var i=e[1];return i=$o(i,t.y),i=Zo(i,t.y+t.height),[n,i]})}function hy(r,t){var e=$o(r.x,t.x),n=Zo(r.x+r.width,t.x+t.width),i=$o(r.y,t.y),a=Zo(r.y+r.height,t.y+t.height);if(n>=e&&a>=i)return{x:e,y:i,width:n-e,height:a-i}}function Ms(r,t,e){var n=B({rectHover:!0},t),i=n.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(i.image=r.slice(8),ft(i,e),new Pr(n)):Cs(r.replace("path://",""),n,e,"center")}function Fx(r,t,e,n,i){for(var a=0,o=i[i.length-1];a<i.length;a++){var s=i[a];if(cy(r,t,e,n,s[0],s[1],o[0],o[1]))return!0;o=s}}function cy(r,t,e,n,i,a,o,s){var u=e-r,l=n-t,f=o-i,h=s-a,v=Mu(f,h,u,l);if(Nx(v))return!1;var c=r-i,d=t-a,g=Mu(c,d,u,l)/v;if(g<0||g>1)return!1;var p=Mu(c,d,f,h)/v;return!(p<0||p>1)}function Mu(r,t,e,n){return r*n-e*t}function Nx(r){return r<=1e-6&&r>=-1e-6}function Ds(r){var t=r.itemTooltipOption,e=r.componentModel,n=r.itemName,i=G(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:n,$vars:["name"]};s[a+"Index"]=o;var u=r.formatterParamsExtra;u&&C(yt(u),function(f){Dr(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=dt(r.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:n,option:ft({content:n,encodeHTMLContent:!0,formatterParams:s},i)}}function av(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function vy(r,t){if(r)if(N(r))for(var e=0;e<r.length;e++)av(r[e],t);else av(r,t)}Se("circle",Ta);Se("ellipse",_s);Se("sector",Ss);Se("ring",ws);Se("polygon",xs);Se("polyline",bs);Se("rect",Lt);Se("line",sr);Se("bezierCurve",Ts);Se("arc",Ca);const zx=Object.freeze(Object.defineProperty({__proto__:null,Arc:Ca,BezierCurve:Ts,BoundingRect:it,Circle:Ta,CompoundPath:jg,Ellipse:_s,Group:ae,Image:Pr,IncrementalDisplayable:ey,Line:sr,LinearGradient:Jg,OrientedBoundingRect:Xo,Path:mt,Point:vt,Polygon:xs,Polyline:bs,RadialGradient:ty,Rect:Lt,Ring:ws,Sector:Ss,Text:Xt,applyTransform:rh,clipPointsByRect:fy,clipRectByRect:hy,createIcon:Ms,extendPath:iy,extendShape:ny,getShapeClass:ay,getTransform:uy,groupTransition:ly,initProps:Jf,isElementRemoved:qi,lineLineIntersect:cy,linePolygonIntersect:Fx,makeImage:th,makePath:Cs,mergePath:sy,registerShape:Se,removeElement:ry,removeElementWithFadeOut:Px,resizePath:eh,setTooltipConfig:Ds,subPixelOptimize:Ox,subPixelOptimizeLine:aa,subPixelOptimizeRect:Ex,transformDirection:kx,traverseElements:vy,updateProps:Qn},Symbol.toStringTag,{value:"Module"}));var As={};function dy(r,t){for(var e=0;e<Ar.length;e++){var n=Ar[e],i=t[n],a=r.ensureState(n);a.style=a.style||{},a.style.text=i}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function Yl(r,t,e){var n=r.labelFetcher,i=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;n&&(s=n.getFormattedLabel(i,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=j(r.defaultText)?r.defaultText(i,r,e):r.defaultText);for(var u={normal:s},l=0;l<Ar.length;l++){var f=Ar[l],h=t[f];u[f]=J(n?n.getFormattedLabel(i,f,null,a,h&&h.get("formatter")):null,s)}return u}function Hx(r,t,e,n){e=e||As;for(var i=r instanceof Xt,a=!1,o=0;o<Uc.length;o++){var s=t[Uc[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=i?r:r.getTextContent();if(a){i||(u||(u=new Xt,r.setTextContent(u)),r.stateProxy&&(u.stateProxy=r.stateProxy));var l=Yl(e,t),f=t.normal,h=!!f.getShallow("show"),v=gn(f,n&&n.normal,e,!1,!i);v.text=l.normal,i||r.setTextConfig(ov(f,e,!1));for(var o=0;o<Ar.length;o++){var c=Ar[o],s=t[c];if(s){var d=u.ensureState(c),g=!!J(s.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=gn(s,n&&n[c],e,!0,!i),d.style.text=l[c],!i){var p=r.ensureState(c);p.textConfig=ov(s,e,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(v.x=u.style.x),u.style.y!=null&&(v.y=u.style.y),u.ignore=!h,u.useStyle(v),u.dirty(),e.enableTextSetter&&(nh(u).setLabelText=function(y){var m=Yl(e,t,y);dy(u,m)})}else u&&(u.ignore=!0);r.dirty()}function AI(r,t){t=t||"label";for(var e={normal:r.getModel(t)},n=0;n<Ar.length;n++){var i=Ar[n];e[i]=r.getModel([i,t])}return e}function gn(r,t,e,n,i){var a={};return Gx(a,r,e,n,i),t&&B(a,t),a}function ov(r,t,e){t=t||{};var n={},i,a=r.getShallow("rotate"),o=J(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return i=r.getShallow("position")||(e?null:"inside"),i==="outside"&&(i=t.defaultOutsidePosition||"top"),i!=null&&(n.position=i),s!=null&&(n.offset=s),a!=null&&(a*=Math.PI/180,n.rotation=a),o!=null&&(n.distance=o),n.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",n}function Gx(r,t,e,n,i){e=e||As;var a=t.ecModel,o=a&&a.option.textStyle,s=Vx(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);fv(u[l]={},f,o,e,n,i,!1,!0)}}u&&(r.rich=u);var h=t.get("overflow");h&&(r.overflow=h);var v=t.get("minMargin");v!=null&&(r.margin=v),fv(r,t,o,e,n,i,!0,!1)}function Vx(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||As).rich;if(e){t=t||{};for(var n=yt(e),i=0;i<n.length;i++){var a=n[i];t[a]=1}}r=r.parentModel}return t}var sv=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],uv=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],lv=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function fv(r,t,e,n,i,a,o,s){e=!i&&e||As;var u=n&&n.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=J(t.getShallow("opacity"),e.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||e.color,f=f||e.textBorderColor),l!=null&&(r.fill=l),f!=null&&(r.stroke=f);var v=J(t.getShallow("textBorderWidth"),e.textBorderWidth);v!=null&&(r.lineWidth=v);var c=J(t.getShallow("textBorderType"),e.textBorderType);c!=null&&(r.lineDash=c);var d=J(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!i&&h==null&&!s&&(h=n&&n.defaultOpacity),h!=null&&(r.opacity=h),!i&&!a&&r.fill==null&&n.inheritColor&&(r.fill=n.inheritColor);for(var g=0;g<sv.length;g++){var p=sv[g],y=J(t.getShallow(p),e[p]);y!=null&&(r[p]=y)}for(var g=0;g<uv.length;g++){var p=uv[g],y=t.getShallow(p);y!=null&&(r[p]=y)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!n.disableBox){for(var g=0;g<lv.length;g++){var p=lv[g],y=t.getShallow(p);y!=null&&(r[p]=y)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&u&&(r.backgroundColor=u),(r.borderColor==="auto"||r.borderColor==="inherit")&&u&&(r.borderColor=u)}}function Wx(r,t){var e=t&&t.getModel("textStyle");return Ae([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var nh=Mt();function LI(r,t,e,n){if(r){var i=nh(r);i.prevValue=i.value,i.value=e;var a=t.normal;i.valueAnimation=a.get("valueAnimation"),i.valueAnimation&&(i.precision=a.get("precision"),i.defaultInterpolatedText=n,i.statesModels=t)}}function II(r,t,e,n,i){var a=nh(r);if(!a.valueAnimation||a.prevValue===a.value)return;var o=a.defaultInterpolatedText,s=J(a.interpolatedValue,a.prevValue),u=a.value;function l(f){var h=kw(e,a.precision,s,u,f);a.interpolatedValue=f===1?null:h;var v=Yl({labelDataIndex:t,labelFetcher:i,defaultText:o?o(h):h+""},a.statesModels,h);dy(r,v)}r.percent=0,(a.prevValue==null?Jf:Qn)(r,{percent:1},n,t,null,l)}var Ux=["textStyle","color"],Du=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Au=new Xt,Yx=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Ux):null)},r.prototype.getFont=function(){return Wx({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<Du.length;n++)e[Du[n]]=this.getShallow(Du[n]);return Au.useStyle(e),Au.update(),Au.getBoundingRect()},r}(),py=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],qx=Qi(py),Xx=function(){function r(){}return r.prototype.getLineStyle=function(t){return qx(this,t)},r}(),gy=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],$x=Qi(gy),Zx=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return $x(this,t,e)},r}(),gt=function(){function r(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return r.prototype.init=function(t,e,n){},r.prototype.mergeOption=function(t,e){nt(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var n=this.option,i=n==null?n:n[t];if(i==null&&!e){var a=this.parentModel;a&&(i=a.getShallow(t))}return i},r.prototype.getModel=function(t,e){var n=t!=null,i=n?this.parsePath(t):null,a=n?this._doGet(i):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(i)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(et(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!Y.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&!(t[i]&&(n=n&&typeof n=="object"?n[t[i]]:null,n==null));i++);return n==null&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},r}();Bf(gt);L1(gt);_e(gt,Xx);_e(gt,Zx);_e(gt,O1);_e(gt,Yx);function hi(r){return r==null?0:r.length||1}function hv(r){return r}var Kx=function(){function r(t,e,n,i,a,o){this._old=t,this._new=e,this._oldKeyGetter=n||hv,this._newKeyGetter=i||hv,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=i[o],u=n[s],l=hi(u);if(l>1){var f=u.shift();u.length===1&&(n[s]=u[0]),this._update&&this._update(f,o)}else l===1?(n[s]=null,this._update&&this._update(u,o)):this._remove&&this._remove(o)}this._performRestAdd(a,n)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},a=[],o=[];this._initIndexMap(t,n,a,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var u=a[s],l=n[u],f=i[u],h=hi(l),v=hi(f);if(h>1&&v===1)this._updateManyToOne&&this._updateManyToOne(f,l),i[u]=null;else if(h===1&&v>1)this._updateOneToMany&&this._updateOneToMany(f,l),i[u]=null;else if(h===1&&v===1)this._update&&this._update(f,l),i[u]=null;else if(h>1&&v>1)this._updateManyToMany&&this._updateManyToMany(f,l),i[u]=null;else if(h>1)for(var c=0;c<h;c++)this._remove&&this._remove(l[c]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},r.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],a=e[i],o=hi(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[i]=null}},r.prototype._initIndexMap=function(t,e,n,i){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[i](t[o],o);if(a||(n[o]=s),!!e){var u=e[s],l=hi(u);l===0?(e[s]=o,a&&n.push(s)):l===1?e[s]=[u,o]:u.push(o)}}},r}(),yy=Z(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),we="original",Jt="arrayRows",Ee="objectRows",Xe="keyedColumns",Cr="typedArray",my="unknown",ar="column",Jn="row",At={Must:1,Might:2,Not:3},_y=Mt();function jx(r){_y(r).datasetMap=Z()}function Qx(r,t,e){var n={},i=ih(t);if(!i||!r)return n;var a=[],o=[],s=t.ecModel,u=_y(s).datasetMap,l=i.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),C(r,function(g,p){var y=V(g)?g:r[p]={name:g};y.type==="ordinal"&&f==null&&(f=p,h=d(y)),n[y.name]=[]});var v=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});C(r,function(g,p){var y=g.name,m=d(g);if(f==null){var _=v.valueWayDim;c(n[y],_,m),c(o,_,m),v.valueWayDim+=m}else if(f===p)c(n[y],0,m),c(a,0,m);else{var _=v.categoryWayDim;c(n[y],_,m),c(o,_,m),v.categoryWayDim+=m}});function c(g,p,y){for(var m=0;m<y;m++)g.push(p+m)}function d(g){var p=g.dimsDef;return p?p.length:1}return a.length&&(n.itemName=a),o.length&&(n.seriesName=o),n}function PI(r,t,e){var n={},i=ih(r);if(!i)return n;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===Ee||a===Xe)&&C(o,function(f,h){(V(f)?f.name:f)==="name"&&(s=h)});var u=function(){for(var f={},h={},v=[],c=0,d=Math.min(5,e);c<d;c++){var g=wy(t.data,a,t.seriesLayoutBy,o,t.startIndex,c);v.push(g);var p=g===At.Not;if(p&&f.v==null&&c!==s&&(f.v=c),(f.n==null||f.n===f.v||!p&&v[f.n]===At.Not)&&(f.n=c),y(f)&&v[f.n]!==At.Not)return f;p||(g===At.Might&&h.v==null&&c!==s&&(h.v=c),(h.n==null||h.n===h.v)&&(h.n=c))}function y(m){return m.v!=null&&m.n!=null}return y(f)?f:y(h)?h:null}();if(u){n.value=[u.v];var l=s??u.n;n.itemName=[l],n.seriesName=[l]}return n}function ih(r){var t=r.get("data",!0);if(!t)return ba(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},Le).models[0]}function Jx(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:ba(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},Le).models}function Sy(r,t){return wy(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function wy(r,t,e,n,i,a){var o,s=5;if(qt(r))return At.Not;var u,l;if(n){var f=n[a];V(f)?(u=f.name,l=f.type):G(f)&&(u=f)}if(l!=null)return l==="ordinal"?At.Must:At.Not;if(t===Jt){var h=r;if(e===Jn){for(var v=h[a],c=0;c<(v||[]).length&&c<s;c++)if((o=S(v[i+c]))!=null)return o}else for(var c=0;c<h.length&&c<s;c++){var d=h[i+c];if(d&&(o=S(d[a]))!=null)return o}}else if(t===Ee){var g=r;if(!u)return At.Not;for(var c=0;c<g.length&&c<s;c++){var p=g[c];if(p&&(o=S(p[u]))!=null)return o}}else if(t===Xe){var y=r;if(!u)return At.Not;var v=y[u];if(!v||qt(v))return At.Not;for(var c=0;c<v.length&&c<s;c++)if((o=S(v[c]))!=null)return o}else if(t===we)for(var m=r,c=0;c<m.length&&c<s;c++){var p=m[c],_=wa(p);if(!N(_))return At.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=G(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?At.Might:At.Not;if(w&&b!=="-")return At.Must}return At.Not}var Ls=function(){function r(t){this.data=t.data||(t.sourceFormat===Xe?{}:[]),this.sourceFormat=t.sourceFormat||my,this.seriesLayoutBy=t.seriesLayoutBy||ar,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];i.type==null&&Sy(this,n)===At.Must&&(i.type="ordinal")}}return r}();function ah(r){return r instanceof Ls}function ql(r,t,e){e=e||by(r);var n=t.seriesLayoutBy,i=eb(r,e,n,t.sourceHeader,t.dimensions),a=new Ls({data:r,sourceFormat:e,seriesLayoutBy:n,dimensionsDefine:i.dimensionsDefine,startIndex:i.startIndex,dimensionsDetectedCount:i.dimensionsDetectedCount,metaRawOption:et(t)});return a}function xy(r){return new Ls({data:r,sourceFormat:qt(r)?Cr:we})}function tb(r){return new Ls({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:et(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function by(r){var t=my;if(qt(r))t=Cr;else if(N(r)){r.length===0&&(t=Jt);for(var e=0,n=r.length;e<n;e++){var i=r[e];if(i!=null){if(N(i)||qt(i)){t=Jt;break}else if(V(i)){t=Ee;break}}}}else if(V(r)){for(var a in r)if(Dr(r,a)&&Yt(r[a])){t=Xe;break}}return t}function eb(r,t,e,n,i){var a,o;if(!r)return{dimensionsDefine:cv(i),startIndex:o,dimensionsDetectedCount:a};if(t===Jt){var s=r;n==="auto"||n==null?vv(function(l){l!=null&&l!=="-"&&(G(l)?o==null&&(o=1):o=0)},e,s,10):o=wt(n)?n:n?1:0,!i&&o===1&&(i=[],vv(function(l,f){i[f]=l!=null?l+"":""},e,s,1/0)),a=i?i.length:e===Jn?s.length:s[0]?s[0].length:null}else if(t===Ee)i||(i=rb(r));else if(t===Xe)i||(i=[],C(r,function(l,f){i.push(f)}));else if(t===we){var u=wa(r[0]);a=N(u)&&u.length||1}return{startIndex:o,dimensionsDefine:cv(i),dimensionsDetectedCount:a}}function rb(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return yt(e)}function cv(r){if(r){var t=Z();return W(r,function(e,n){e=V(e)?e:{name:e};var i={name:e.name,displayName:e.displayName,type:e.type};if(i.name==null)return i;i.name+="",i.displayName==null&&(i.displayName=i.name);var a=t.get(i.name);return a?i.name+="-"+a.count++:t.set(i.name,{count:1}),i})}}function vv(r,t,e,n){if(t===Jn)for(var i=0;i<e.length&&i<n;i++)r(e[i]?e[i][0]:null,i);else for(var a=e[0]||[],i=0;i<a.length&&i<n;i++)r(a[i],i)}function Ty(r){var t=r.sourceFormat;return t===Ee||t===Xe}var Qr,Jr,tn,dv,pv,Cy=function(){function r(t,e){var n=ah(t)?t:xy(t);this._source=n;var i=this._data=n.data;n.sourceFormat===Cr&&(this._offset=0,this._dimSize=e,this._data=i),pv(this,i,n)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;pv=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,v=u.dimensionsDefine,c=dv[oh(l,f)];if(B(o,c),l===Cr)o.getItem=e,o.count=i,o.fillStorage=n;else{var d=My(l,f);o.getItem=pt(d,null,s,h,v);var g=Dy(l,f);o.count=pt(g,null,s,h,v)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},n=function(o,s,u,l){for(var f=this._data,h=this._dimSize,v=0;v<h;v++){for(var c=l[v],d=c[0]==null?1/0:c[0],g=c[1]==null?-1/0:c[1],p=s-o,y=u[v],m=0;m<p;m++){var _=f[m*h+v];y[o+m]=_,_<d&&(d=_),_>g&&(g=_)}c[0]=d,c[1]=g}},i=function(){return this._data?this._data.length/this._dimSize:0};dv=(t={},t[Jt+"_"+ar]={pure:!0,appendData:a},t[Jt+"_"+Jn]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[Ee]={pure:!0,appendData:a},t[Xe]={pure:!0,appendData:function(o){var s=this._data;C(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[we]={appendData:a},t[Cr]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),gv=function(r,t,e,n){return r[n]},nb=(Qr={},Qr[Jt+"_"+ar]=function(r,t,e,n){return r[n+t]},Qr[Jt+"_"+Jn]=function(r,t,e,n,i){n+=t;for(var a=i||[],o=r,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[n]:null}return a},Qr[Ee]=gv,Qr[Xe]=function(r,t,e,n,i){for(var a=i||[],o=0;o<e.length;o++){var s=e[o].name,u=r[s];a[o]=u?u[n]:null}return a},Qr[we]=gv,Qr);function My(r,t){var e=nb[oh(r,t)];return e}var yv=function(r,t,e){return r.length},ib=(Jr={},Jr[Jt+"_"+ar]=function(r,t,e){return Math.max(0,r.length-t)},Jr[Jt+"_"+Jn]=function(r,t,e){var n=r[0];return n?Math.max(0,n.length-t):0},Jr[Ee]=yv,Jr[Xe]=function(r,t,e){var n=e[0].name,i=r[n];return i?i.length:0},Jr[we]=yv,Jr);function Dy(r,t){var e=ib[oh(r,t)];return e}var Lu=function(r,t,e){return r[t]},ab=(tn={},tn[Jt]=Lu,tn[Ee]=function(r,t,e){return r[e]},tn[Xe]=Lu,tn[we]=function(r,t,e){var n=wa(r);return n instanceof Array?n[t]:n},tn[Cr]=Lu,tn);function Ay(r){var t=ab[r];return t}function oh(r,t){return r===Jt?r+"_"+t:r}function Ko(r,t,e){if(r){var n=r.getRawDataItem(t);if(n!=null){var i=r.getStore(),a=i.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=i.getDimensionProperty(o);return Ay(a)(n,o,s)}else{var u=n;return a===we&&(u=wa(n)),u}}}}var ob=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function sb(r,t){var e={},n=e.encode={},i=Z(),a=[],o=[],s={};C(r.dimensions,function(v){var c=r.getDimensionInfo(v),d=c.coordDim;if(d){var g=c.coordDimIndex;Iu(n,d)[g]=v,c.isExtraCoord||(i.set(d,1),lb(c.type)&&(a[0]=v),Iu(s,d)[g]=r.getDimensionIndex(c.name)),c.defaultTooltip&&o.push(v)}yy.each(function(p,y){var m=Iu(n,y),_=c.otherDims[y];_!=null&&_!==!1&&(m[_]=c.name)})});var u=[],l={};i.each(function(v,c){var d=n[c];l[c]=d[0],u=u.concat(d)}),e.dataDimsOnCoord=u,e.dataDimIndicesOnCoord=W(u,function(v){return r.getDimensionInfo(v).storeDimIndex}),e.encodeFirstDimNotExtra=l;var f=n.label;f&&f.length&&(a=f.slice());var h=n.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),n.defaultedLabel=a,n.defaultedTooltip=o,e.userOutput=new ob(s,t),e}function Iu(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function ub(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function lb(r){return!(r==="ordinal"||r==="time")}var Lo=function(){function r(t){this.otherDims={},t!=null&&B(this,t)}return r}();function Io(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!wt(r)&&r!=null&&r!=="-"&&(r=+me(r)),r==null||r===""?NaN:Number(r))}Z({number:function(r){return parseFloat(r)},time:function(r){return+me(r)},trim:function(r){return G(r)?Ae(r):r}});var fb=function(){function r(t,e){var n=t==="desc";this._resultLT=n?1:-1,e==null&&(e=n?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var n=wt(t)?t:na(t),i=wt(e)?e:na(e),a=isNaN(n),o=isNaN(i);if(a&&(n=this._incomparable),o&&(i=this._incomparable),a&&o){var s=G(t),u=G(e);s&&(n=u?t:0),u&&(i=s?e:0)}return n<i?this._resultLT:n>i?-this._resultLT:0},r}(),Is="undefined",hb=typeof Uint32Array===Is?Array:Uint32Array,cb=typeof Uint16Array===Is?Array:Uint16Array,Ly=typeof Int32Array===Is?Array:Int32Array,mv=typeof Float64Array===Is?Array:Float64Array,Iy={float:mv,int:Ly,ordinal:Array,number:Array,time:mv},Pu;function Ln(r){return r>65535?hb:cb}function In(){return[1/0,-1/0]}function vb(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function _v(r,t,e,n,i){var a=Iy[e||"float"];if(i){var o=r[t],s=o&&o.length;if(s!==n){for(var u=new a(n),l=0;l<s;l++)u[l]=o[l];r[t]=u}}else r[t]=new a(n)}var Xl=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=Z()}return r.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),a=this.defaultDimValueGetter=Pu[i.sourceFormat];this._dimValueGetter=n||a,this._rawExtent=[],Ty(i),this._dimensions=W(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,a=n.get(t);if(a!=null){if(i[a].type===e)return a}else a=i.length;return i[a]={type:e},n.set(t,a),this._chunks[a]=new Iy[e||"float"](this._rawCount),this._rawExtent[a]=In(),a},r.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],a=this._rawExtent,o=i.ordinalOffset||0,s=n.length;o===0&&(a[t]=In());for(var u=a[t],l=o;l<s;l++){var f=n[l]=e.parseAndCollect(n[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}i.ordinalMeta=e,i.ordinalOffset=s,i.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],n=e.ordinalMeta;return n},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},r.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,a=i.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<a;l++){var f=i[l];_v(n,l,f.type,u,!0)}for(var h=[],v=s;v<u;v++)for(var c=v-s,d=0;d<a;d++){var f=i[d],g=Pu.arrayRows.call(this,t[c]||h,f.property,c,d);n[d][v]=g;var p=o[d];g<p[0]&&(p[0]=g),g>p[1]&&(p[1]=g)}return this._rawCount=this._count=u,{start:s,end:u}},r.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=W(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=In()),_v(a,f,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,a,u);else for(var v=[],c=t;c<e;c++){v=i.getItem(c,v);for(var d=0;d<s;d++){var g=a[d],p=this._dimValueGetter(v,l[d],c,d);g[c]=p;var y=u[d];p<y[0]&&(y[0]=p),p>y[1]&&(y[1]=p)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var n=[],i=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)i.push(a)}else i=t;for(var a=0,o=i.length;a<o;a++)n.push(this.get(i[a],e));return n},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],n=0;if(e)for(var i=0,a=this.count();i<a;i++){var o=this.get(t,i);isNaN(o)||(n+=o)}return n},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var n=e.sort(function(a,o){return a-o}),i=this.count();return i===0?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(n!=null&&n<this._count&&n===t)return t;for(var i=0,a=this._count-1;i<=a;){var o=(i+a)/2|0;if(e[o]<t)i=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks,a=i[t],o=[];if(!a)return o;n==null&&(n=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var v=this.getRawIndex(f),c=e-a[v],d=Math.abs(c);d<=n&&((d<s||d===s&&c>=0&&u<0)&&(s=d,u=c,l=0),c===u&&(o[l++]=f))}return o.length=l,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var a=0;a<i;a++)t[a]=e[a]}else t=new n(e.buffer,0,i)}else{var n=Ln(this._rawCount);t=new n(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),a=Ln(n._rawCount),o=new a(i),s=[],u=t.length,l=0,f=t[0],h=n._chunks,v=0;v<i;v++){var c=void 0,d=n.getRawIndex(v);if(u===0)c=e(v);else if(u===1){var g=h[f][d];c=e(g,v)}else{for(var p=0;p<u;p++)s[p]=h[t[p]][d];s[p]=v,c=e.apply(null,s)}c&&(o[l++]=d)}return l<i&&(n._indices=o),n._count=l,n._extent=[],n._updateGetRawIdx(),n},r.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=yt(t),a=i.length;if(!a)return this;var o=e.count(),s=Ln(e._rawCount),u=new s(o),l=0,f=i[0],h=t[f][0],v=t[f][1],c=e._chunks,d=!1;if(!e._indices){var g=0;if(a===1){for(var p=c[i[0]],y=0;y<n;y++){var m=p[y];(m>=h&&m<=v||isNaN(m))&&(u[l++]=g),g++}d=!0}else if(a===2){for(var p=c[i[0]],_=c[i[1]],S=t[i[1]][0],b=t[i[1]][1],y=0;y<n;y++){var m=p[y],w=_[y];(m>=h&&m<=v||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(u[l++]=g),g++}d=!0}}if(!d)if(a===1)for(var y=0;y<o;y++){var T=e.getRawIndex(y),m=c[i[0]][T];(m>=h&&m<=v||isNaN(m))&&(u[l++]=T)}else for(var y=0;y<o;y++){for(var M=!0,T=e.getRawIndex(y),A=0;A<a;A++){var L=i[A],m=c[L][T];(m<t[L][0]||m>t[L][1])&&(M=!1)}M&&(u[l++]=e.getRawIndex(y))}return l<o&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,n){for(var i=t._chunks,a=[],o=e.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<e.length;f++)l[e[f]]=In();for(var h=0;h<s;h++){for(var v=t.getRawIndex(h),c=0;c<o;c++)u[c]=i[e[c]][v];u[o]=h;var d=n&&n.apply(null,u);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var g=e[f],p=d[f],y=l[g],m=i[g];m&&(m[v]=p),p<y[0]&&(y[0]=p),p>y[1]&&(y[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var n=this.clone([t],!0),i=n._chunks,a=i[t],o=this.count(),s=0,u=Math.floor(1/e),l=this.getRawIndex(0),f,h,v,c=new(Ln(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));c[s++]=l;for(var d=1;d<o-1;d+=u){for(var g=Math.min(d+u,o-1),p=Math.min(d+u*2,o),y=(p+g)/2,m=0,_=g;_<p;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=p-g;var w=d,T=Math.min(d+u,o),M=d-1,A=a[l];f=-1,v=w;for(var L=-1,I=0,_=w;_<T;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){I++,L<0&&(L=S);continue}h=Math.abs((M-y)*(b-A)-(M-_)*(m-A)),h>f&&(f=h,v=S)}I>0&&I<T-w&&(c[s++]=Math.min(L,v),v=Math.max(L,v)),c[s++]=v,l=v}return c[s++]=this.getRawIndex(o-1),n._count=s,n._indices=c,n.getRawIndex=this._getRawIdx,n},r.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,a=Math.floor(1/e),o=i[t],s=this.count(),u=new(Ln(this._rawCount))(Math.ceil(s/a)*2),l=0,f=0;f<s;f+=a){var h=f,v=o[this.getRawIndex(h)],c=f,d=o[this.getRawIndex(c)],g=a;f+a>s&&(g=s-f);for(var p=0;p<g;p++){var y=this.getRawIndex(f+p),m=o[y];m<v&&(v=m,h=f+p),m>d&&(d=m,c=f+p)}var _=this.getRawIndex(h),S=this.getRawIndex(c);h<c?(u[l++]=_,u[l++]=S):(u[l++]=S,u[l++]=_)}return n._count=l,n._indices=u,n._updateGetRawIdx(),n},r.prototype.downSample=function(t,e,n,i){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/e),l=o[t],f=this.count(),h=a._rawExtent[t]=In(),v=new(Ln(this._rawCount))(Math.ceil(f/u)),c=0,d=0;d<f;d+=u){u>f-d&&(u=f-d,s.length=u);for(var g=0;g<u;g++){var p=this.getRawIndex(d+g);s[g]=l[p]}var y=n(s),m=this.getRawIndex(Math.min(d+i(s,y)||0,f-1));l[m]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),v[c++]=m}return a._count=c,a._indices=v,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(n){case 0:e(a);break;case 1:e(i[t[0]][s],a);break;case 2:e(i[t[0]][s],i[t[1]][s],a);break;default:for(var u=0,l=[];u<n;u++)l[u]=i[t[u]][s];l[u]=a,e.apply(null,l)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],n=In();if(!e)return n;var i=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=n;for(var s=o[0],u=o[1],l=0;l<i;l++){var f=this.getRawIndex(l),h=e[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,a=0;a<i.length;a++)n.push(i[a][e]);return n},r.prototype.clone=function(t,e){var n=new r,i=this._chunks,a=t&&Ue(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<i.length;o++)n._chunks[o]=a[o]?vb(i[o]):i[o];else n._chunks=i;return this._copyCommonProps(n),e||(n._indices=this._cloneIndices()),n._updateGetRawIdx(),n},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=et(this._extent),t._rawExtent=et(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,n,i,a){return Io(e[a],this._dimensions[a])}Pu={arrayRows:t,objectRows:function(e,n,i,a){return Io(e[n],this._dimensions[a])},keyedColumns:t,original:function(e,n,i,a){var o=e&&(e.value==null?e:e.value);return Io(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,n,i,a){return e[a]}}}(),r}(),db=Mt(),pb={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Py=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=Oy(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return J(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Ty(this.source),n=!ky(t),i="",a=[],o=0,s=0;o<t;o++){var u=void 0,l=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)u=e?h.name:null,l=h.type,f=h.ordinalMeta,s++;else{var v=this.getSourceDimension(o);v&&(u=e?v.name:null,l=v.type)}a.push({property:u,type:l,ordinalMeta:f}),e&&u!=null&&(!h||!h.isCalculationCoord)&&(i+=n?u.replace(/\`/g,"`1").replace(/\$/g,"`2"):u),i+="$",i+=pb[l]||"f",f&&(i+=f.uid),i+="$"}var c=this.source,d=[c.seriesLayoutBy,c.startIndex,i].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,a=this.dimensions[n];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(i=a.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function Ry(r){return r instanceof Py}function Ey(r){for(var t=Z(),e=0;e<(r||[]).length;e++){var n=r[e],i=V(n)?n.name:n;i!=null&&t.get(i)==null&&t.set(i,e)}return t}function Oy(r){var t=db(r);return t.dimNameMap||(t.dimNameMap=Ey(r.dimensionsDefine))}function ky(r){return r>30}var ci=V,cr=W,gb=typeof Int32Array>"u"?Array:Int32Array,yb="e\0\0",Sv=-1,mb=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],_b=["_approximateExtent"],wv,no,vi,di,Ru,pi,Eu,By=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var n,i=!1;Ry(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var a={},o=[],s={},u=!1,l={},f=0;f<n.length;f++){var h=n[f],v=G(h)?new Lo({name:h}):h instanceof Lo?h:new Lo(h),c=v.name;v.type=v.type||"float",v.coordDim||(v.coordDim=c,v.coordDimIndex=0);var d=v.otherDims=v.otherDims||{};o.push(c),a[c]=v,l[c]!=null&&(u=!0),v.createInvertedIndices&&(s[c]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),i&&(v.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(u),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var g=this._dimIdxToName=Z();C(o,function(p){g.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(n!=null)return n;var i=this._schema.getSourceDimension(e);if(i)return i.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(wt(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(n){return e.hasOwnProperty(n)?e[n]:void 0}:function(n){return e[n]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(e==null)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,n=e.encode[t];return(n||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,n){var i=this,a;if(t instanceof Xl&&(a=t),!a){var o=this.dimensions,s=ah(t)||Yt(t)?new Cy(t,o.length):t;a=new Xl;var u=cr(o,function(l){return{type:i._dimInfos[l].type,property:l}});a.initData(s,u,n)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=sb(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e&&e.length),i=n.start,a=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=i;s<a;s++){var u=s-i;this._nameList[s]=e[u],o&&Eu(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==Cr&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store,i=n.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=i.getSource().sourceFormat,u=s===we;if(u&&!i.pure)for(var l=[],f=t;f<e;f++){var h=i.getItem(f,l);if(!this.hasItemOption&&xw(h)&&(this.hasItemOption=!0),h){var v=h.name;a[f]==null&&v!=null&&(a[f]=Pe(v,null));var c=h.id;o[f]==null&&c!=null&&(o[f]=Pe(c,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)Eu(this,f);wv(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){ci(t)?B(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return n==null&&this._nameDimIdx!=null&&(n=vi(this,this._nameDimIdx,e)),n==null&&(n=""),n},r.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},r.prototype.getId=function(t){return no(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var n=this,i=this._store;return N(t)?i.getValues(cr(t,function(a){return n._getStoreDimIndex(a)}),e):i.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],i=n&&n[e];return i==null||isNaN(i)?Sv:i},r.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},r.prototype.each=function(t,e,n){j(t)&&(n=e,e=t,t=[]);var i=n||this,a=cr(di(t),this._getStoreDimIndex,this);this._store.each(a,i?pt(e,i):e)},r.prototype.filterSelf=function(t,e,n){j(t)&&(n=e,e=t,t=[]);var i=n||this,a=cr(di(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,i?pt(e,i):e),this},r.prototype.selectRange=function(t){var e=this,n={},i=yt(t);return C(i,function(a){var o=e._getStoreDimIndex(a);n[o]=t[a]}),this._store=this._store.selectRange(n),this},r.prototype.mapArray=function(t,e,n){j(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n),i},r.prototype.map=function(t,e,n,i){var a=n||i||this,o=cr(di(t),this._getStoreDimIndex,this),s=pi(this);return s._store=this._store.map(o,a?pt(e,a):e),s},r.prototype.modify=function(t,e,n,i){var a=n||i||this,o=cr(di(t),this._getStoreDimIndex,this);this._store.modify(o,a?pt(e,a):e)},r.prototype.downSample=function(t,e,n,i){var a=pi(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),a},r.prototype.minmaxDownSample=function(t,e){var n=pi(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},r.prototype.lttbDownSample=function(t,e){var n=pi(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new gt(n,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new Kx(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(n){return no(t,n)},function(n){return no(e,n)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},ci(t)?B(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return i??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var a=i[e];return a==null&&(a=this.getVisual(e),N(a)?a=a.slice():ci(a)&&(a=B({},a)),i[e]=a),a},r.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,ci(e)?B(i,e):i[e]=n},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){ci(t)?B(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?B(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var n=this.hostModel&&this.hostModel.seriesIndex;Bw(n,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){C(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:cr(this.dimensions,this._getDimInfo,this),this.hostModel)),Ru(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var n=this[t];j(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var i=n.apply(this,arguments);return e.apply(this,[i].concat(ls(arguments)))})},r.internalField=function(){wv=function(t){var e=t._invertedIndicesMap;C(e,function(n,i){var a=t._dimInfos[i],o=a.ordinalMeta,s=t._store;if(o){n=e[i]=new gb(o.categories.length);for(var u=0;u<n.length;u++)n[u]=Sv;for(var u=0;u<s.count();u++)n[s.get(a.storeDimIndex,u)]=u}})},vi=function(t,e,n){return Pe(t._getCategory(e,n),null)},no=function(t,e){var n=t._idList[e];return n==null&&t._idDimIdx!=null&&(n=vi(t,t._idDimIdx,e)),n==null&&(n=yb+e),n},di=function(t){return N(t)||(t=t!=null?[t]:[]),t},pi=function(t){var e=new r(t._schema?t._schema:cr(t.dimensions,t._getDimInfo,t),t.hostModel);return Ru(e,t),e},Ru=function(t,e){C(mb.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods,C(_b,function(n){t[n]=et(e[n])}),t._calculationInfo=B({},e._calculationInfo)},Eu=function(t,e){var n=t._nameList,i=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=n[e],u=i[e];if(s==null&&a!=null&&(n[e]=s=vi(t,a,e)),u==null&&o!=null&&(i[e]=u=vi(t,o,e)),u==null&&s!=null){var l=t._nameRepeatCount,f=l[s]=(l[s]||0)+1;u=s,f>1&&(u+="__ec__"+f),i[e]=u}}}(),r}();function Sb(r,t){return Fy(r,t).dimensions}function Fy(r,t){ah(r)||(r=xy(r)),t=t||{};var e=t.coordDimensions||[],n=t.dimensionsDefine||r.dimensionsDefine||[],i=Z(),a=[],o=xb(r,e,n,t.dimensionsCount),s=t.canOmitUnusedDimensions&&ky(o),u=n===r.dimensionsDefine,l=u?Oy(r):Ey(n),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=Z(f),v=new Ly(o),c=0;c<v.length;c++)v[c]=-1;function d(A){var L=v[A];if(L<0){var I=n[A],E=V(I)?I:{name:I},O=new Lo,R=E.name;R!=null&&l.get(R)!=null&&(O.name=O.displayName=R),E.type!=null&&(O.type=E.type),E.displayName!=null&&(O.displayName=E.displayName);var k=a.length;return v[A]=k,O.storeDimIndex=A,a.push(O),O}return a[L]}if(!s)for(var c=0;c<o;c++)d(c);h.each(function(A,L){var I=Bt(A).slice();if(I.length===1&&!G(I[0])&&I[0]<0){h.set(L,!1);return}var E=h.set(L,[]);C(I,function(O,R){var k=G(O)?l.get(O):O;k!=null&&k<o&&(E[R]=k,p(d(k),L,R))})});var g=0;C(e,function(A){var L,I,E,O;if(G(A))L=A,O={};else{O=A,L=O.name;var R=O.ordinalMeta;O.ordinalMeta=null,O=B({},O),O.ordinalMeta=R,I=O.dimsDef,E=O.otherDims,O.name=O.coordDim=O.coordDimIndex=O.dimsDef=O.otherDims=null}var k=h.get(L);if(k!==!1){if(k=Bt(k),!k.length)for(var F=0;F<(I&&I.length||1);F++){for(;g<o&&d(g).coordDim!=null;)g++;g<o&&k.push(g++)}C(k,function(K,U){var q=d(K);if(u&&O.type!=null&&(q.type=O.type),p(ft(q,O),L,U),q.name==null&&I){var Q=I[U];!V(Q)&&(Q={name:Q}),q.name=q.displayName=Q.name,q.defaultTooltip=Q.defaultTooltip}E&&ft(q.otherDims,E)})}});function p(A,L,I){yy.get(L)!=null?A.otherDims[L]=I:(A.coordDim=L,A.coordDimIndex=I,i.set(L,!0))}var y=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=y?m||1:0;var S=y||"value";function b(A){A.name==null&&(A.name=A.coordDim)}if(s)C(a,function(A){b(A)}),a.sort(function(A,L){return A.storeDimIndex-L.storeDimIndex});else for(var w=0;w<o;w++){var T=d(w),M=T.coordDim;M==null&&(T.coordDim=bb(S,i,_),T.coordDimIndex=0,(!y||m<=0)&&(T.isExtraCoord=!0),m--),b(T),T.type==null&&(Sy(r,w)===At.Must||T.isExtraCoord&&(T.otherDims.itemName!=null||T.otherDims.seriesName!=null))&&(T.type="ordinal")}return wb(a),new Py({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function wb(r){for(var t=Z(),e=0;e<r.length;e++){var n=r[e],i=n.name,a=t.get(i)||0;a>0&&(n.name=i+(a-1)),a++,t.set(i,a)}}function xb(r,t,e,n){var i=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,n||0);return C(t,function(a){var o;V(a)&&(o=a.dimsDef)&&(i=Math.max(i,o.length))}),i}function bb(r,t,e){if(e||t.hasKey(r)){for(var n=0;t.hasKey(r+n);)n++;r+=n}return t.set(r,!0),r}var Ou={},Ps=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var n=[];C(Ou,function(i,a){var o=i.create(t,e);n=n.concat(o||[])}),this._coordinateSystems=n},r.prototype.update=function(t,e){C(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){Ou[t]=e},r.get=function(t){return Ou[t]},r}(),Tb=function(){function r(t){this.coordSysDims=[],this.axisMap=Z(),this.categoryAxisMap=Z(),this.coordSysName=t}return r}();function Cb(r){var t=r.get("coordinateSystem"),e=new Tb(t),n=Mb[t];if(n)return n(r,e,e.axisMap,e.categoryAxisMap),e}var Mb={cartesian2d:function(r,t,e,n){var i=r.getReferringComponents("xAxis",Le).models[0],a=r.getReferringComponents("yAxis",Le).models[0];t.coordSysDims=["x","y"],e.set("x",i),e.set("y",a),Pn(i)&&(n.set("x",i),t.firstCategoryDimIndex=0),Pn(a)&&(n.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,n){var i=r.getReferringComponents("singleAxis",Le).models[0];t.coordSysDims=["single"],e.set("single",i),Pn(i)&&(n.set("single",i),t.firstCategoryDimIndex=0)},polar:function(r,t,e,n){var i=r.getReferringComponents("polar",Le).models[0],a=i.findAxisModel("radiusAxis"),o=i.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),Pn(a)&&(n.set("radius",a),t.firstCategoryDimIndex=0),Pn(o)&&(n.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,n){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,n){var i=r.ecModel,a=i.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();C(a.parallelAxisIndex,function(s,u){var l=i.getComponent("parallelAxis",s),f=o[u];e.set(f,l),Pn(l)&&(n.set(f,l),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=u))})}};function Pn(r){return r.get("type")==="category"}function Ny(r,t,e){e=e||{};var n=e.byIndex,i=e.stackedCoordDimension,a,o,s;Db(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var u=!!(r&&r.get("stack")),l,f,h,v;if(C(a,function(m,_){G(m)&&(a[_]=m={name:m}),u&&!m.isExtraCoord&&(!n&&!l&&m.ordinalMeta&&(l=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!i||i===m.coordDim)&&(f=m))}),f&&!n&&!l&&(n=!0),f){h="__\0ecstackresult_"+r.id,v="__\0ecstackedover_"+r.id,l&&(l.createInvertedIndices=!0);var c=f.coordDim,d=f.type,g=0;C(a,function(m){m.coordDim===c&&g++});var p={name:h,coordDim:c,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},y={name:v,coordDim:v,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(v,d),y.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(y)):(a.push(p),a.push(y))}return{stackedDimension:f&&f.name,stackedByDimension:l&&l.name,isStackedByIndex:n,stackedOverDimension:v,stackResultDimension:h}}function Db(r){return!Ry(r.schema)}function sh(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function zy(r,t){return sh(r,t)?r.getCalculationInfo("stackResultDimension"):t}function Ab(r,t){var e=r.get("coordinateSystem"),n=Ps.get(e),i;return t&&t.coordSysDims&&(i=W(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var u=s.get("type");o.type=ub(u)}return o})),i||(i=n&&(n.getDimensionsInfo?n.getDimensionsInfo():n.dimensions.slice())||["x","y"]),i}function Lb(r,t,e){var n,i;return e&&C(r,function(a,o){var s=a.coordDim,u=e.categoryAxisMap.get(s);u&&(n==null&&(n=o),a.ordinalMeta=u.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(i=!0)}),!i&&n!=null&&(r[n].otherDims.itemName=0),n}function Ib(r,t,e){e=e||{};var n=t.getSourceManager(),i,a=!1;i=n.getSource(),a=i.sourceFormat===we;var o=Cb(t),s=Ab(t,o),u=e.useEncodeDefaulter,l=j(u)?u:u?Ct(Qx,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:l,canOmitUnusedDimensions:!a},h=Fy(i,f),v=Lb(h.dimensions,e.createInvertedIndices,o),c=a?null:n.getSharedDataStore(h),d=Ny(t,{schema:h,store:c}),g=new By(h,t);g.setCalculationInfo(d);var p=v!=null&&Pb(i)?function(y,m,_,S){return S===v?_:this.defaultDimValueGetter(y,m,_,S)}:null;return g.hasItemOption=!1,g.initData(a?i:c,null,p),g}function Pb(r){if(r.sourceFormat===we){var t=Rb(r.data||[]);return!N(wa(t))}}function Rb(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var Eb=Math.round(Math.random()*10);function Rs(r){return[r||"",Eb++].join("_")}function Ob(r){var t={};r.registerSubTypeDefaulter=function(e,n){var i=Ge(e);t[i.main]=n},r.determineSubType=function(e,n){var i=n.type;if(!i){var a=Ge(e).main;r.hasSubTypes(e)&&t[a]&&(i=t[a](n))}return i}}function kb(r,t){r.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=e(o),f=l.graph,h=l.noEntryList,v={};for(C(a,function(m){v[m]=!0});h.length;){var c=h.pop(),d=f[c],g=!!v[c];g&&(s.call(u,c,d.originalDeps.slice()),delete v[c]),C(d.successor,g?y:p)}C(v,function(){var m="";throw new Error(m)});function p(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function y(m){v[m]=!0,p(m)}};function e(a){var o={},s=[];return C(a,function(u){var l=n(o,u),f=l.originalDeps=t(u),h=i(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),C(h,function(v){lt(l.predecessor,v)<0&&l.predecessor.push(v);var c=n(o,v);lt(c.successor,v)<0&&c.successor.push(u)})}),{graph:o,noEntryList:s}}function n(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function i(a,o){var s=[];return C(a,function(u){lt(o,u)>=0&&s.push(u)}),s}}function Bb(r,t){return nt(nt({},r,!0),t,!0)}var Fb=Math.log(2);function $l(r,t,e,n,i,a){var o=n+"-"+i,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~i)/Fb);return r[e][u]}for(var l=n|1<<e,f=e+1;n&1<<f;)f++;for(var h=0,v=0,c=0;v<s;v++){var d=1<<v;d&i||(h+=(c%2?-1:1)*r[e][v]*$l(r,t-1,f,l,i|d,a),c++)}return a[o]=h,h}function xv(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],n={},i=$l(e,8,0,0,0,n);if(i!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*$l(e,7,o===0?1:0,1<<o,1<<s,n)/i*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var bv="___zrEVENTSAVED",ku=[];function Nb(r,t,e,n,i){return Zl(ku,t,n,i,!0)&&Zl(r,e,ku[0],ku[1])}function Zl(r,t,e,n,i){if(t.getBoundingClientRect&&Y.domSupported&&!Hy(t)){var a=t[bv]||(t[bv]={}),o=zb(t,a),s=Hb(o,a,i);if(s)return s(r,e,n),!0}return!1}function zb(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var n=["left","right"],i=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",n[u]+":0",i[l]+":0",n[1-u]+":auto",i[1-l]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function Hb(r,t,e){for(var n=e?"invTrans":"trans",i=t[n],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=r[l].getBoundingClientRect(),h=2*l,v=f.left,c=f.top;o.push(v,c),u=u&&a&&v===a[h]&&c===a[h+1],s.push(r[l].offsetLeft,r[l].offsetTop)}return u&&i?i:(t.srcCoords=o,t[n]=e?xv(s,o):xv(o,s))}function Hy(r){return r.nodeName.toUpperCase()==="CANVAS"}var Gb=/([&<>"'])/g,Vb={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Vt(r){return r==null?"":(r+"").replace(Gb,function(t,e){return Vb[e]})}const Wb={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},Ub={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var jo="ZH",uh="EN",Wn=uh,Po={},lh={},Gy=Y.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||Wn).toUpperCase();return r.indexOf(jo)>-1?jo:Wn}():Wn;function fh(r,t){r=r.toUpperCase(),lh[r]=new gt(t),Po[r]=t}function Yb(r){if(G(r)){var t=Po[r.toUpperCase()]||{};return r===jo||r===uh?et(t):nt(et(t),et(Po[Wn]),!1)}else return nt(et(r),et(Po[Wn]),!1)}function qb(r){return lh[r]}function Xb(){return lh[Wn]}fh(uh,Wb);fh(jo,Ub);var hh=1e3,ch=hh*60,Xi=ch*60,ye=Xi*24,Tv=ye*365,Bi={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},io="{yyyy}-{MM}-{dd}",Cv={year:"{yyyy}",month:"{yyyy}-{MM}",day:io,hour:io+" "+Bi.hour,minute:io+" "+Bi.minute,second:io+" "+Bi.second,millisecond:Bi.none},Bu=["year","month","day","hour","minute","second","millisecond"],Vy=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function zt(r,t){return r+="","0000".substr(0,t-r.length)+r}function Un(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function $b(r){return r===Un(r)}function Zb(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function Ma(r,t,e,n){var i=me(r),a=i[vh(e)](),o=i[Yn(e)]()+1,s=Math.floor((o-1)/3)+1,u=i[Es(e)](),l=i["get"+(e?"UTC":"")+"Day"](),f=i[oa(e)](),h=(f-1)%12+1,v=i[Os(e)](),c=i[ks(e)](),d=i[Bs(e)](),g=f>=12?"pm":"am",p=g.toUpperCase(),y=n instanceof gt?n:qb(n||Gy)||Xb(),m=y.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,g+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,zt(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,zt(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,zt(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,b[l]).replace(/{ee}/g,w[l]).replace(/{e}/g,l+"").replace(/{HH}/g,zt(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,zt(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,zt(v,2)).replace(/{m}/g,v+"").replace(/{ss}/g,zt(c,2)).replace(/{s}/g,c+"").replace(/{SSS}/g,zt(d,3)).replace(/{S}/g,d+"")}function Kb(r,t,e,n,i){var a=null;if(G(e))a=e;else if(j(e))a=e(r.value,t,{level:r.level});else{var o=B({},Bi);if(r.level>0)for(var s=0;s<Bu.length;++s)o[Bu[s]]="{primary|"+o[Bu[s]]+"}";var u=e?e.inherit===!1?e:ft(e,o):o,l=Wy(r.value,i);if(u[l])a=u[l];else if(u.inherit){for(var f=Vy.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(N(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return Ma(new Date(r.value),a,i,n)}function Wy(r,t){var e=me(r),n=e[Yn(t)]()+1,i=e[Es(t)](),a=e[oa(t)](),o=e[Os(t)](),s=e[ks(t)](),u=e[Bs(t)](),l=u===0,f=l&&s===0,h=f&&o===0,v=h&&a===0,c=v&&i===1,d=c&&n===1;return d?"year":c?"month":v?"day":h?"hour":f?"minute":l?"second":"millisecond"}function Mv(r,t,e){var n=wt(r)?me(r):r;switch(t=t||Wy(r,e),t){case"year":return n[vh(e)]();case"half-year":return n[Yn(e)]()>=6?1:0;case"quarter":return Math.floor((n[Yn(e)]()+1)/4);case"month":return n[Yn(e)]();case"day":return n[Es(e)]();case"half-day":return n[oa(e)]()/24;case"hour":return n[oa(e)]();case"minute":return n[Os(e)]();case"second":return n[ks(e)]();case"millisecond":return n[Bs(e)]()}}function vh(r){return r?"getUTCFullYear":"getFullYear"}function Yn(r){return r?"getUTCMonth":"getMonth"}function Es(r){return r?"getUTCDate":"getDate"}function oa(r){return r?"getUTCHours":"getHours"}function Os(r){return r?"getUTCMinutes":"getMinutes"}function ks(r){return r?"getUTCSeconds":"getSeconds"}function Bs(r){return r?"getUTCMilliseconds":"getMilliseconds"}function jb(r){return r?"setUTCFullYear":"setFullYear"}function Uy(r){return r?"setUTCMonth":"setMonth"}function Yy(r){return r?"setUTCDate":"setDate"}function qy(r){return r?"setUTCHours":"setHours"}function Xy(r){return r?"setUTCMinutes":"setMinutes"}function $y(r){return r?"setUTCSeconds":"setSeconds"}function Zy(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Qb(r,t,e,n,i,a,o,s){var u=new Xt({style:{text:r,font:t,align:e,verticalAlign:n,padding:i,rich:a,overflow:o?"truncate":null,lineHeight:s}});return u.getBoundingRect()}function dh(r){if(!Lg(r))return G(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function ph(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,n){return n.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var Da=Of;function Kl(r,t,e){var n="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function i(f){return f&&Ae(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var u=o?me(r):r;if(isNaN(+u)){if(s)return"-"}else return Ma(u,n,e)}if(t==="ordinal")return Fo(r)?i(r):wt(r)&&a(r)?r+"":"-";var l=na(r);return a(l)?dh(l):Fo(r)?i(r):typeof r=="boolean"?r+"":"-"}var Dv=["a","b","c","d","e","f","g"],Fu=function(r,t){return"{"+r+(t??"")+"}"};function gh(r,t,e){N(t)||(t=[t]);var n=t.length;if(!n)return"";for(var i=t[0].$vars||[],a=0;a<i.length;a++){var o=Dv[a];r=r.replace(Fu(o),Fu(o,0))}for(var s=0;s<n;s++)for(var u=0;u<i.length;u++){var l=t[s][i[u]];r=r.replace(Fu(Dv[u],s),e?Vt(l):l)}return r}function Ky(r,t){var e=G(r)?{color:r,extraCssText:t}:r||{},n=e.color,i=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!n)return"";if(a==="html")return i==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Vt(n)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Vt(n)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:i==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}}function Jb(r,t,e){(r==="week"||r==="month"||r==="quarter"||r==="half-year"||r==="year")&&(r=`MM-dd
yyyy`);var n=me(t),i=e?"getUTC":"get",a=n[i+"FullYear"](),o=n[i+"Month"]()+1,s=n[i+"Date"](),u=n[i+"Hours"](),l=n[i+"Minutes"](),f=n[i+"Seconds"](),h=n[i+"Milliseconds"]();return r=r.replace("MM",zt(o,2)).replace("M",o).replace("yyyy",a).replace("yy",zt(a%100+"",2)).replace("dd",zt(s,2)).replace("d",s).replace("hh",zt(u,2)).replace("h",u).replace("mm",zt(l,2)).replace("m",l).replace("ss",zt(f,2)).replace("s",f).replace("SSS",zt(h,3)),r}function tT(r){return r&&r.charAt(0).toUpperCase()+r.substr(1)}function sa(r,t){return t=t||"transparent",G(r)?r:V(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function RI(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var Ro=C,eT=["left","right","top","bottom","width","height"],ao=[["width","left","right"],["height","top","bottom"]];function yh(r,t,e,n,i){var a=0,o=0;n==null&&(n=1/0),i==null&&(i=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),v=h&&h.getBoundingRect(),c,d;if(r==="horizontal"){var g=f.width+(v?-v.x+f.x:0);c=a+g,c>n||u.newline?(a=0,c=g,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(v?-v.y+f.y:0);d=o+p,d>i||u.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),r==="horizontal"?a=c+e:o=d+e)})}var qn=yh;Ct(yh,"vertical");Ct(yh,"horizontal");function yn(r,t,e){e=Da(e||0);var n=t.width,i=t.height,a=Gt(r.left,n),o=Gt(r.top,i),s=Gt(r.right,n),u=Gt(r.bottom,i),l=Gt(r.width,n),f=Gt(r.height,i),h=e[2]+e[0],v=e[1]+e[3],c=r.aspect;switch(isNaN(l)&&(l=n-s-v-a),isNaN(f)&&(f=i-u-h-o),c!=null&&(isNaN(l)&&isNaN(f)&&(c>n/i?l=n*.8:f=i*.8),isNaN(l)&&(l=c*f),isNaN(f)&&(f=l/c)),isNaN(a)&&(a=n-s-l-v),isNaN(o)&&(o=i-u-f-h),r.left||r.right){case"center":a=n/2-l/2-e[3];break;case"right":a=n-l-v;break}switch(r.top||r.bottom){case"middle":case"center":o=i/2-f/2-e[0];break;case"bottom":o=i-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=n-v-a-(s||0)),isNaN(f)&&(f=i-h-o-(u||0));var d=new it(a+e[3],o+e[0],l,f);return d.margin=e,d}function rT(r,t,e,n,i,a){a=a||r,a.x=r.x,a.y=r.y;var o;if(o=r.getBoundingRect(),r.needLocalTransform()){var s=r.getLocalTransform();o=o.clone(),o.applyTransform(s)}var u=yn(ft({width:o.width,height:o.height},t),e,n),l=u.x-o.x,f=u.y-o.y;return a.x+=l,a.y+=f,a===r&&r.markRedraw(),!0}function ua(r){var t=r.layoutMode||r.constructor.layoutMode;return V(t)?t:t?{type:t}:null}function Kn(r,t,e){var n=e&&e.ignoreSize;!N(n)&&(n=[n,n]);var i=o(ao[0],0),a=o(ao[1],1);l(ao[0],r,i),l(ao[1],r,a);function o(f,h){var v={},c=0,d={},g=0,p=2;if(Ro(f,function(_){d[_]=r[_]}),Ro(f,function(_){s(t,_)&&(v[_]=d[_]=t[_]),u(v,_)&&c++,u(d,_)&&g++}),n[h])return u(t,f[1])?d[f[2]]=null:u(t,f[2])&&(d[f[1]]=null),d;if(g===p||!c)return d;if(c>=p)return v;for(var y=0;y<f.length;y++){var m=f[y];if(!s(v,m)&&s(r,m)){v[m]=r[m];break}}return v}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,v){Ro(f,function(c){h[c]=v[c]})}}function Fs(r){return nT({},r)}function nT(r,t){return t&&r&&Ro(eT,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var iT=Mt(),ot=function(r){H(t,r);function t(e,n,i){var a=r.call(this,e,n,i)||this;return a.uid=Rs("ec_cpt_model"),a}return t.prototype.init=function(e,n,i){this.mergeDefaultAndTheme(e,i)},t.prototype.mergeDefaultAndTheme=function(e,n){var i=ua(this),a=i?Fs(e):{},o=n.getTheme();nt(e,o.get(this.mainType)),nt(e,this.getDefaultOption()),i&&Kn(e,a,i)},t.prototype.mergeOption=function(e,n){nt(this.option,e,!0);var i=ua(this);i&&Kn(this.option,e,i)},t.prototype.optionUpdated=function(e,n){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!M1(e))return e.defaultOption;var n=iT(this);if(!n.defaultOption){for(var i=[],a=e;a;){var o=a.prototype.defaultOption;o&&i.push(o),a=a.superClass}for(var s={},u=i.length-1;u>=0;u--)s=nt(s,i[u],!0);n.defaultOption=s}return n.defaultOption},t.prototype.getReferringComponents=function(e,n){var i=e+"Index",a=e+"Id";return ba(this.ecModel,e,{index:this.get(i,!0),id:this.get(a,!0)},n)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(gt);Zp(ot,gt);fs(ot);Ob(ot);kb(ot,aT);function aT(r){var t=[];return C(ot.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=W(t,function(e){return Ge(e).main}),r!=="dataset"&&lt(t,"dataset")<=0&&t.unshift("dataset"),t}var Av=Mt();Mt();var mh=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,n){var i=Bt(this.get("color",!0)),a=this.get("colorLayer",!0);return sT(this,Av,i,a,t,e,n)},r.prototype.clearColorPalette=function(){uT(this,Av)},r}();function oT(r,t){for(var e=r.length,n=0;n<e;n++)if(r[n].length>t)return r[n];return r[e-1]}function sT(r,t,e,n,i,a,o){a=a||r;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(i))return l[i];var f=o==null||!n?e:oT(n,o);if(f=f||e,!(!f||!f.length)){var h=f[u];return i&&(l[i]=h),s.paletteIdx=(u+1)%f.length,h}}function uT(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var lT=/\{@(.+?)\}/g,fT=function(){function r(){}return r.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),a=n.getRawIndex(t),o=n.getName(t),s=n.getRawDataItem(t),u=n.getItemVisual(t,"style"),l=u&&u[n.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,v=h==="series",c=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:v?this.subType:null,seriesIndex:this.seriesIndex,seriesId:v?this.id:null,seriesName:v?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:i,color:l,borderColor:f,dimensionNames:c?c.fullDimensions:null,encode:c?c.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,n,i,a,o){e=e||"normal";var s=this.getData(n),u=this.getDataParams(t,n);if(o&&(u.value=o.interpolatedValue),i!=null&&N(u.value)&&(u.value=u.value[i]),!a){var l=s.getItemModel(t);a=l.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(j(a))return u.status=e,u.dimensionIndex=i,a(u);if(G(a)){var f=gh(a,u);return f.replace(lT,function(h,v){var c=v.length,d=v;d.charAt(0)==="["&&d.charAt(c-1)==="]"&&(d=+d.slice(1,c-1));var g=Ko(s,t,d);if(o&&N(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(g=o.interpolatedValue[p])}return g!=null?g+"":""})}},r.prototype.getRawValue=function(t,e){return Ko(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,n){},r}();function Lv(r){var t,e;return V(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function $i(r){return new hT(r)}var hT=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,n=t&&t.skip;if(this._dirty&&e){var i=this.context;i.data=i.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(n)),this._modBy=u,this._modDataCount=l;var v=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var c=this._dueIndex,d=Math.min(v!=null?this._dueIndex+v:1/0,this._dueEnd);if(!n&&(h||c<d)){var g=this._progress;if(N(g))for(var p=0;p<g.length;p++)this._doProgress(g[p],c,d,u,l);else this._doProgress(g,c,d,u,l)}this._dueIndex=d;var y=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,n,i,a){Iv.reset(e,n,i,a),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Iv.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,n;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(n=e.forceFirstProgress,e=e.progress),N(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Iv=function(){var r,t,e,n,i,a={reset:function(u,l,f,h){t=u,r=l,e=f,n=h,i=Math.ceil(n/e),a.next=e>1&&n>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var u=t%i*e+Math.ceil(t/i),l=t>=r?null:u<n?u:t;return t++,l}}(),cT=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return Io(t,e)},r}();function vT(r,t){var e=new cT,n=r.data,i=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==ar&&jt(o);var s=[],u={},l=r.dimensionsDefine;if(l)C(l,function(g,p){var y=g.name,m={index:p,name:y,displayName:g.displayName};if(s.push(m),y!=null){var _="";Dr(u,y)&&jt(_),u[y]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=My(i,ar);t.__isBuiltIn&&(e.getRawDataItem=function(g){return h(n,a,s,g)},e.getRawData=pt(dT,null,r)),e.cloneRawData=pt(pT,null,r);var v=Dy(i,ar);e.count=pt(v,null,n,a,s);var c=Ay(i);e.retrieveValue=function(g,p){var y=h(n,a,s,g);return d(y,p)};var d=e.retrieveValueFromItem=function(g,p){if(g!=null){var y=s[p];if(y)return c(g,p,y.name)}};return e.getDimensionInfo=pt(gT,null,s,u),e.cloneAllDimensionInfo=pt(yT,null,s),e}function dT(r){var t=r.sourceFormat;if(!_h(t)){var e="";jt(e)}return r.data}function pT(r){var t=r.sourceFormat,e=r.data;if(!_h(t)){var n="";jt(n)}if(t===Jt){for(var i=[],a=0,o=e.length;a<o;a++)i.push(e[a].slice());return i}else if(t===Ee){for(var i=[],a=0,o=e.length;a<o;a++)i.push(B({},e[a]));return i}}function gT(r,t,e){if(e!=null){if(wt(e)||!isNaN(e)&&!Dr(t,e))return r[e];if(Dr(t,e))return t[e]}}function yT(r){return et(r)}var jy=Z();function mT(r){r=et(r);var t=r.type,e="";t||jt(e);var n=t.split(":");n.length!==2&&jt(e);var i=!1;n[0]==="echarts"&&(t=n[1],i=!0),r.__isBuiltIn=i,jy.set(t,r)}function _T(r,t,e){var n=Bt(r),i=n.length,a="";i||jt(a);for(var o=0,s=i;o<s;o++){var u=n[o];t=ST(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function ST(r,t,e,n){var i="";t.length||jt(i),V(r)||jt(i);var a=r.type,o=jy.get(a);o||jt(i);var s=W(t,function(l){return vT(l,o)}),u=Bt(o.transform({upstream:s[0],upstreamList:s,config:et(r.config)}));return W(u,function(l,f){var h="";V(l)||jt(h),l.data||jt(h);var v=by(l.data);_h(v)||jt(h);var c,d=t[0];if(d&&f===0&&!l.dimensions){var g=d.startIndex;g&&(l.data=d.data.slice(0,g).concat(l.data)),c={seriesLayoutBy:ar,sourceHeader:g,dimensions:d.metaRawOption.dimensions}}else c={seriesLayoutBy:ar,sourceHeader:0,dimensions:l.dimensions};return ql(l.data,c,null)})}function _h(r){return r===Jt||r===Ee}var wT=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),n=!!e.length,i,a;if(oo(t)){var o=t,s=void 0,u=void 0,l=void 0;if(n){var f=e[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=qt(s)?Cr:we,a=[];var h=this._getSourceMetaRawOption()||{},v=l&&l.metaRawOption||{},c=J(h.seriesLayoutBy,v.seriesLayoutBy)||null,d=J(h.sourceHeader,v.sourceHeader),g=J(h.dimensions,v.dimensions),p=c!==v.seriesLayoutBy||!!d!=!!v.sourceHeader||g;i=p?[ql(s,{seriesLayoutBy:c,sourceHeader:d,dimensions:g},u)]:[]}else{var y=t;if(n){var m=this._applyTransform(e);i=m.sourceList,a=m.upstreamSignList}else{var _=y.get("source",!0);i=[ql(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(i,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,n=e.get("transform",!0),i=e.get("fromTransformResult",!0);if(i!=null){var a="";t.length!==1&&Pv(a)}var o,s=[],u=[];return C(t,function(l){l.prepareSource();var f=l.getSource(i||0),h="";i!=null&&!f&&Pv(h),s.push(f),u.push(l._getVersionSign())}),n?o=_T(n,s,{datasetIndex:e.componentIndex}):i!=null&&(o=[tb(s[0])]),{sourceList:o,upstreamSignList:u}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,n){var i=0,a=this._storeList,o=a[i];o||(o=a[i]={});var s=o[n];if(!s){var u=this._getUpstreamSourceManagers()[0];oo(this._sourceHost)&&u?s=u._innerGetDataStore(t,e,n):(s=new Xl,s.initData(new Cy(e,t.length),t)),o[n]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(oo(t)){var e=ih(t);return e?[e.getSourceManager()]:[]}else return W(Jx(t),function(n){return n.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,n,i;if(oo(t))e=t.get("seriesLayoutBy",!0),n=t.get("sourceHeader",!0),i=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),n=a.get("sourceHeader",!0),i=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:n,dimensions:i}},r}();function oo(r){return r.mainType==="series"}function Pv(r){throw new Error(r)}var xT="line-height:1";function Qy(r){var t=r.lineHeight;return t==null?xT:"line-height:"+Vt(t+"")+"px"}function Jy(r,t){var e=r.color||"#6e7079",n=r.fontSize||12,i=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Vt(n+"")+"px;color:"+Vt(e)+";font-weight:"+Vt(i+""),valueStyle:"font-size:"+Vt(o+"")+"px;color:"+Vt(a)+";font-weight:"+Vt(s+"")}:{nameStyle:{fontSize:n,fill:e,fontWeight:i},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var bT=[0,10,20,30],TT=["",`
`,`

`,`


`];function la(r,t){return t.type=r,t}function jl(r){return r.type==="section"}function tm(r){return jl(r)?CT:MT}function em(r){if(jl(r)){var t=0,e=r.blocks.length,n=e>1||e>0&&!r.noHeader;return C(r.blocks,function(i){var a=em(i);a>=t&&(t=a+ +(n&&(!a||jl(i)&&!i.noHeader)))}),t}return 0}function CT(r,t,e,n){var i=t.noHeader,a=DT(em(t)),o=[],s=t.blocks||[];Re(!s||N(s)),s=s||[];var u=r.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(Dr(l,u)){var f=new fb(l[u],null);s.sort(function(g,p){return f.evaluate(g.sortParam,p.sortParam)})}else u==="seriesDesc"&&s.reverse()}C(s,function(g,p){var y=t.valueFormatter,m=tm(g)(y?B(B({},r),{valueFormatter:y}):r,g,p>0?a.html:0,n);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):Ql(n,o.join(""),i?e:a.html);if(i)return h;var v=Kl(t.header,"ordinal",r.useUTC),c=Jy(n,r.renderMode).nameStyle,d=Qy(n);return r.renderMode==="richText"?rm(r,v,c)+a.richText+h:Ql(n,'<div style="'+c+";"+d+';">'+Vt(v)+"</div>"+h,e)}function MT(r,t,e,n){var i=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=N(S)?S:[S],W(S,function(b,w){return Kl(b,N(c)?c[w]:c,l)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",i),v=a?"":Kl(u,"ordinal",l),c=t.valueType,d=o?[]:f(t.value,t.dataIndex),g=!s||!a,p=!s&&a,y=Jy(n,i),m=y.nameStyle,_=y.valueStyle;return i==="richText"?(s?"":h)+(a?"":rm(r,v,m))+(o?"":IT(r,d,g,p,_)):Ql(n,(s?"":h)+(a?"":AT(v,!s,m))+(o?"":LT(d,g,p,_)),e)}}function Rv(r,t,e,n,i,a){if(r){var o=tm(r),s={useUTC:i,renderMode:e,orderMode:n,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function DT(r){return{html:bT[r],richText:TT[r]}}function Ql(r,t,e){var n='<div style="clear:both"></div>',i="margin: "+e+"px 0 0",a=Qy(r);return'<div style="'+i+";"+a+';">'+t+n+"</div>"}function AT(r,t,e){var n=t?"margin-left:2px":"";return'<span style="'+e+";"+n+'">'+Vt(r)+"</span>"}function LT(r,t,e,n){var i=e?"10px":"20px",a=t?"float:right;margin-left:"+i:"";return r=N(r)?r:[r],'<span style="'+a+";"+n+'">'+W(r,function(o){return Vt(o)}).join("&nbsp;&nbsp;")+"</span>"}function rm(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function IT(r,t,e,n,i){var a=[i],o=n?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(N(t)?t.join("  "):t,a)}function PT(r,t){var e=r.getData().getItemVisual(t,"style"),n=e[r.visualDrawType];return sa(n)}function nm(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Nu=function(){function r(){this.richTextStyles={},this._nextStyleNameId=Ig()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,n){var i=n==="richText"?this._generateStyleName():null,a=Ky({color:e,type:t,renderMode:n,markerId:i});return G(a)?a:(this.richTextStyles[i]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var n={};N(e)?C(e,function(a){return B(n,a)}):B(n,e);var i=this._generateStyleName();return this.richTextStyles[i]=n,"{"+i+"|"+t+"}"},r}();function RT(r){var t=r.series,e=r.dataIndex,n=r.multipleSeries,i=t.getData(),a=i.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),u=N(s),l=PT(t,e),f,h,v,c;if(o>1||u&&!o){var d=ET(s,t,e,a,l);f=d.inlineValues,h=d.inlineValueTypes,v=d.blocks,c=d.inlineValues[0]}else if(o){var g=i.getDimensionInfo(a[0]);c=f=Ko(i,e,a[0]),h=g.type}else c=f=u?s[0]:s;var p=qf(t),y=p&&t.name||"",m=i.getName(e),_=n?y:m;return la("section",{header:y,noHeader:n||!p,sortParam:c,blocks:[la("nameValue",{markerType:"item",markerColor:l,name:_,noName:!Ae(_),value:f,valueType:h,dataIndex:e})].concat(v||[])})}function ET(r,t,e,n,i){var a=t.getData(),o=Ue(r,function(h,v,c){var d=a.getDimensionInfo(c);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],u=[],l=[];n.length?C(n,function(h){f(Ko(a,e,h),h)}):C(r,f);function f(h,v){var c=a.getDimensionInfo(v);!c||c.otherDims.tooltip===!1||(o?l.push(la("nameValue",{markerType:"subItem",markerColor:i,name:c.displayName,value:h,valueType:c.type})):(s.push(h),u.push(c.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var vr=Mt();function so(r,t){return r.getName(t)||r.getId(t)}var OT="__universalTransitionEnabled",Ye=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=$i({count:BT,reset:FT}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,i);var a=vr(this).sourceManager=new wT(this);a.prepareSource();var o=this.getInitialData(e,i);Ov(o,this),this.dataTask.context.data=o,vr(this).dataBeforeProcessed=o,Ev(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,n){var i=ua(this),a=i?Fs(e):{},o=this.subType;ot.hasClass(o)&&(o+="Series"),nt(e,n.getTheme().get(this.subType)),nt(e,this.getDefaultOption()),Hc(e,"label",["show"]),this.fillDataTextStyle(e.data),i&&Kn(e,a,i)},t.prototype.mergeOption=function(e,n){e=nt(this.option,e,!0),this.fillDataTextStyle(e.data);var i=ua(this);i&&Kn(this.option,e,i);var a=vr(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,n);Ov(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,vr(this).dataBeforeProcessed=o,Ev(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!qt(e))for(var n=["show"],i=0;i<e.length;i++)e[i]&&e[i].label&&Hc(e[i],"label",n)},t.prototype.getInitialData=function(e,n){},t.prototype.appendData=function(e){var n=this.getRawData();n.appendData(e.data)},t.prototype.getData=function(e){var n=Jl(this);if(n){var i=n.context.data;return e==null||!i.getLinkedData?i:i.getLinkedData(e)}else return vr(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var n=Jl(this);if(n){var i=n.context;i.outputData=e,n!==this.dataTask&&(i.data=e)}vr(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return Z(e)},t.prototype.getSourceManager=function(){return vr(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return vr(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,n,i){return RT({series:this,dataIndex:e,multipleSeries:n})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(Y.node&&!(e&&e.ssr))return!1;var n=this.getShallow("animation");return n&&this.getData().count()>this.getShallow("animationThreshold")&&(n=!1),!!n},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,n,i){var a=this.ecModel,o=mh.prototype.getColorFromPalette.call(this,e,n,i);return o||(o=a.getColorFromPalette(e,n,i)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,n){this._innerSelect(this.getData(n),e)},t.prototype.unselect=function(e,n){var i=this.option.selectedMap;if(i){var a=this.option.selectedMode,o=this.getData(n);if(a==="series"||i==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var u=e[s],l=so(o,u);i[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(e,n){for(var i=[],a=0;a<e.length;a++)i[0]=e[a],this.isSelected(e[a],n)?this.unselect(i,n):this.select(i,n)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,n=yt(e),i=[],a=0;a<n.length;a++){var o=e[n[a]];o>=0&&i.push(o)}return i},t.prototype.isSelected=function(e,n){var i=this.option.selectedMap;if(!i)return!1;var a=this.getData(n);return(i==="all"||i[so(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[OT])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,n){var i,a,o=this.option,s=o.selectedMode,u=n.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){V(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=n[f],v=so(e,h);l[v]=!0,this._selectedDataIndicesMap[v]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var c=n[u-1],v=so(e,c);o.selectedMap=(i={},i[v]=!0,i),this._selectedDataIndicesMap=(a={},a[v]=e.getRawIndex(c),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var n=[];e.hasItemOption&&e.each(function(i){var a=e.getRawDataItem(i);a&&a.selected&&n.push(i)}),n.length>0&&this._innerSelect(e,n)}},t.registerClass=function(e){return ot.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(ot);_e(Ye,fT);_e(Ye,mh);Zp(Ye,ot);function Ev(r){var t=r.name;qf(r)||(r.name=kT(r)||t)}function kT(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),n=[];return C(e,function(i){var a=t.getDimensionInfo(i);a.displayName&&n.push(a.displayName)}),n.join(" ")}function BT(r){return r.model.getRawData().count()}function FT(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),NT}function NT(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Ov(r,t){C(qp(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,Ct(zT,t))})}function zT(r,t){var e=Jl(r);return e&&e.setOutputEnd((t||this).count()),t}function Jl(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var n=e.currentTask;if(n){var i=n.agentStubMap;i&&(n=i.get(r.uid))}return n}}var HT=mt.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,n=t.cy,i=t.width/2,a=t.height/2;r.moveTo(e,n-a),r.lineTo(e+i,n+a),r.lineTo(e-i,n+a),r.closePath()}}),GT=mt.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,n=t.cy,i=t.width/2,a=t.height/2;r.moveTo(e,n-a),r.lineTo(e+i,n),r.lineTo(e,n+a),r.lineTo(e-i,n),r.closePath()}}),VT=mt.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,n=t.y,i=t.width/5*3,a=Math.max(i,t.height),o=i/2,s=o*o/(a-o),u=n-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),v=Math.cos(l),c=o*.6,d=o*.7;r.moveTo(e-f,u+s),r.arc(e,u,o,Math.PI-l,Math.PI*2+l),r.bezierCurveTo(e+f-h*c,u+s+v*c,e,n-d,e,n),r.bezierCurveTo(e,n-d,e-f+h*c,u+s+v*c,e-f,u+s),r.closePath()}}),WT=mt.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,n=t.width,i=t.x,a=t.y,o=n/3*2;r.moveTo(i,a),r.lineTo(i+o,a+e),r.lineTo(i,a+e/4*3),r.lineTo(i-o,a+e),r.lineTo(i,a),r.closePath()}}),UT={line:sr,rect:Lt,roundRect:Lt,square:Lt,circle:Ta,diamond:GT,pin:VT,arrow:WT,triangle:HT},YT={line:function(r,t,e,n,i){i.x1=r,i.y1=t+n/2,i.x2=r+e,i.y2=t+n/2},rect:function(r,t,e,n,i){i.x=r,i.y=t,i.width=e,i.height=n},roundRect:function(r,t,e,n,i){i.x=r,i.y=t,i.width=e,i.height=n,i.r=Math.min(e,n)/4},square:function(r,t,e,n,i){var a=Math.min(e,n);i.x=r,i.y=t,i.width=a,i.height=a},circle:function(r,t,e,n,i){i.cx=r+e/2,i.cy=t+n/2,i.r=Math.min(e,n)/2},diamond:function(r,t,e,n,i){i.cx=r+e/2,i.cy=t+n/2,i.width=e,i.height=n},pin:function(r,t,e,n,i){i.x=r+e/2,i.y=t+n/2,i.width=e,i.height=n},arrow:function(r,t,e,n,i){i.x=r+e/2,i.y=t+n/2,i.width=e,i.height=n},triangle:function(r,t,e,n,i){i.cx=r+e/2,i.cy=t+n/2,i.width=e,i.height=n}},tf={};C(UT,function(r,t){tf[t]=new r});var qT=mt.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var n=Jp(r,t,e),i=this.shape;return i&&i.symbolType==="pin"&&t.position==="inside"&&(n.y=e.y+e.height*.4),n},buildPath:function(r,t,e){var n=t.symbolType;if(n!=="none"){var i=tf[n];i||(n="rect",i=tf[n]),YT[n](t.x,t.y,t.width,t.height,i.shape),i.buildPath(r,i.shape,e)}}});function XT(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Ns(r,t,e,n,i,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var u;return r.indexOf("image://")===0?u=th(r.slice(8),new it(t,e,n,i),o?"center":"cover"):r.indexOf("path://")===0?u=Cs(r.slice(7),{},new it(t,e,n,i),o?"center":"cover"):u=new qT({shape:{symbolType:r,x:t,y:e,width:n,height:i}}),u.__isEmptyBrush=s,u.setColor=XT,a&&u.setColor(a),u}function EI(r){return N(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function $T(r,t){if(r!=null)return N(r)||(r=[r,r]),[Gt(r[0],t[0])||0,Gt(J(r[1],r[0]),t[1])||0]}var im=typeof Float32Array<"u",ZT=im?Float32Array:Array;function zu(r){return N(r)?im?new Float32Array(r):r:new ZT(r)}function am(){var r=Mt();return function(t){var e=r(t),n=t.pipelineContext,i=!!e.large,a=!!e.progressiveRender,o=e.large=!!(n&&n.large),s=e.progressiveRender=!!(n&&n.progressiveRender);return(i!==o||a!==s)&&"reset"}}var om=Mt(),KT=am(),We=function(){function r(){this.group=new ae,this.uid=Rs("viewChart"),this.renderTask=$i({plan:jT,reset:QT}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,n,i){},r.prototype.highlight=function(t,e,n,i){var a=t.getData(i&&i.dataType);a&&Bv(a,i,"emphasis")},r.prototype.downplay=function(t,e,n,i){var a=t.getData(i&&i.dataType);a&&Bv(a,i,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},r.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},r.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},r.prototype.eachRendered=function(t){vy(this.group,t)},r.markUpdateMethod=function(t,e){om(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function kv(r,t,e){r&&Gl(r)&&(t==="emphasis"?Fl:Nl)(r,e)}function Bv(r,t,e){var n=xa(r,t),i=t&&t.highlightKey!=null?ex(t.highlightKey):null;n!=null?C(Bt(n),function(a){kv(r.getItemGraphicEl(a),e,i)}):r.eachItemGraphicEl(function(a){kv(a,e,i)})}Bf(We);fs(We);function jT(r){return KT(r.model)}function QT(r){var t=r.model,e=r.ecModel,n=r.api,i=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=i&&om(i).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,e,n,i),JT[u]}var JT={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}},tC="__ec_stack_";function sm(r){return r.get("stack")||tC+r.seriesIndex}function Sh(r){return r.dim+r.index}function um(r,t){var e=[];return t.eachSeriesByType(r,function(n){fm(n)&&e.push(n)}),e}function eC(r){var t={};C(r,function(u){var l=u.coordinateSystem,f=l.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=u.getData(),v=f.dim+"_"+f.index,c=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),g=0,p=d.count();g<p;++g){var y=d.get(c,g);t[v]?t[v].push(y):t[v]=[y]}});var e={};for(var n in t)if(t.hasOwnProperty(n)){var i=t[n];if(i){i.sort(function(u,l){return u-l});for(var a=null,o=1;o<i.length;++o){var s=i[o]-i[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[n]=a}}return e}function lm(r){var t=eC(r),e=[];return C(r,function(n){var i=n.coordinateSystem,a=i.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var u=a.dim+"_"+a.index,l=t[u],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),v=Math.abs(h[1]-h[0]);s=l?f/v*l:f}else{var c=n.getData();s=Math.abs(o[1]-o[0])/c.count()}var d=Gt(n.get("barWidth"),s),g=Gt(n.get("barMaxWidth"),s),p=Gt(n.get("barMinWidth")||(hm(n)?.5:1),s),y=n.get("barGap"),m=n.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:g,barMinWidth:p,barGap:y,barCategoryGap:m,axisKey:Sh(a),stackId:sm(n)})}),rC(e)}function rC(r){var t={};C(r,function(n,i){var a=n.axisKey,o=n.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},u=s.stacks;t[a]=s;var l=n.stackId;u[l]||s.autoWidthCount++,u[l]=u[l]||{width:0,maxWidth:0};var f=n.barWidth;f&&!u[l].width&&(u[l].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=n.barMaxWidth;h&&(u[l].maxWidth=h);var v=n.barMinWidth;v&&(u[l].minWidth=v);var c=n.barGap;c!=null&&(s.gap=c);var d=n.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return C(t,function(n,i){e[i]={};var a=n.stacks,o=n.bandWidth,s=n.categoryGap;if(s==null){var u=yt(a).length;s=Math.max(35-u*4,15)+"%"}var l=Gt(s,o),f=Gt(n.gap,1),h=n.remainedWidth,v=n.autoWidthCount,c=(h-l)/(v+(v-1)*f);c=Math.max(c,0),C(a,function(y){var m=y.maxWidth,_=y.minWidth;if(y.width){var S=y.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),y.width=S,h-=S+f*S,v--}else{var S=c;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==c&&(y.width=S,h-=S+f*S,v--)}}),c=(h-l)/(v+(v-1)*f),c=Math.max(c,0);var d=0,g;C(a,function(y,m){y.width||(y.width=c),g=y,d+=y.width*(1+f)}),g&&(d-=g.width*f);var p=-d/2;C(a,function(y,m){e[i][m]=e[i][m]||{bandWidth:o,offset:p,width:y.width},p+=y.width*(1+f)})}),e}function nC(r,t,e){if(r&&t){var n=r[Sh(t)];return n}}function OI(r,t){var e=um(r,t),n=lm(e);C(e,function(i){var a=i.getData(),o=i.coordinateSystem,s=o.getBaseAxis(),u=sm(i),l=n[Sh(s)][u],f=l.offset,h=l.width;a.setLayout({bandWidth:l.bandWidth,offset:f,size:h})})}function kI(r){return{seriesType:r,plan:am(),reset:function(t){if(fm(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),a=n.getOtherAxis(i),o=e.getDimensionIndex(e.mapDimension(a.dim)),s=e.getDimensionIndex(e.mapDimension(i.dim)),u=t.get("showBackground",!0),l=e.mapDimension(a.dim),f=e.getCalculationInfo("stackResultDimension"),h=sh(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),v=a.isHorizontal(),c=iC(i,a),d=hm(t),g=t.get("barMinHeight")||0,p=f&&e.getDimensionIndex(f),y=e.getLayout("size"),m=e.getLayout("offset");return{progress:function(_,S){for(var b=_.count,w=d&&zu(b*3),T=d&&u&&zu(b*3),M=d&&zu(b),A=n.master.getRect(),L=v?A.width:A.height,I,E=S.getStore(),O=0;(I=_.next())!=null;){var R=E.get(h?p:o,I),k=E.get(s,I),F=c,K=void 0;h&&(K=+R-E.get(o,I));var U=void 0,q=void 0,Q=void 0,ht=void 0;if(v){var st=n.dataToPoint([R,k]);if(h){var $=n.dataToPoint([K,k]);F=$[0]}U=F,q=st[1]+m,Q=st[0]-F,ht=y,Math.abs(Q)<g&&(Q=(Q<0?-1:1)*g)}else{var st=n.dataToPoint([k,R]);if(h){var $=n.dataToPoint([k,K]);F=$[1]}U=st[0]+m,q=F,Q=y,ht=st[1]-F,Math.abs(ht)<g&&(ht=(ht<=0?-1:1)*g)}d?(w[O]=U,w[O+1]=q,w[O+2]=v?Q:ht,T&&(T[O]=v?A.x:U,T[O+1]=v?q:A.y,T[O+2]=L),M[I]=I):S.setItemLayout(I,{x:U,y:q,width:Q,height:ht}),O+=3}d&&S.setLayout({largePoints:w,largeDataIndices:M,largeBackgroundPoints:T,valueAxisHorizontal:v})}}}}}}function fm(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function hm(r){return r.pipelineContext&&r.pipelineContext.large}function iC(r,t){var e=t.model.get("startValue");return e||(e=0),t.toGlobalCoord(t.dataToCoord(t.type==="log"?e>0?e:1:e))}var Qo="\0__throttleOriginMethod",Fv="\0__throttleRate",Nv="\0__throttleType";function wh(r,t,e){var n,i=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(u,l||[])}var v=function(){for(var c=[],d=0;d<arguments.length;d++)c[d]=arguments[d];n=new Date().getTime(),u=this,l=c;var g=f||t,p=f||e;f=null,s=n-(p?i:a)-g,clearTimeout(o),p?o=setTimeout(h,g):s>=0?h():o=setTimeout(h,-s),i=n};return v.clear=function(){o&&(clearTimeout(o),o=null)},v.debounceNextCall=function(c){f=c},v}function cm(r,t,e,n){var i=r[t];if(i){var a=i[Qo]||i,o=i[Nv],s=i[Fv];if(s!==e||o!==n){if(e==null||!n)return r[t]=a;i=r[t]=wh(a,e,n==="debounce"),i[Qo]=a,i[Nv]=n,i[Fv]=e}return i}}function ef(r,t){var e=r[t];e&&e[Qo]&&(e.clear&&e.clear(),r[t]=e[Qo])}function BI(r,t){function e(n,i){var a=[];return n.eachComponent({mainType:"series",subType:r,query:i},function(o){a.push(o.seriesIndex)}),a}C([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(n){t(n[0],function(i,a,o){i=B({},i),o.dispatchAction(B(i,{type:n[1],seriesIndex:e(a,i)}))})})}function Rn(r,t,e,n,i){var a=r+t;e.isSilent(a)||n.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=i.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),v=xa(h,i.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:N(v)?h.getName(v[0]):h.getName(v),selected:G(u)?u:B({},u)})}})}function aC(r,t,e){r.on("selectchanged",function(n){var i=e.getModel();n.isFromClick?(Rn("map","selectchanged",t,i,n),Rn("pie","selectchanged",t,i,n)):n.fromAction==="select"?(Rn("map","selected",t,i,n),Rn("pie","selected",t,i,n)):n.fromAction==="unselect"&&(Rn("map","unselected",t,i,n),Rn("pie","unselected",t,i,n))})}function oC(r){for(var t=[],e=0;e<r.length;e++){var n=r[e];if(!n.defaultAttr.ignore){var i=n.label,a=i.getComputedTransform(),o=i.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,u=i.style.margin||0,l=o.clone();l.applyTransform(a),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var f=s?new Xo(o,a):null;t.push({label:i,labelLine:n.labelLine,rect:l,localRect:o,obb:f,priority:n.priority,defaultAttr:n.defaultAttr,layoutOption:n.computedLayoutOption,axisAligned:s,transform:a})}}return t}function sC(r,t,e,n,i,a){var o=r.length;if(o<2)return;r.sort(function(w,T){return w.rect[t]-T.rect[t]});for(var s=0,u,l=!1,f=0;f<o;f++){var h=r[f],v=h.rect;u=v[t]-s,u<0&&(v[t]-=u,h.label[t]-=u,l=!0),s=v[t]+v[e]}var c=r[0],d=r[o-1],g,p;y(),g<0&&S(-g,.8),p<0&&S(p,.8),y(),m(g,p,1),m(p,g,-1),y(),g<0&&b(-g),p<0&&b(p);function y(){g=c.rect[t]-n,p=i-d.rect[t]-d.rect[e]}function m(w,T,M){if(w<0){var A=Math.min(T,-w);if(A>0){_(A*M,0,o);var L=A+w;L<0&&S(-L*M,1)}else S(-w*M,1)}}function _(w,T,M){w!==0&&(l=!0);for(var A=T;A<M;A++){var L=r[A],I=L.rect;I[t]+=w,L.label[t]+=w}}function S(w,T){for(var M=[],A=0,L=1;L<o;L++){var I=r[L-1].rect,E=Math.max(r[L].rect[t]-I[t]-I[e],0);M.push(E),A+=E}if(A){var O=Math.min(Math.abs(w)/A,T);if(w>0)for(var L=0;L<o-1;L++){var R=M[L]*O;_(R,0,L+1)}else for(var L=o-1;L>0;L--){var R=M[L-1]*O;_(-R,L,o)}}}function b(w){var T=w<0?-1:1;w=Math.abs(w);for(var M=Math.ceil(w/(o-1)),A=0;A<o-1;A++)if(T>0?_(M,0,A+1):_(-M,o-A-1,o),w-=M,w<=0)return}return l}function FI(r,t,e,n){return sC(r,"y","height",t,e)}function uC(r){var t=[];r.sort(function(g,p){return p.priority-g.priority});var e=new it(0,0,0,0);function n(g){if(!g.ignore){var p=g.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}g.ignore=!0}for(var i=0;i<r.length;i++){var a=r[i],o=a.axisAligned,s=a.localRect,u=a.transform,l=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,v=!1,c=0;c<t.length;c++){var d=t[c];if(e.intersect(d.rect)){if(o&&d.axisAligned){v=!0;break}if(d.obb||(d.obb=new Xo(d.localRect,d.transform)),h||(h=new Xo(s,u)),h.intersect(d.obb)){v=!0;break}}}v?(n(l),f&&n(f)):(l.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}var En=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),lC=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new En(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,a=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(a,o,t),this.handler.dispatchToElement(new En(e,t),"drag",t.event);var s=this.handler.findHover(n,i,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new En(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new En(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new En(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new En(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}(),fC=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Hu=[],hC=Y.browser.firefox&&+Y.browser.version.split(".")[0]<39;function rf(r,t,e,n){return e=e||{},n?zv(r,t,e):hC&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):zv(r,t,e),e}function zv(r,t,e){if(Y.domSupported&&r.getBoundingClientRect){var n=t.clientX,i=t.clientY;if(Hy(r)){var a=r.getBoundingClientRect();e.zrX=n-a.left,e.zrY=i-a.top;return}else if(Zl(Hu,r,n,i)){e.zrX=Hu[0],e.zrY=Hu[1];return}}e.zrX=e.zrY=0}function xh(r){return r||window.event}function he(r,t,e){if(t=xh(t),t.zrX!=null)return t;var n=t.type,i=n&&n.indexOf("touch")>=0;if(i){var o=n!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&rf(r,o,t,e)}else{rf(r,t,t,e);var a=cC(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&fC.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function cC(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,n=r.deltaY;if(e==null||n==null)return t;var i=Math.abs(n!==0?n:e),a=n>0?-1:n<0?1:e>0?-1:1;return 3*i*a}function vC(r,t,e,n){r.addEventListener(t,e,n)}function dC(r,t,e,n){r.removeEventListener(t,e,n)}var vm=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0};function NI(r){return r.which===2||r.which===3}var pC=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var a={points:[],touches:[],target:e,event:t},o=0,s=i.length;o<s;o++){var u=i[o],l=rf(n,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Gu)if(Gu.hasOwnProperty(e)){var n=Gu[e](this._track,t);if(n)return n}},r}();function Hv(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function gC(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Gu={pinch:function(r,t){var e=r.length;if(e){var n=(r[e-1]||{}).points,i=(r[e-2]||{}).points||n;if(i&&i.length>1&&n&&n.length>1){var a=Hv(n)/Hv(i);!isFinite(a)&&(a=1),t.pinchScale=a;var o=gC(n);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}},dm="silent";function yC(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:mC}}function mC(){vm(this.event)}var _C=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(qe),gi=function(){function r(t,e){this.x=t,this.y=e}return r}(),SC=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Vu=new it(0,0,0,0),pm=function(r){H(t,r);function t(e,n,i,a,o){var s=r.call(this)||this;return s._hovered=new gi(0,0),s.storage=e,s.painter=n,s.painterRoot=a,s._pointerSize=o,i=i||new _C,s.proxy=null,s.setHandlerProxy(i),s._draggingMgr=new lC(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(C(SC,function(n){e.on&&e.on(n,this[n],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var n=e.zrX,i=e.zrY,a=gm(this,n,i),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new gi(n,i):this.findHover(n,i),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(u,"mousemove",e),l&&l!==s&&this.dispatchToElement(u,"mouseover",e)},t.prototype.mouseout=function(e){var n=e.zrEventControl;n!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),n!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new gi(0,0)},t.prototype.dispatch=function(e,n){var i=this[e];i&&i.call(this,n)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var n=this.proxy;n.setCursor&&n.setCursor(e)},t.prototype.dispatchToElement=function(e,n,i){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+n,s=yC(n,e,i);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(n,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(n,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(n,s)}))}},t.prototype.findHover=function(e,n,i){var a=this.storage.getDisplayList(),o=new gi(e,n);if(Gv(a,o,e,n,i),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new it(e-l,n-l,u,u),h=a.length-1;h>=0;h--){var v=a[h];v!==i&&!v.ignore&&!v.ignoreCoarsePointer&&(!v.parent||!v.parent.ignoreCoarsePointer)&&(Vu.copy(v.getBoundingRect()),v.transform&&Vu.applyTransform(v.transform),Vu.intersect(f)&&s.push(v))}if(s.length)for(var c=4,d=Math.PI/12,g=Math.PI*2,p=0;p<l;p+=c)for(var y=0;y<g;y+=d){var m=e+p*Math.cos(y),_=n+p*Math.sin(y);if(Gv(s,o,m,_,i),o.target)return o}}return o},t.prototype.processGesture=function(e,n){this._gestureMgr||(this._gestureMgr=new pC);var i=this._gestureMgr;n==="start"&&i.clear();var a=i.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(n==="end"&&i.clear(),a){var o=a.type;e.gestureEvent=o;var s=new gi;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(qe);C(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){pm.prototype[r]=function(t){var e=t.zrX,n=t.zrY,i=gm(this,e,n),a,o;if((r!=="mouseup"||!i)&&(a=this.findHover(e,n),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||sg(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function wC(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var n=r,i=void 0,a=!1;n;){if(n.ignoreClip&&(a=!0),!a){var o=n.getClipPath();if(o&&!o.contain(t,e))return!1}n.silent&&(i=!0);var s=n.__hostTarget;n=s||n.parent}return i?dm:!0}return!1}function Gv(r,t,e,n,i){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==i&&!o.ignore&&(s=wC(o,e,n))&&(!t.topTarget&&(t.topTarget=o),s!==dm)){t.target=o;break}}}function gm(r,t,e){var n=r.painter;return t<0||t>n.getWidth()||e<0||e>n.getHeight()}var ym=32,yi=7;function xC(r){for(var t=0;r>=ym;)t|=r&1,r>>=1;return r+t}function Vv(r,t,e,n){var i=t+1;if(i===e)return 1;if(n(r[i++],r[t])<0){for(;i<e&&n(r[i],r[i-1])<0;)i++;bC(r,t,i)}else for(;i<e&&n(r[i],r[i-1])>=0;)i++;return i-t}function bC(r,t,e){for(e--;t<e;){var n=r[t];r[t++]=r[e],r[e--]=n}}function Wv(r,t,e,n,i){for(n===t&&n++;n<e;n++){for(var a=r[n],o=t,s=n,u;o<s;)u=o+s>>>1,i(a,r[u])<0?s=u:o=u+1;var l=n-o;switch(l){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;l>0;)r[o+l]=r[o+l-1],l--}r[o]=a}}function Wu(r,t,e,n,i,a){var o=0,s=0,u=1;if(a(r,t[e+i])>0){for(s=n-i;u<s&&a(r,t[e+i+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=i,u+=i}else{for(s=i+1;u<s&&a(r,t[e+i-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=i-u,u=i-l}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])>0?o=f+1:u=f}return u}function Uu(r,t,e,n,i,a){var o=0,s=0,u=1;if(a(r,t[e+i])<0){for(s=i+1;u<s&&a(r,t[e+i-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=i-u,u=i-l}else{for(s=n-i;u<s&&a(r,t[e+i+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=i,u+=i}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])<0?u=f:o=f+1}return u}function TC(r,t){var e=yi,n,i,a=0,o=[];n=[],i=[];function s(c,d){n[a]=c,i[a]=d,a+=1}function u(){for(;a>1;){var c=a-2;if(c>=1&&i[c-1]<=i[c]+i[c+1]||c>=2&&i[c-2]<=i[c]+i[c-1])i[c-1]<i[c+1]&&c--;else if(i[c]>i[c+1])break;f(c)}}function l(){for(;a>1;){var c=a-2;c>0&&i[c-1]<i[c+1]&&c--,f(c)}}function f(c){var d=n[c],g=i[c],p=n[c+1],y=i[c+1];i[c]=g+y,c===a-3&&(n[c+1]=n[c+2],i[c+1]=i[c+2]),a--;var m=Uu(r[p],r,d,g,0,t);d+=m,g-=m,g!==0&&(y=Wu(r[d+g-1],r,p,y,y-1,t),y!==0&&(g<=y?h(d,g,p,y):v(d,g,p,y)))}function h(c,d,g,p){var y=0;for(y=0;y<d;y++)o[y]=r[c+y];var m=0,_=g,S=c;if(r[S++]=r[_++],--p===0){for(y=0;y<d;y++)r[S+y]=o[m+y];return}if(d===1){for(y=0;y<p;y++)r[S+y]=r[_+y];r[S+p]=o[m];return}for(var b=e,w,T,M;;){w=0,T=0,M=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],T++,w=0,--p===0){M=!0;break}}else if(r[S++]=o[m++],w++,T=0,--d===1){M=!0;break}while((w|T)<b);if(M)break;do{if(w=Uu(r[_],o,m,d,0,t),w!==0){for(y=0;y<w;y++)r[S+y]=o[m+y];if(S+=w,m+=w,d-=w,d<=1){M=!0;break}}if(r[S++]=r[_++],--p===0){M=!0;break}if(T=Wu(o[m],r,_,p,0,t),T!==0){for(y=0;y<T;y++)r[S+y]=r[_+y];if(S+=T,_+=T,p-=T,p===0){M=!0;break}}if(r[S++]=o[m++],--d===1){M=!0;break}b--}while(w>=yi||T>=yi);if(M)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(y=0;y<p;y++)r[S+y]=r[_+y];r[S+p]=o[m]}else{if(d===0)throw new Error;for(y=0;y<d;y++)r[S+y]=o[m+y]}}function v(c,d,g,p){var y=0;for(y=0;y<p;y++)o[y]=r[g+y];var m=c+d-1,_=p-1,S=g+p-1,b=0,w=0;if(r[S--]=r[m--],--d===0){for(b=S-(p-1),y=0;y<p;y++)r[b+y]=o[y];return}if(p===1){for(S-=d,m-=d,w=S+1,b=m+1,y=d-1;y>=0;y--)r[w+y]=r[b+y];r[S]=o[_];return}for(var T=e;;){var M=0,A=0,L=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],M++,A=0,--d===0){L=!0;break}}else if(r[S--]=o[_--],A++,M=0,--p===1){L=!0;break}while((M|A)<T);if(L)break;do{if(M=d-Uu(o[_],r,c,d,d-1,t),M!==0){for(S-=M,m-=M,d-=M,w=S+1,b=m+1,y=M-1;y>=0;y--)r[w+y]=r[b+y];if(d===0){L=!0;break}}if(r[S--]=o[_--],--p===1){L=!0;break}if(A=p-Wu(r[m],o,0,p,p-1,t),A!==0){for(S-=A,_-=A,p-=A,w=S+1,b=_+1,y=0;y<A;y++)r[w+y]=o[b+y];if(p<=1){L=!0;break}}if(r[S--]=r[m--],--d===0){L=!0;break}T--}while(M>=yi||A>=yi);if(L)break;T<0&&(T=0),T+=2}if(e=T,e<1&&(e=1),p===1){for(S-=d,m-=d,w=S+1,b=m+1,y=d-1;y>=0;y--)r[w+y]=r[b+y];r[S]=o[_]}else{if(p===0)throw new Error;for(b=S-(p-1),y=0;y<p;y++)r[b+y]=o[y]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function Eo(r,t,e,n){e||(e=0),n||(n=r.length);var i=n-e;if(!(i<2)){var a=0;if(i<ym){a=Vv(r,e,n,t),Wv(r,e,n,e+a,t);return}var o=TC(r,t),s=xC(i);do{if(a=Vv(r,e,n,t),a<s){var u=i;u>s&&(u=s),Wv(r,e,e+u,e+a,t),a=u}o.pushRun(e,a),o.mergeRuns(),i-=a,e+=a}while(i!==0);o.forceMergeRuns()}}var Uv=!1;function Yu(){Uv||(Uv=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Yv(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var CC=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Yv}return r.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return(t||!n.length)&&this.updateDisplayList(e),n},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,a=e.length;i<a;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Eo(n,Yv)},r.prototype._updateAndAddDisplayable=function(t,e,n){if(!(t.ignore&&!n)){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e?e=e.slice():e=[];for(var a=i,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=re),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Yu(),f.z=0),isNaN(f.z2)&&(Yu(),f.z2=0),isNaN(f.zlevel)&&(Yu(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var v=t.getTextGuideLine();v&&this._updateAndAddDisplayable(v,e,n);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,e,n)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);return}var i=lt(this._roots,t);i>=0&&this._roots.splice(i,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}(),Jo;Jo=Y.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};function Hn(){return new Date().getTime()}var MC=function(r){H(t,r);function t(e){var n=r.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var n=e.getClip();n&&this.addClip(n)},t.prototype.removeClip=function(e){if(e.animation){var n=e.prev,i=e.next;n?n.next=i:this._head=i,i?i.prev=n:this._tail=n,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var n=e.getClip();n&&this.removeClip(n),e.animation=null},t.prototype.update=function(e){for(var n=Hn()-this._pausedTime,i=n-this._time,a=this._head;a;){var o=a.next,s=a.step(n,i);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=n,e||(this.trigger("frame",i),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function n(){e._running&&(Jo(n),!e._paused&&e.update())}Jo(n)},t.prototype.start=function(){this._running||(this._time=Hn(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Hn(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Hn()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var n=e.next;e.prev=e.next=e.animation=null,e=n}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,n){n=n||{},this.start();var i=new Wf(e,n.loop);return this.addAnimator(i),i},t}(qe),DC=300,qu=Y.domSupported,Xu=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=W(r,function(i){var a=i.replace("mouse","pointer");return e.hasOwnProperty(a)?a:i});return{mouse:r,touch:t,pointer:n}}(),qv={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Xv=!1;function nf(r){var t=r.pointerType;return t==="pen"||t==="touch"}function AC(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function $u(r){r&&(r.zrByTouch=!0)}function LC(r,t){return he(r.dom,new IC(r,t),!0)}function mm(r,t){for(var e=t,n=!1;e&&e.nodeType!==9&&!(n=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return n}var IC=function(){function r(t,e){this.stopPropagation=Wt,this.stopImmediatePropagation=Wt,this.preventDefault=Wt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),Me={mousedown:function(r){r=he(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=he(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=he(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=he(this.dom,r);var t=r.toElement||r.relatedTarget;mm(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){Xv=!0,r=he(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){Xv||(r=he(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=he(this.dom,r),$u(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),Me.mousemove.call(this,r),Me.mousedown.call(this,r)},touchmove:function(r){r=he(this.dom,r),$u(r),this.handler.processGesture(r,"change"),Me.mousemove.call(this,r)},touchend:function(r){r=he(this.dom,r),$u(r),this.handler.processGesture(r,"end"),Me.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<DC&&Me.click.call(this,r)},pointerdown:function(r){Me.mousedown.call(this,r)},pointermove:function(r){nf(r)||Me.mousemove.call(this,r)},pointerup:function(r){Me.mouseup.call(this,r)},pointerout:function(r){nf(r)||Me.mouseout.call(this,r)}};C(["click","dblclick","contextmenu"],function(r){Me[r]=function(t){t=he(this.dom,t),this.trigger(r,t)}});var af={pointermove:function(r){nf(r)||af.mousemove.call(this,r)},pointerup:function(r){af.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function PC(r,t){var e=t.domHandlers;Y.pointerEventsSupported?C(Xu.pointer,function(n){Oo(t,n,function(i){e[n].call(r,i)})}):(Y.touchEventsSupported&&C(Xu.touch,function(n){Oo(t,n,function(i){e[n].call(r,i),AC(t)})}),C(Xu.mouse,function(n){Oo(t,n,function(i){i=xh(i),t.touching||e[n].call(r,i)})}))}function RC(r,t){Y.pointerEventsSupported?C(qv.pointer,e):Y.touchEventsSupported||C(qv.mouse,e);function e(n){function i(a){a=xh(a),mm(r,a.target)||(a=LC(r,a),t.domHandlers[n].call(r,a))}Oo(t,n,i,{capture:!0})}}function Oo(r,t,e,n){r.mounted[t]=e,r.listenerOpts[t]=n,vC(r.domTarget,t,e,n)}function Zu(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&dC(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var $v=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),EC=function(r){H(t,r);function t(e,n){var i=r.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new $v(e,Me),qu&&(i._globalHandlerScope=new $v(document,af)),PC(i,i._localHandlerScope),i}return t.prototype.dispose=function(){Zu(this._localHandlerScope),qu&&Zu(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,qu&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var n=this._globalHandlerScope;e?RC(this,n):Zu(n)}},t}(qe);/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var ko={},on={};function OC(r){delete on[r]}function kC(r){if(!r)return!1;if(typeof r=="string")return ea(r,1)<Pl;if(r.colorStops){for(var t=r.colorStops,e=0,n=t.length,i=0;i<n;i++)e+=ea(t[i].color,1);return e/=n,e<Pl}return!1}var BC=function(){function r(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var a=new CC,o=n.renderer||"canvas";ko[o]||(o=yt(ko)[0]),n.useDirtyRect=n.useDirtyRect==null?!1:n.useDirtyRect;var s=new ko[o](e,a,n,t),u=n.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!Y.node&&!Y.worker&&!u?new EC(s.getViewportRoot(),s.root):null,f=n.useCoarsePointer,h=f==null||f==="auto"?Y.touchEventsSupported:!!f,v=44,c;h&&(c=J(n.pointerSize,v)),this.handler=new pm(a,s,l,s.root,c),this.animation=new MC({stage:{update:u?null:function(){return i._flush(!0)}}}),u||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=kC(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,n=Hn();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Hn();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof ae&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,OC(this.id))},r}();function of(r,t){var e=new BC(Rf(),r,t);return on[e.id]=e,e}function FC(r){r.dispose()}function NC(){for(var r in on)on.hasOwnProperty(r)&&on[r].dispose();on={}}function zC(r){return on[r]}function _m(r,t){ko[r]=t}var sf;function HC(r){if(typeof sf=="function")return sf(r)}function Sm(r){sf=r}var GC="5.6.1";const VC=Object.freeze(Object.defineProperty({__proto__:null,dispose:FC,disposeAll:NC,getElementSSRData:HC,getInstance:zC,init:of,registerPainter:_m,registerSSRDataGetter:Sm,version:GC},Symbol.toStringTag,{value:"Module"}));var wm="";typeof navigator<"u"&&(wm=navigator.platform||"");var On="rgba(0, 0, 0, 0.2)";const WC={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:On,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:On,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:On,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:On,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:On,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:On,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:wm.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var uf=Z();function zI(r,t){Re(uf.get(r)==null&&t),uf.set(r,t)}function UC(r,t,e){var n=uf.get(t);if(!n)return e;var i=n(r);return i?e.concat(i):e}var uo,mi,Zv,Kv="\0_ec_inner",YC=1,bh=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,n,i,a,o,s){a=a||{},this.option=null,this._theme=new gt(a),this._locale=new gt(o),this._optionManager=s},t.prototype.setOption=function(e,n,i){var a=Jv(n);this._optionManager.setOption(e,i,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,n){return this._resetOption(e,Jv(n))},t.prototype._resetOption=function(e,n){var i=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?Zv(this,o):(this.restoreData(),this._mergeOption(o,n)),i=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(i=!0,this._mergeOption(s,n))}if(!e||e==="recreate"||e==="media"){var u=a.getMediaOption(this);u.length&&C(u,function(l){i=!0,this._mergeOption(l,n)},this)}return i},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,n){var i=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=Z(),l=n&&n.replaceMergeMainTypeMap;jx(this),C(e,function(h,v){h!=null&&(ot.hasClass(v)?v&&(s.push(v),u.set(v,!0)):i[v]=i[v]==null?et(h):nt(i[v],h,!0))}),l&&l.each(function(h,v){ot.hasClass(v)&&!u.get(v)&&(s.push(v),u.set(v,!0))}),ot.topologicalTravel(s,ot.getAllClassMainTypes(),f,this);function f(h){var v=UC(this,h,Bt(e[h])),c=a.get(h),d=c?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",g=bw(c,v,d);Iw(g,h,ot),i[h]=null,a.set(h,null),o.set(h,0);var p=[],y=[],m=0,_;C(g,function(S,b){var w=S.existing,T=S.newOption;if(!T)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var M=h==="series",A=ot.getClass(h,S.keyInfo.subType,!M);if(!A)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===A)w.name=S.keyInfo.name,w.mergeOption(T,this),w.optionUpdated(T,!1);else{var L=B({componentIndex:b},S.keyInfo);w=new A(T,this,this,L),B(w,L),S.brandNew&&(w.__requireNewView=!0),w.init(T,this,this),w.optionUpdated(null,!0)}}w?(p.push(w.option),y.push(w),m++):(p.push(void 0),y.push(void 0))},this),i[h]=p,a.set(h,y),o.set(h,m),h==="series"&&uo(this)}this._seriesIndices||uo(this)},t.prototype.getOption=function(){var e=et(this.option);return C(e,function(n,i){if(ot.hasClass(i)){for(var a=Bt(n),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!ia(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,e[i]=a}}),delete e[Kv],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,n){var i=this._componentsMap.get(e);if(i){var a=i[n||0];if(a)return a;if(n==null){for(var o=0;o<i.length;o++)if(i[o])return i[o]}}},t.prototype.queryComponents=function(e){var n=e.mainType;if(!n)return[];var i=e.index,a=e.id,o=e.name,s=this._componentsMap.get(n);if(!s||!s.length)return[];var u;return i!=null?(u=[],C(Bt(i),function(l){s[l]&&u.push(s[l])})):a!=null?u=jv("id",a,s):o!=null?u=jv("name",o,s):u=Tt(s,function(l){return!!l}),Qv(u,e)},t.prototype.findComponents=function(e){var n=e.query,i=e.mainType,a=s(n),o=a?this.queryComponents(a):Tt(this._componentsMap.get(i),function(l){return!!l});return u(Qv(o,e));function s(l){var f=i+"Index",h=i+"Id",v=i+"Name";return l&&(l[f]!=null||l[h]!=null||l[v]!=null)?{mainType:i,index:l[f],id:l[h],name:l[v]}:null}function u(l){return e.filter?Tt(l,e.filter):l}},t.prototype.eachComponent=function(e,n,i){var a=this._componentsMap;if(j(e)){var o=n,s=e;a.each(function(h,v){for(var c=0;h&&c<h.length;c++){var d=h[c];d&&s.call(o,v,d,d.componentIndex)}})}else for(var u=G(e)?a.get(e):V(e)?this.findComponents(e):null,l=0;u&&l<u.length;l++){var f=u[l];f&&n.call(i,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var n=Pe(e,null);return Tt(this._componentsMap.get("series"),function(i){return!!i&&n!=null&&i.name===n})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Tt(this._componentsMap.get("series"),function(n){return!!n&&n.subType===e})},t.prototype.getSeries=function(){return Tt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,n){mi(this),C(this._seriesIndices,function(i){var a=this._componentsMap.get("series")[i];e.call(n,a,i)},this)},t.prototype.eachRawSeries=function(e,n){C(this._componentsMap.get("series"),function(i){i&&e.call(n,i,i.componentIndex)})},t.prototype.eachSeriesByType=function(e,n,i){mi(this),C(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&n.call(i,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,n,i){return C(this.getSeriesByType(e),n,i)},t.prototype.isSeriesFiltered=function(e){return mi(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,n){mi(this);var i=[];C(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(n,o,a)&&i.push(a)},this),this._seriesIndices=i,this._seriesIndicesMap=Z(i)},t.prototype.restoreData=function(e){uo(this);var n=this._componentsMap,i=[];n.each(function(a,o){ot.hasClass(o)&&i.push(o)}),ot.topologicalTravel(i,ot.getAllClassMainTypes(),function(a){C(n.get(a),function(o){o&&(a!=="series"||!qC(o,e))&&o.restoreData()})})},t.internalField=function(){uo=function(e){var n=e._seriesIndices=[];C(e._componentsMap.get("series"),function(i){i&&n.push(i.componentIndex)}),e._seriesIndicesMap=Z(n)},mi=function(e){},Zv=function(e,n){e.option={},e.option[Kv]=YC,e._componentsMap=Z({series:[]}),e._componentsCount=Z();var i=n.aria;V(i)&&i.enabled==null&&(i.enabled=!0),XC(n,e._theme.option),nt(n,WC,!1),e._mergeOption(n,null)}}(),t}(gt);function qC(r,t){if(t){var e=t.seriesIndex,n=t.seriesId,i=t.seriesName;return e!=null&&r.componentIndex!==e||n!=null&&r.id!==n||i!=null&&r.name!==i}}function XC(r,t){var e=r.color&&!r.colorLayer;C(t,function(n,i){i==="colorLayer"&&e||ot.hasClass(i)||(typeof n=="object"?r[i]=r[i]?nt(r[i],n,!1):et(n):r[i]==null&&(r[i]=n))})}function jv(r,t,e){if(N(t)){var n=Z();return C(t,function(a){if(a!=null){var o=Pe(a,null);o!=null&&n.set(a,!0)}}),Tt(e,function(a){return a&&n.get(a[r])})}else{var i=Pe(t,null);return Tt(e,function(a){return a&&i!=null&&a[r]===i})}}function Qv(r,t){return t.hasOwnProperty("subType")?Tt(r,function(e){return e&&e.subType===t.subType}):r}function Jv(r){var t=Z();return r&&C(Bt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}_e(bh,mh);var $C=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],xm=function(){function r(t){C($C,function(e){this[e]=pt(t[e],t)},this)}return r}(),ZC=/^(min|max)?(.+)$/,KC=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,n){t&&(C(Bt(t.series),function(o){o&&o.data&&qt(o.data)&&No(o.data)}),C(Bt(t.dataset),function(o){o&&o.source&&qt(o.source)&&No(o.source)})),t=et(t);var i=this._optionBackup,a=jC(t,e,!i);this._newBaseOption=a.baseOption,i?(a.timelineOptions.length&&(i.timelineOptions=a.timelineOptions),a.mediaList.length&&(i.mediaList=a.mediaList),a.mediaDefault&&(i.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],et(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=et(n[i.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!i.length&&!a)return s;for(var u=0,l=i.length;u<l;u++)QC(i[u].query,e,n)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!tM(o,this._currentMediaIndices)&&(s=W(o,function(f){return et(f===-1?a.option:i[f].option)})),this._currentMediaIndices=o,s},r}();function jC(r,t,e){var n=[],i,a,o=r.baseOption,s=r.timeline,u=r.options,l=r.media,f=!!r.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&N(l)&&C(l,function(c){c&&c.option&&(c.query?n.push(c):i||(i=c))}),v(a),C(u,function(c){return v(c)}),C(n,function(c){return v(c.option)});function v(c){C(t,function(d){d(c,e)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:i,mediaList:n}}function QC(r,t,e){var n={width:t,height:e,aspectratio:t/e},i=!0;return C(r,function(a,o){var s=o.match(ZC);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();JC(n[l],a,u)||(i=!1)}}),i}function JC(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function tM(r,t){return r.join(",")===t.join(",")}var be=C,fa=V,td=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ku(r){var t=r&&r.itemStyle;if(t)for(var e=0,n=td.length;e<n;e++){var i=td[e],a=t.normal,o=t.emphasis;a&&a[i]&&(r[i]=r[i]||{},r[i].normal?nt(r[i].normal,a[i]):r[i].normal=a[i],a[i]=null),o&&o[i]&&(r[i]=r[i]||{},r[i].emphasis?nt(r[i].emphasis,o[i]):r[i].emphasis=o[i],o[i]=null)}}function kt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var n=r[t].normal,i=r[t].emphasis;n&&(e?(r[t].normal=r[t].emphasis=null,ft(r[t],n)):r[t]=n),i&&(r.emphasis=r.emphasis||{},r.emphasis[t]=i,i.focus&&(r.emphasis.focus=i.focus),i.blurScope&&(r.emphasis.blurScope=i.blurScope))}}function Fi(r){kt(r,"itemStyle"),kt(r,"lineStyle"),kt(r,"areaStyle"),kt(r,"label"),kt(r,"labelLine"),kt(r,"upperLabel"),kt(r,"edgeLabel")}function bt(r,t){var e=fa(r)&&r[t],n=fa(e)&&e.textStyle;if(n)for(var i=0,a=Gc.length;i<a;i++){var o=Gc[i];n.hasOwnProperty(o)&&(e[o]=n[o])}}function ce(r){r&&(Fi(r),bt(r,"label"),r.emphasis&&bt(r.emphasis,"label"))}function eM(r){if(fa(r)){Ku(r),Fi(r),bt(r,"label"),bt(r,"upperLabel"),bt(r,"edgeLabel"),r.emphasis&&(bt(r.emphasis,"label"),bt(r.emphasis,"upperLabel"),bt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ku(t),ce(t));var e=r.markLine;e&&(Ku(e),ce(e));var n=r.markArea;n&&ce(n);var i=r.data;if(r.type==="graph"){i=i||r.nodes;var a=r.links||r.edges;if(a&&!qt(a))for(var o=0;o<a.length;o++)ce(a[o]);C(r.categories,function(l){Fi(l)})}if(i&&!qt(i))for(var o=0;o<i.length;o++)ce(i[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)ce(s[o]);if(e=r.markLine,e&&e.data)for(var u=e.data,o=0;o<u.length;o++)N(u[o])?(ce(u[o][0]),ce(u[o][1])):ce(u[o]);r.type==="gauge"?(bt(r,"axisLabel"),bt(r,"title"),bt(r,"detail")):r.type==="treemap"?(kt(r.breadcrumb,"itemStyle"),C(r.levels,function(l){Fi(l)})):r.type==="tree"&&Fi(r.leaves)}}function Qe(r){return N(r)?r:r?[r]:[]}function ed(r){return(N(r)?r[0]:r)||{}}function rM(r,t){be(Qe(r.series),function(n){fa(n)&&eM(n)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),be(e,function(n){be(Qe(r[n]),function(i){i&&(bt(i,"axisLabel"),bt(i.axisPointer,"label"))})}),be(Qe(r.parallel),function(n){var i=n&&n.parallelAxisDefault;bt(i,"axisLabel"),bt(i&&i.axisPointer,"label")}),be(Qe(r.calendar),function(n){kt(n,"itemStyle"),bt(n,"dayLabel"),bt(n,"monthLabel"),bt(n,"yearLabel")}),be(Qe(r.radar),function(n){bt(n,"name"),n.name&&n.axisName==null&&(n.axisName=n.name,delete n.name),n.nameGap!=null&&n.axisNameGap==null&&(n.axisNameGap=n.nameGap,delete n.nameGap)}),be(Qe(r.geo),function(n){fa(n)&&(ce(n),be(Qe(n.regions),function(i){ce(i)}))}),be(Qe(r.timeline),function(n){ce(n),kt(n,"label"),kt(n,"itemStyle"),kt(n,"controlStyle",!0);var i=n.data;N(i)&&C(i,function(a){V(a)&&(kt(a,"label"),kt(a,"itemStyle"))})}),be(Qe(r.toolbox),function(n){kt(n,"iconStyle"),be(n.feature,function(i){kt(i,"iconStyle")})}),bt(ed(r.axisPointer),"label"),bt(ed(r.tooltip).axisPointer,"label")}function nM(r,t){for(var e=t.split(","),n=r,i=0;i<e.length&&(n=n&&n[e[i]],n!=null);i++);return n}function iM(r,t,e,n){for(var i=t.split(","),a=r,o,s=0;s<i.length-1;s++)o=i[s],a[o]==null&&(a[o]={}),a=a[o];a[i[s]]==null&&(a[i[s]]=e)}function rd(r){r&&C(aM,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var aM=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],oM=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],ju=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function _i(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<ju.length;e++){var n=ju[e][1],i=ju[e][0];t[n]!=null&&(t[i]=t[n])}}function nd(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function id(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function sM(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function bm(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&bm(r[e].children,t)}function Tm(r,t){rM(r,t),r.series=Bt(r.series),C(r.series,function(e){if(V(e)){var n=e.type;if(n==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(n==="pie"||n==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),nd(e.label);var i=e.data;if(i&&!qt(i))for(var a=0;a<i.length;a++)nd(i[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(n==="gauge"){var o=nM(e,"pointer.color");o!=null&&iM(e,"itemStyle.color",o)}else if(n==="bar"){_i(e),_i(e.backgroundStyle),_i(e.emphasis);var i=e.data;if(i&&!qt(i))for(var a=0;a<i.length;a++)typeof i[a]=="object"&&(_i(i[a]),_i(i[a]&&i[a].emphasis))}else if(n==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),id(e),bm(e.data,id)}else n==="graph"||n==="sankey"?sM(e):n==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&ft(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),rd(e)}}),r.dataRange&&(r.visualMap=r.dataRange),C(oM,function(e){var n=r[e];n&&(N(n)||(n=[n]),C(n,function(i){rd(i)}))})}function uM(r){var t=Z();r.eachSeries(function(e){var n=e.get("stack");if(n){var i=t.get(n)||t.set(n,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;i.length&&a.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}}),t.each(lM)}function lM(r){C(r,function(t,e){var n=[],i=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var v=o.get(t.stackedDimension,h);if(isNaN(v))return i;var c,d;s?d=o.getRawIndex(h):c=o.get(t.stackedByDimension,h);for(var g=NaN,p=e-1;p>=0;p--){var y=r[p];if(s||(d=y.data.rawIndexOf(y.stackedByDimension,c)),d>=0){var m=y.data.getByRawIndex(y.stackResultDimension,d);if(u==="all"||u==="positive"&&m>0||u==="negative"&&m<0||u==="samesign"&&v>=0&&m>0||u==="samesign"&&v<=0&&m<0){v=yw(v,m),g=m;break}}}return n[0]=v,n[1]=g,n})})}var oe=function(){function r(){this.group=new ae,this.uid=Rs("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,n,i){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,n,i){},r.prototype.updateLayout=function(t,e,n,i){},r.prototype.updateVisual=function(t,e,n,i){},r.prototype.toggleBlurSeries=function(t,e,n){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();Bf(oe);fs(oe);var ad=Mt(),od={itemStyle:Qi(gy,!0),lineStyle:Qi(py,!0)},fM={lineStyle:"stroke",itemStyle:"fill"};function Cm(r,t){var e=r.visualStyleMapper||od[t];return e||(console.warn("Unknown style type '"+t+"'."),od.itemStyle)}function Mm(r,t){var e=r.visualDrawType||fM[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var hM={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),a=Cm(r,n),o=a(i),s=i.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var u=Mm(r,n),l=o[u],f=j(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var v=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[u]||(o[u]=v,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||j(o.fill)?v:o.fill,o.stroke=o.stroke==="auto"||j(o.stroke)?v:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",u),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(c,d){var g=r.getDataParams(d),p=B({},o);p[u]=f(g),c.setItemVisual(d,"style",p)}}}},Si=new gt,cM={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=Cm(r,n),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[n]){Si.option=u[n];var l=i(Si),f=o.ensureUniqueItemVisual(s,"style");B(f,l),Si.option.decal&&(o.setItemVisual(s,"decal",Si.option.decal),Si.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},vM={performRawSeries:!0,overallReset:function(r){var t=Z();r.eachSeries(function(e){var n=e.getColorBy();if(!e.isColorBySeries()){var i=e.type+"-"+n,a=t.get(i);a||(a={},t.set(i,a)),ad(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var n=e.getRawData(),i={},a=e.getData(),o=ad(e).scope,s=e.visualStyleAccessPath||"itemStyle",u=Mm(e,s);a.each(function(l){var f=a.getRawIndex(l);i[f]=l}),n.each(function(l){var f=i[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var v=a.ensureUniqueItemVisual(f,"style"),c=n.getName(l)||l+"",d=n.count();v[u]=e.getColorFromPalette(c,o,d)}})}})}},lo=Math.PI;function dM(r,t){t=t||{},ft(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new ae,n=new Lt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(n);var i=new Xt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new Lt({style:{fill:"none"},textContent:i,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new Ca({shape:{startAngle:-lo/2,endAngle:-lo/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:lo*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:lo*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=i.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(r.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),n.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Dm=function(){function r(t,e,n,i){this._stageTaskMap=Z(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(n){var i=n.overallTask;i&&i.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,a=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,o=a?n.step:null,s=i&&i.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData(),a=i.count(),o=n.progressiveEnabled&&e.incrementalPrepareRender&&a>=n.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=n.context={progressiveRender:o,modDataCount:u,large:s}},r.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=Z();t.eachSeries(function(i){var a=i.getProgressive(),o=i.uid;n.set(o,{id:o,head:null,tail:null,threshold:i.getProgressiveThreshold(),progressiveEnabled:a&&!(i.preventIncremental&&i.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(i,i.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;C(this._allHandlers,function(i){var a=t.get(i.uid)||t.set(i.uid,{}),o="";Re(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,a,e,n),i.overallReset&&this._createOverallStageTask(i,a,e,n)},this)},r.prototype.prepareView=function(t,e,n,i){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=n,o.api=i,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},r.prototype._performStageTasks=function(t,e,n,i){i=i||{};var a=!1,o=this;C(t,function(u,l){if(!(i.visualType&&i.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,v=f.overallTask;if(v){var c,d=v.agentStubMap;d.each(function(p){s(i,p)&&(p.dirty(),c=!0)}),c&&v.dirty(),o.updatePayload(v,n);var g=o.getPerformArgs(v,i.block);d.each(function(p){p.perform(g)}),v.perform(g)&&(a=!0)}else h&&h.each(function(p,y){s(i,p)&&p.dirty();var m=o.getPerformArgs(p,i.block);m.skip=!u.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,n),p.perform(m)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(n){e=n.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,n,i){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=Z(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?n.eachRawSeries(f):u?n.eachRawSeriesByType(u,f):l&&l(n,i).each(f);function f(h){var v=h.uid,c=s.set(v,o&&o.get(v)||$i({plan:_M,reset:SM,count:xM}));c.context={model:h,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,c)}},r.prototype._createOverallStageTask=function(t,e,n,i){var a=this,o=e.overallTask=e.overallTask||$i({reset:pM});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=Z(),l=t.seriesType,f=t.getTargetSeries,h=!0,v=!1,c="";Re(!t.createOnAllSeries,c),l?n.eachRawSeriesByType(l,d):f?f(n,i).each(d):(h=!1,C(n.getSeries(),d));function d(g){var p=g.uid,y=u.set(p,s&&s.get(p)||(v=!0,$i({reset:gM,onDirty:mM})));y.context={model:g,overallProgress:h},y.agent=o,y.__block=h,a._pipe(g,y)}v&&o.dirty()},r.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},r.wrapStageHandler=function(t,e){return j(t)&&(t={overallReset:t,seriesType:bM(t)}),t.uid=Rs("stageHandler"),e&&(t.visualType=e),t},r}();function pM(r){r.overallReset(r.ecModel,r.api,r.payload)}function gM(r){return r.overallProgress&&yM}function yM(){this.agent.dirty(),this.getDownstream().dirty()}function mM(){this.agent&&this.agent.dirty()}function _M(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function SM(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Bt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?W(t,function(e,n){return Am(n)}):wM}var wM=Am(0);function Am(r){return function(t,e){var n=e.data,i=e.resetDefines[r];if(i&&i.dataEach)for(var a=t.start;a<t.end;a++)i.dataEach(n,a);else i&&i.progress&&i.progress(t,n)}}function xM(r){return r.data.count()}function bM(r){ts=null;try{r(ha,Lm)}catch{}return ts}var ha={},Lm={},ts;Im(ha,bh);Im(Lm,xm);ha.eachSeriesByType=ha.eachRawSeriesByType=function(r){ts=r};ha.eachComponent=function(r){r.mainType==="series"&&r.subType&&(ts=r.subType)};function Im(r,t){for(var e in t.prototype)r[e]=Wt}var sd=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const TM={color:sd,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],sd]};var Ot="#B9B8CE",ud="#100C2A",fo=function(){return{axisLine:{lineStyle:{color:Ot}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},ld=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Pm={darkMode:!0,color:ld,backgroundColor:ud,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Ot},pageTextStyle:{color:Ot}},textStyle:{color:Ot},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Ot}},dataZoom:{borderColor:"#71708A",textStyle:{color:Ot},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Ot}},timeline:{lineStyle:{color:Ot},label:{color:Ot},controlStyle:{color:Ot,borderColor:Ot}},calendar:{itemStyle:{color:ud},dayLabel:{color:Ot},monthLabel:{color:Ot},yearLabel:{color:Ot}},timeAxis:fo(),logAxis:fo(),valueAxis:fo(),categoryAxis:fo(),line:{symbol:"circle"},graph:{color:ld},gauge:{title:{color:Ot},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Ot},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Pm.categoryAxis.splitLine.show=!1;var CM=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},n={},i={};if(G(t)){var a=Ge(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};C(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var v=o[h],c=l.lastIndexOf(v);if(c>0&&c===l.length-v.length){var d=l.slice(0,c);d!=="data"&&(e.mainType=d,e[v.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(n[l]=u,f=!0),f||(i[l]=u)})}return{cptQuery:e,dataQuery:n,otherQuery:i}},r.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var u=e.cptQuery,l=e.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,i,a));function f(h,v,c,d){return h[c]==null||v[d||c]===h[c]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),lf=["symbol","symbolSize","symbolRotate","symbolOffset"],fd=lf.concat(["symbolKeepAspect"]),MM={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var n={},i={},a=!1,o=0;o<lf.length;o++){var s=lf[o],u=r.get(s);j(u)?(a=!0,i[s]=u):n[s]=u}if(n.symbol=n.symbol||r.defaultSymbol,e.setVisual(B({legendIcon:r.legendIcon||n.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},n)),t.isSeriesFiltered(r))return;var l=yt(i);function f(h,v){for(var c=r.getRawValue(v),d=r.getDataParams(v),g=0;g<l.length;g++){var p=l[g];h.setItemVisual(v,p,i[p](c,d))}}return{dataEach:a?f:null}}},DM={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function n(i,a){for(var o=i.getItemModel(a),s=0;s<fd.length;s++){var u=fd[s],l=o.getShallow(u,!0);l!=null&&i.setItemVisual(a,u,l)}}return{dataEach:e.hasItemOption?n:null}}};function AM(r,t,e){switch(e){case"color":var n=r.getItemVisual(t,"style");return n[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function LM(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function HI(r,t,e,n){switch(e){case"color":var i=r.ensureUniqueItemVisual(t,"style");i[r.getVisual("drawType")]=n,r.setItemVisual(t,"colorFromPalette",!1);break;case"opacity":r.ensureUniqueItemVisual(t,"style").opacity=n;break;case"symbol":case"symbolSize":case"liftZ":r.setItemVisual(t,e,n);break}}function Ni(r,t,e){for(var n;r&&!(t(r)&&(n=r,e));)r=r.__hostTarget||r.parent;return n}var IM=Math.round(Math.random()*9),PM=typeof Object.defineProperty=="function",RM=function(){function r(){this._id="__ec_inner_"+IM++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var n=this._guard(t);return PM?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();function sn(r){return isFinite(r)}function EM(r,t,e){var n=t.x==null?0:t.x,i=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(n=n*e.width+e.x,i=i*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),n=sn(n)?n:0,i=sn(i)?i:1,a=sn(a)?a:0,o=sn(o)?o:0;var s=r.createLinearGradient(n,a,i,o);return s}function OM(r,t,e){var n=e.width,i=e.height,a=Math.min(n,i),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*n+e.x,s=s*i+e.y,u=u*a),o=sn(o)?o:.5,s=sn(s)?s:.5,u=u>=0&&sn(u)?u:.5;var l=r.createRadialGradient(o,s,0,o,s,u);return l}function ff(r,t,e){for(var n=t.type==="radial"?OM(r,t,e):EM(r,t,e),i=t.colorStops,a=0;a<i.length;a++)n.addColorStop(i[a].offset,i[a].color);return n}function kM(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function ho(r){return parseInt(r,10)}function co(r,t,e){var n=["width","height"][t],i=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[n]!=null&&e[n]!=="auto")return parseFloat(e[n]);var s=document.defaultView.getComputedStyle(r);return(r[i]||ho(s[n])||ho(r.style[n]))-(ho(s[a])||0)-(ho(s[o])||0)|0}function BM(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:wt(r)?[r]:N(r)?r:null}function Rm(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&BM(t.lineDash,t.lineWidth),n=t.lineDashOffset;if(e){var i=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;i&&i!==1&&(e=W(e,function(a){return a/i}),n/=i)}return[e,n]}var FM=new Zn(!0);function es(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function hd(r){return typeof r=="string"&&r!=="none"}function rs(r){var t=r.fill;return t!=null&&t!=="none"}function cd(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function vd(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function hf(r,t,e){var n=jp(t.image,t.__image,e);if(hs(n)){var i=r.createPattern(n,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&i&&i.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*Xp),a.scaleSelf(t.scaleX||1,t.scaleY||1),i.setTransform(a)}return i}}function NM(r,t,e,n){var i,a=es(e),o=rs(e),s=e.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||FM,h=t.__dirty;if(!n){var v=e.fill,c=e.stroke,d=o&&!!v.colorStops,g=a&&!!c.colorStops,p=o&&!!v.image,y=a&&!!c.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(d||g)&&(w=t.getBoundingRect()),d&&(m=h?ff(r,v,w):t.__canvasFillGradient,t.__canvasFillGradient=m),g&&(_=h?ff(r,c,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),p&&(S=h||!t.__canvasFillPattern?hf(r,v,t):t.__canvasFillPattern,t.__canvasFillPattern=S),y&&(b=h||!t.__canvasStrokePattern?hf(r,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),d?r.fillStyle=m:p&&(S?r.fillStyle=S:o=!1),g?r.strokeStyle=_:y&&(b?r.strokeStyle=b:a=!1)}var T=t.getGlobalScale();f.setScale(T[0],T[1],t.segmentIgnoreThreshold);var M,A;r.setLineDash&&e.lineDash&&(i=Rm(t),M=i[0],A=i[1]);var L=!0;(l||h&Nn)&&(f.setDPR(r.dpr),u?f.setContext(null):(f.setContext(r),L=!1),f.reset(),t.buildPath(f,t.shape,n),f.toStatic(),t.pathUpdated()),L&&f.rebuildPath(r,u?s:1),M&&(r.setLineDash(M),r.lineDashOffset=A),n||(e.strokeFirst?(a&&vd(r,e),o&&cd(r,e)):(o&&cd(r,e),a&&vd(r,e))),M&&r.setLineDash([])}function zM(r,t,e){var n=t.__image=jp(e.image,t.__image,t,t.onload);if(!(!n||!hs(n))){var i=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),u=n.width/n.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=n.width,s=n.height),e.sWidth&&e.sHeight){var l=e.sx||0,f=e.sy||0;r.drawImage(n,l,f,e.sWidth,e.sHeight,i,a,o,s)}else if(e.sx&&e.sy){var l=e.sx,f=e.sy,h=o-l,v=s-f;r.drawImage(n,l,f,h,v,i,a,o,s)}else r.drawImage(n,i,a,o,s)}}function HM(r,t,e){var n,i=e.text;if(i!=null&&(i+=""),i){r.font=e.font||dn,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(n=Rm(t),a=n[0],o=n[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(es(e)&&r.strokeText(i,e.x,e.y),rs(e)&&r.fillText(i,e.x,e.y)):(rs(e)&&r.fillText(i,e.x,e.y),es(e)&&r.strokeText(i,e.x,e.y)),a&&r.setLineDash([])}}var dd=["shadowBlur","shadowOffsetX","shadowOffsetY"],pd=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Em(r,t,e,n,i){var a=!1;if(!n&&(e=e||{},t===e))return!1;if(n||t.opacity!==e.opacity){Qt(r,i),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?hn.opacity:o}(n||t.blend!==e.blend)&&(a||(Qt(r,i),a=!0),r.globalCompositeOperation=t.blend||hn.blend);for(var s=0;s<dd.length;s++){var u=dd[s];(n||t[u]!==e[u])&&(a||(Qt(r,i),a=!0),r[u]=r.dpr*(t[u]||0))}return(n||t.shadowColor!==e.shadowColor)&&(a||(Qt(r,i),a=!0),r.shadowColor=t.shadowColor||hn.shadowColor),a}function gd(r,t,e,n,i){var a=ca(t,i.inHover),o=n?null:e&&ca(e,i.inHover)||{};if(a===o)return!1;var s=Em(r,a,o,n,i);if((n||a.fill!==o.fill)&&(s||(Qt(r,i),s=!0),hd(a.fill)&&(r.fillStyle=a.fill)),(n||a.stroke!==o.stroke)&&(s||(Qt(r,i),s=!0),hd(a.stroke)&&(r.strokeStyle=a.stroke)),(n||a.opacity!==o.opacity)&&(s||(Qt(r,i),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==l&&(s||(Qt(r,i),s=!0),r.lineWidth=l)}for(var f=0;f<pd.length;f++){var h=pd[f],v=h[0];(n||a[v]!==o[v])&&(s||(Qt(r,i),s=!0),r[v]=a[v]||h[1])}return s}function GM(r,t,e,n,i){return Em(r,ca(t,i.inHover),e&&ca(e,i.inHover),n,i)}function Om(r,t){var e=t.transform,n=r.dpr||1;e?r.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):r.setTransform(n,0,0,n,0,0)}function VM(r,t,e){for(var n=!1,i=0;i<r.length;i++){var a=r[i];n=n||a.isZeroArea(),Om(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=n}function WM(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var yd=1,md=2,_d=3,Sd=4;function UM(r){var t=rs(r),e=es(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function Qt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function ca(r,t){return t&&r.__hoverStyle||r.style}function Th(r,t){un(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function un(r,t,e,n){var i=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~re,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,u=!1;if((!o||kM(a,o))&&(o&&o.length&&(Qt(r,e),r.restore(),u=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(Qt(r,e),r.save(),VM(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=e.prevEl;l||(u=s=!0);var f=t instanceof mt&&t.autoBatch&&UM(t.style);s||WM(i,l.transform)?(Qt(r,e),Om(r,t)):f||Qt(r,e);var h=ca(t,e.inHover);t instanceof mt?(e.lastDrawType!==yd&&(u=!0,e.lastDrawType=yd),gd(r,t,l,u,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),NM(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Yo?(e.lastDrawType!==_d&&(u=!0,e.lastDrawType=_d),gd(r,t,l,u,e),HM(r,t,h)):t instanceof Pr?(e.lastDrawType!==md&&(u=!0,e.lastDrawType=md),GM(r,t,l,u,e),zM(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==Sd&&(u=!0,e.lastDrawType=Sd),YM(r,t,e)),f&&n&&Qt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function YM(r,t,e){var n=t.getDisplayables(),i=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=n.length;o<s;o++){var u=n[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),un(r,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=i.length;l<f;l++){var u=i[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),un(r,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Qu=new RM,wd=new ya(100),xd=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function cf(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),n=t.getZr(),i=n.painter.type==="svg";r.dirty&&Qu.delete(r);var a=Qu.get(r);if(a)return a;var o=ft(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=i?1:1/e,Qu.set(r,s),r.dirty=!1,s;function u(l){for(var f=[e],h=!0,v=0;v<xd.length;++v){var c=o[xd[v]];if(c!=null&&!N(c)&&!G(c)&&!wt(c)&&typeof c!="boolean"){h=!1;break}f.push(c)}var d;if(h){d=f.join(",")+(i?"-svg":"");var g=wd.get(d);g&&(i?l.svgElement=g:l.image=g)}var p=Bm(o.dashArrayX),y=qM(o.dashArrayY),m=km(o.symbol),_=XM(p),S=Fm(y),b=!i&&or.createCanvas(),w=i&&{tag:"g",attrs:{},key:"dcl",children:[]},T=A(),M;b&&(b.width=T.width*e,b.height=T.height*e,M=b.getContext("2d")),L(),h&&wd.put(d,b||w),l.image=b,l.svgElement=w,l.svgWidth=T.width,l.svgHeight=T.height;function A(){for(var I=1,E=0,O=_.length;E<O;++E)I=Nc(I,_[E]);for(var R=1,E=0,O=m.length;E<O;++E)R=Nc(R,m[E].length);I*=R;var k=S*_.length*m.length;return{width:Math.max(1,Math.min(I,o.maxTileWidth)),height:Math.max(1,Math.min(k,o.maxTileHeight))}}function L(){M&&(M.clearRect(0,0,b.width,b.height),o.backgroundColor&&(M.fillStyle=o.backgroundColor,M.fillRect(0,0,b.width,b.height)));for(var I=0,E=0;E<y.length;++E)I+=y[E];if(I<=0)return;for(var O=-S,R=0,k=0,F=0;O<T.height;){if(R%2===0){for(var K=k/2%m.length,U=0,q=0,Q=0;U<T.width*2;){for(var ht=0,E=0;E<p[F].length;++E)ht+=p[F][E];if(ht<=0)break;if(q%2===0){var st=(1-o.symbolSize)*.5,$=U+p[F][q]*st,Ft=O+y[R]*st,Oe=p[F][q]*o.symbolSize,te=y[R]*o.symbolSize,ke=Q/2%m[K].length;se($,Ft,Oe,te,m[K][ke])}U+=p[F][q],++Q,++q,q===p[F].length&&(q=0)}++F,F===p.length&&(F=0)}O+=y[R],++k,++R,R===y.length&&(R=0)}function se(Pt,xt,X,tt,Ze){var Dt=i?1:e,Aa=Ns(Ze,Pt*Dt,xt*Dt,X*Dt,tt*Dt,o.color,o.symbolKeepAspect);if(i){var La=n.painter.renderOneToVNode(Aa);La&&w.children.push(La)}else Th(M,Aa)}}}}function km(r){if(!r||r.length===0)return[["rect"]];if(G(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!G(r[e])){t=!1;break}if(t)return km([r]);for(var n=[],e=0;e<r.length;++e)G(r[e])?n.push([r[e]]):n.push(r[e]);return n}function Bm(r){if(!r||r.length===0)return[[0,0]];if(wt(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,n=0;n<r.length;++n)if(!wt(r[n])){e=!1;break}if(e)return Bm([r]);for(var i=[],n=0;n<r.length;++n)if(wt(r[n])){var t=Math.ceil(r[n]);i.push([t,t])}else{var t=W(r[n],function(s){return Math.ceil(s)});t.length%2===1?i.push(t.concat(t)):i.push(t)}return i}function qM(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(wt(r)){var t=Math.ceil(r);return[t,t]}var e=W(r,function(n){return Math.ceil(n)});return r.length%2?e.concat(e):e}function XM(r){return W(r,function(t){return Fm(t)})}function Fm(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function $M(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var n=e.getData();n.hasItemVisual()&&n.each(function(o){var s=n.getItemVisual(o,"decal");if(s){var u=n.ensureUniqueItemVisual(o,"style");u.decal=cf(s,t)}});var i=n.getVisual("decal");if(i){var a=n.getVisual("style");a.decal=cf(i,t)}}})}var De=new qe,Nm={};function ZM(r,t){Nm[r]=t}function zm(r){return Nm[r]}var KM="5.6.0",jM={zrender:"5.6.1"},QM=1,JM=800,tD=900,eD=1e3,rD=2e3,nD=5e3,Hm=1e3,iD=1100,Ch=2e3,Gm=3e3,aD=4e3,zs=4500,oD=4600,sD=5e3,uD=6e3,Vm=7e3,Wm={PROCESSOR:{FILTER:eD,SERIES_FILTER:JM,STATISTIC:nD},VISUAL:{LAYOUT:Hm,PROGRESSIVE_LAYOUT:iD,GLOBAL:Ch,CHART:Gm,POST_CHART_LAYOUT:oD,COMPONENT:aD,BRUSH:sD,CHART_ITEM:zs,ARIA:uD,DECAL:Vm}},Et="__flagInMainProcess",Zt="__pendingUpdate",Ju="__needsUpdateStatus",bd=/^[a-zA-Z0-9_]+$/,tl="__connectUpdateStatus",Td=0,lD=1,fD=2;function Um(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return qm(this,r,t)}}function Ym(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return qm(this,r,t)}}function qm(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),qe.prototype[t].apply(r,e)}var Xm=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(qe),$m=Xm.prototype;$m.on=Ym("on");$m.off=Ym("off");var kn,el,vo,dr,rl,nl,il,wi,xi,Cd,Md,al,Dd,po,Ad,Zm,ue,Ld,ns=function(r){H(t,r);function t(e,n,i){var a=r.call(this,new CM)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],i=i||{},G(n)&&(n=Km[n]),a._dom=e;var o="canvas",s="auto",u=!1;i.ssr&&Sm(function(v){var c=dt(v),d=c.dataIndex;if(d!=null){var g=Z();return g.set("series_index",c.seriesIndex),g.set("data_index",d),c.ssrType&&g.set("ssr_type",c.ssrType),g}});var l=a._zr=of(e,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:J(i.useDirtyRect,u),useCoarsePointer:J(i.useCoarsePointer,s),pointerSize:i.pointerSize});a._ssr=i.ssr,a._throttledZrFlush=wh(pt(l.flush,l),17),n=et(n),n&&Tm(n,!0),a._theme=n,a._locale=Yb(i.locale||Gy),a._coordSysMgr=new Ps;var f=a._api=Ad(a);function h(v,c){return v.__prio-c.__prio}return Eo(as,h),Eo(vf,h),a._scheduler=new Dm(a,f,vf,as),a._messageCenter=new Xm,a._initEvents(),a.resize=pt(a.resize,a),l.animation.on("frame",a._onframe,a),Cd(l,a),Md(l,a),No(a),a}return t.prototype._onframe=function(){if(!this._disposed){Ld(this);var e=this._scheduler;if(this[Zt]){var n=this[Zt].silent;this[Et]=!0;try{kn(this),dr.update.call(this,null,this[Zt].updateParams)}catch(u){throw this[Et]=!1,this[Zt]=null,u}this._zr.flush(),this[Et]=!1,this[Zt]=null,wi.call(this,n),xi.call(this,n)}else if(e.unfinished){var i=QM,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),nl(this,a),e.performVisualTasks(a),po(this,this._model,o,"remain",{}),i-=+new Date-s}while(i>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,n,i){if(!this[Et]){if(this._disposed){this.id;return}var a,o,s;if(V(n)&&(i=n.lazyUpdate,a=n.silent,o=n.replaceMerge,s=n.transition,n=n.notMerge),this[Et]=!0,!this._model||n){var u=new KC(this._api),l=this._theme,f=this._model=new bh;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(e,{replaceMerge:o},df);var h={seriesTransition:s,optionChanged:!0};if(i)this[Zt]={silent:a,updateParams:h},this[Et]=!1,this.getZr().wakeUp();else{try{kn(this),dr.update.call(this,null,h)}catch(v){throw this[Zt]=null,this[Et]=!1,v}this._ssr||this._zr.flush(),this[Zt]=null,this[Et]=!1,wi.call(this,a),xi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Y.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var n=this._zr.painter;return n.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var n=this._zr.painter;return n.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(Y.svgSupported){var e=this._zr,n=e.storage.getDisplayList();return C(n,function(i){i.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var n=e.excludeComponents,i=this._model,a=[],o=this;C(n,function(u){i.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return C(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var n=e.type==="svg",i=this.group,a=Math.min,o=Math.max,s=1/0;if(os[i]){var u=s,l=s,f=-s,h=-s,v=[],c=e&&e.pixelRatio||this.getDevicePixelRatio();C(vn,function(_,S){if(_.group===i){var b=n?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(et(e)),w=_.getDom().getBoundingClientRect();u=a(w.left,u),l=a(w.top,l),f=o(w.right,f),h=o(w.bottom,h),v.push({dom:b,left:w.left,top:w.top})}}),u*=c,l*=c,f*=c,h*=c;var d=f-u,g=h-l,p=or.createCanvas(),y=of(p,{renderer:n?"svg":"canvas"});if(y.resize({width:d,height:g}),n){var m="";return C(v,function(_){var S=_.left-u,b=_.top-l;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),y.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&y.painter.setBackgroundColor(e.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}else return e.connectedBackgroundColor&&y.add(new Lt({shape:{x:0,y:0,width:d,height:g},style:{fill:e.connectedBackgroundColor}})),C(v,function(_){var S=new Pr({style:{x:_.left*c-u,y:_.top*c-l,image:_.dom}});y.add(S)}),y.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,n){return rl(this,"convertToPixel",e,n)},t.prototype.convertFromPixel=function(e,n){return rl(this,"convertFromPixel",e,n)},t.prototype.containPixel=function(e,n){if(this._disposed){this.id;return}var i=this._model,a,o=xu(i,e);return C(o,function(s,u){u.indexOf("Models")>=0&&C(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(n);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(n,l))}},this)},this),!!a},t.prototype.getVisual=function(e,n){var i=this._model,a=xu(i,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?AM(s,u,n):LM(s,n)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;C(hD,function(n){var i=function(a){var o=e.getModel(),s=a.target,u,l=n==="globalout";if(l?u={}:s&&Ni(s,function(d){var g=dt(d);if(g&&g.dataIndex!=null){var p=g.dataModel||o.getSeriesByIndex(g.seriesIndex);return u=p&&p.getDataParams(g.dataIndex,g.dataType,s)||{},!0}else if(g.eventData)return u=B({},g.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var v=f&&h!=null&&o.getComponent(f,h),c=v&&e[v.mainType==="series"?"_chartsMap":"_componentsMap"][v.__viewId];u.event=a,u.type=n,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:v,view:c},e.trigger(n,u)}};i.zrEventfulCallAtLast=!0,e._zr.on(n,i,e)}),C(Zi,function(n,i){e._messageCenter.on(i,function(a){this.trigger(i,a)},e)}),C(["selectchanged"],function(n){e._messageCenter.on(n,function(i){this.trigger(n,i)},e)}),aC(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&kg(this.getDom(),Dh,"");var n=this,i=n._api,a=n._model;C(n._componentsViews,function(o){o.dispose(a,i)}),C(n._chartsViews,function(o){o.dispose(a,i)}),n._zr.dispose(),n._dom=n._model=n._chartsMap=n._componentsMap=n._chartsViews=n._componentsViews=n._scheduler=n._api=n._zr=n._throttledZrFlush=n._theme=n._coordSysMgr=n._messageCenter=null,delete vn[n.id]},t.prototype.resize=function(e){if(!this[Et]){if(this._disposed){this.id;return}this._zr.resize(e);var n=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!n){var i=n.resetOption("media"),a=e&&e.silent;this[Zt]&&(a==null&&(a=this[Zt].silent),i=!0,this[Zt]=null),this[Et]=!0;try{i&&kn(this),dr.update.call(this,{type:"resize",animation:B({duration:0},e&&e.animation)})}catch(o){throw this[Et]=!1,o}this[Et]=!1,wi.call(this,a),xi.call(this,a)}}},t.prototype.showLoading=function(e,n){if(this._disposed){this.id;return}if(V(e)&&(n=e,e=""),e=e||"default",this.hideLoading(),!!pf[e]){var i=pf[e](this._api,n),a=this._zr;this._loadingFX=i,a.add(i)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var n=B({},e);return n.type=Zi[e.type],n},t.prototype.dispatchAction=function(e,n){if(this._disposed){this.id;return}if(V(n)||(n={silent:!!n}),!!is[e.type]&&this._model){if(this[Et]){this._pendingActions.push(e);return}var i=n.silent;il.call(this,e,i);var a=n.flush;a?this._zr.flush():a!==!1&&Y.browser.weChat&&this._throttledZrFlush(),wi.call(this,i),xi.call(this,i)}},t.prototype.updateLabelLayout=function(){De.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var n=e.seriesIndex,i=this.getModel(),a=i.getSeriesByIndex(n);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){kn=function(h){var v=h._scheduler;v.restorePipelines(h._model),v.prepareStageTasks(),el(h,!0),el(h,!1),v.plan()},el=function(h,v){for(var c=h._model,d=h._scheduler,g=v?h._componentsViews:h._chartsViews,p=v?h._componentsMap:h._chartsMap,y=h._zr,m=h._api,_=0;_<g.length;_++)g[_].__alive=!1;v?c.eachComponent(function(w,T){w!=="series"&&S(T)}):c.eachSeries(S);function S(w){var T=w.__requireNewView;w.__requireNewView=!1;var M="_ec_"+w.id+"_"+w.type,A=!T&&p[M];if(!A){var L=Ge(w.type),I=v?oe.getClass(L.main,L.sub):We.getClass(L.sub);A=new I,A.init(c,m),p[M]=A,g.push(A),y.add(A.group)}w.__viewId=A.__id=M,A.__alive=!0,A.__model=w,A.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!v&&d.prepareView(A,w,c,m)}for(var _=0;_<g.length;){var b=g[_];b.__alive?_++:(!v&&b.renderTask.dispose(),y.remove(b.group),b.dispose(c,m),g.splice(_,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},vo=function(h,v,c,d,g){var p=h._model;if(p.setUpdatePayload(c),!d){C([].concat(h._componentsViews).concat(h._chartsViews),b);return}var y={};y[d+"Id"]=c[d+"Id"],y[d+"Index"]=c[d+"Index"],y[d+"Name"]=c[d+"Name"];var m={mainType:d,query:y};g&&(m.subType=g);var _=c.excludeSeriesId,S;_!=null&&(S=Z(),C(Bt(_),function(w){var T=Pe(w,null);T!=null&&S.set(T,!0)})),p&&p.eachComponent(m,function(w){var T=S&&S.get(w.id)!=null;if(!T)if(Kc(c))if(w instanceof Ye)c.type===cn&&!c.notBlur&&!w.get(["emphasis","disabled"])&&Xw(w,c,h._api);else{var M=jf(w.mainType,w.componentIndex,c.name,h._api),A=M.focusSelf,L=M.dispatchers;c.type===cn&&A&&!c.notBlur&&Hl(w.mainType,w.componentIndex,h._api),L&&C(L,function(I){c.type===cn?Fl(I):Nl(I)})}else Vl(c)&&w instanceof Ye&&(Kw(w,c,h._api),$c(w),ue(h))},h),p&&p.eachComponent(m,function(w){var T=S&&S.get(w.id)!=null;T||b(h[d==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[v]&&w[v](w.__model,p,h._api,c)}},dr={prepareAndUpdate:function(h){kn(this),dr.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,v){var c=this._model,d=this._api,g=this._zr,p=this._coordSysMgr,y=this._scheduler;if(c){c.setUpdatePayload(h),y.restoreData(c,h),y.performSeriesTasks(c),p.create(c,d),y.performDataProcessorTasks(c,h),nl(this,c),p.update(c,d),e(c),y.performVisualTasks(c,h),al(this,c,d,h,v);var m=c.get("backgroundColor")||"transparent",_=c.get("darkMode");g.setBackgroundColor(m),_!=null&&_!=="auto"&&g.setDarkMode(_),De.trigger("afterupdate",c,d)}},updateTransform:function(h){var v=this,c=this._model,d=this._api;if(c){c.setUpdatePayload(h);var g=[];c.eachComponent(function(y,m){if(y!=="series"){var _=v.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,c,d,h);S&&S.update&&g.push(_)}else g.push(_)}});var p=Z();c.eachSeries(function(y){var m=v._chartsMap[y.__viewId];if(m.updateTransform){var _=m.updateTransform(y,c,d,h);_&&_.update&&p.set(y.uid,1)}else p.set(y.uid,1)}),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0,dirtyMap:p}),po(this,c,d,h,{},p),De.trigger("afterupdate",c,d)}},updateView:function(h){var v=this._model;v&&(v.setUpdatePayload(h),We.markUpdateMethod(h,"updateView"),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0}),al(this,v,this._api,h,{}),De.trigger("afterupdate",v,this._api))},updateVisual:function(h){var v=this,c=this._model;c&&(c.setUpdatePayload(h),c.eachSeries(function(d){d.getData().clearAllVisual()}),We.markUpdateMethod(h,"updateVisual"),e(c),this._scheduler.performVisualTasks(c,h,{visualType:"visual",setDirty:!0}),c.eachComponent(function(d,g){if(d!=="series"){var p=v.getViewOfComponentModel(g);p&&p.__alive&&p.updateVisual(g,c,v._api,h)}}),c.eachSeries(function(d){var g=v._chartsMap[d.__viewId];g.updateVisual(d,c,v._api,h)}),De.trigger("afterupdate",c,this._api))},updateLayout:function(h){dr.update.call(this,h)}},rl=function(h,v,c,d){if(h._disposed){h.id;return}for(var g=h._model,p=h._coordSysMgr.getCoordinateSystems(),y,m=xu(g,c),_=0;_<p.length;_++){var S=p[_];if(S[v]&&(y=S[v](g,m,d))!=null)return y}},nl=function(h,v){var c=h._chartsMap,d=h._scheduler;v.eachSeries(function(g){d.updateStreamModes(g,c[g.__viewId])})},il=function(h,v){var c=this,d=this.getModel(),g=h.type,p=h.escapeConnect,y=is[g],m=y.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&Ge(_[0]);this[Et]=!0;var w=[h],T=!1;h.batch&&(T=!0,w=W(h.batch,function(R){return R=ft(B({},R),h),R.batch=null,R}));var M=[],A,L=Vl(h),I=Kc(h);if(I&&Ug(this._api),C(w,function(R){if(A=y.action(R,c._model,c._api),A=A||B({},R),A.type=m.event||A.type,M.push(A),I){var k=Xf(h),F=k.queryOptionMap,K=k.mainTypeSpecified,U=K?F.keys()[0]:"series";vo(c,S,R,U),ue(c)}else L?(vo(c,S,R,"series"),ue(c)):b&&vo(c,S,R,b.main,b.sub)}),S!=="none"&&!I&&!L&&!b)try{this[Zt]?(kn(this),dr.update.call(this,h),this[Zt]=null):dr[S].call(this,h)}catch(R){throw this[Et]=!1,R}if(T?A={type:m.event||g,escapeConnect:p,batch:M}:A=M[0],this[Et]=!1,!v){var E=this._messageCenter;if(E.trigger(A.type,A),L){var O={type:"selectchanged",escapeConnect:p,selected:jw(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};E.trigger(O.type,O)}}},wi=function(h){for(var v=this._pendingActions;v.length;){var c=v.shift();il.call(this,c,h)}},xi=function(h){!h&&this.trigger("updated")},Cd=function(h,v){h.on("rendered",function(c){v.trigger("rendered",c),h.animation.isFinished()&&!v[Zt]&&!v._scheduler.unfinished&&!v._pendingActions.length&&v.trigger("finished")})},Md=function(h,v){h.on("mouseover",function(c){var d=c.target,g=Ni(d,Gl);g&&($w(g,c,v._api),ue(v))}).on("mouseout",function(c){var d=c.target,g=Ni(d,Gl);g&&(Zw(g,c,v._api),ue(v))}).on("click",function(c){var d=c.target,g=Ni(d,function(m){return dt(m).dataIndex!=null},!0);if(g){var p=g.selected?"unselect":"select",y=dt(g);v._api.dispatchAction({type:p,dataType:y.dataType,dataIndexInside:y.dataIndex,seriesIndex:y.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(v){v.clearColorPalette()})}function n(h){var v=[],c=[],d=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();d=d||!!w,(m==="series"?c:v).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),d){var g=v.concat(c),p,y;Eo(g,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),C(g,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;p!=null&&(S=Math.max(p,S)),b?(S===p&&b!==y&&S++,y=b):y&&(S===p&&S++,y=""),p=S,_.setZLevel(S)})}}al=function(h,v,c,d,g){n(v),Dd(h,v,c,d,g),C(h._chartsViews,function(p){p.__alive=!1}),po(h,v,c,d,g),C(h._chartsViews,function(p){p.__alive||p.remove(v,c)})},Dd=function(h,v,c,d,g,p){C(p||h._componentsViews,function(y){var m=y.__model;l(m,y),y.render(m,v,c,d),s(m,y),f(m,y)})},po=function(h,v,c,d,g,p){var y=h._scheduler;g=B(g||{},{updatedSeries:v.getSeries()}),De.trigger("series:beforeupdate",v,c,g);var m=!1;v.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;y.updatePayload(b,d),l(_,S),p&&p.get(_.uid)&&b.dirty(),b.perform(y.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),$c(_)}),y.unfinished=m||y.unfinished,De.trigger("series:layoutlabels",v,c,g),De.trigger("series:transition",v,c,g),v.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,v),De.trigger("series:afterupdate",v,c,g)},ue=function(h){h[Ju]=!0,h.getZr().wakeUp()},Ld=function(h){h[Ju]&&(h.getZr().storage.traverse(function(v){qi(v)||i(v)}),h[Ju]=!1)};function i(h){for(var v=[],c=h.currentStates,d=0;d<c.length;d++){var g=c[d];g==="emphasis"||g==="blur"||g==="select"||v.push(g)}h.selected&&h.states.select&&v.push("select"),h.hoverState===ys&&h.states.emphasis?v.push("emphasis"):h.hoverState===gs&&h.states.blur&&v.push("blur"),h.useStates(v)}function a(h,v){var c=h._zr,d=c.storage,g=0;d.traverse(function(p){p.isGroup||g++}),g>v.get("hoverLayerThreshold")&&!Y.node&&!Y.worker&&v.eachSeries(function(p){if(!p.preventUsingHoverLayer){var y=h._chartsMap[p.__viewId];y.__alive&&y.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,v){var c=h.get("blendMode")||null;v.eachRendered(function(d){d.isGroup||(d.style.blend=c)})}function s(h,v){if(!h.preventAutoZ){var c=h.get("z")||0,d=h.get("zlevel")||0;v.eachRendered(function(g){return u(g,c,d,-1/0),!0})}}function u(h,v,c,d){var g=h.getTextContent(),p=h.getTextGuideLine(),y=h.isGroup;if(y)for(var m=h.childrenRef(),_=0;_<m.length;_++)d=Math.max(u(m[_],v,c,d),d);else h.z=v,h.zlevel=c,d=Math.max(h.z2,d);if(g&&(g.z=v,g.zlevel=c,isFinite(d)&&(g.z2=d+2)),p){var S=h.textGuideLineConfig;p.z=v,p.zlevel=c,isFinite(d)&&(p.z2=d+(S&&S.showAbove?1:-1))}return d}function l(h,v){v.eachRendered(function(c){if(!qi(c)){var d=c.getTextContent(),g=c.getTextGuideLine();c.stateTransition&&(c.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),g&&g.stateTransition&&(g.stateTransition=null),c.hasState()?(c.prevStates=c.currentStates,c.clearStates()):c.prevStates&&(c.prevStates=null)}})}function f(h,v){var c=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),g=c.get("duration"),p=g>0?{duration:g,delay:c.get("delay"),easing:c.get("easing")}:null;v.eachRendered(function(y){if(y.states&&y.states.emphasis){if(qi(y))return;if(y instanceof mt&&rx(y),y.__dirty){var m=y.prevStates;m&&y.useStates(m)}if(d){y.stateTransition=p;var _=y.getTextContent(),S=y.getTextGuideLine();_&&(_.stateTransition=p),S&&(S.stateTransition=p)}y.__dirty&&i(y)}})}Ad=function(h){return new(function(v){H(c,v);function c(){return v!==null&&v.apply(this,arguments)||this}return c.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},c.prototype.getComponentByElement=function(d){for(;d;){var g=d.__ecComponentInfo;if(g!=null)return h._model.getComponent(g.mainType,g.index);d=d.parent}},c.prototype.enterEmphasis=function(d,g){Fl(d,g),ue(h)},c.prototype.leaveEmphasis=function(d,g){Nl(d,g),ue(h)},c.prototype.enterBlur=function(d){qw(d),ue(h)},c.prototype.leaveBlur=function(d){Hg(d),ue(h)},c.prototype.enterSelect=function(d){Gg(d),ue(h)},c.prototype.leaveSelect=function(d){Vg(d),ue(h)},c.prototype.getModel=function(){return h.getModel()},c.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},c.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},c}(xm))(h)},Zm=function(h){function v(c,d){for(var g=0;g<c.length;g++){var p=c[g];p[tl]=d}}C(Zi,function(c,d){h._messageCenter.on(d,function(g){if(os[h.group]&&h[tl]!==Td){if(g&&g.escapeConnect)return;var p=h.makeActionFromEvent(g),y=[];C(vn,function(m){m!==h&&m.group===h.group&&y.push(m)}),v(y,Td),C(y,function(m){m[tl]!==lD&&m.dispatchAction(p)}),v(y,fD)}})})}}(),t}(qe),Mh=ns.prototype;Mh.on=Um("on");Mh.off=Um("off");Mh.one=function(r,t,e){var n=this;function i(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),n.off(r,i)}this.on.call(this,r,i,e)};var hD=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var is={},Zi={},vf=[],df=[],as=[],Km={},pf={},vn={},os={},cD=+new Date-0,vD=+new Date-0,Dh="_echarts_instance_";function dD(r,t,e){var n=!(e&&e.ssr);if(n){var i=Ah(r);if(i)return i}var a=new ns(r,t,e);return a.id="ec_"+cD++,vn[a.id]=a,n&&kg(r,Dh,a.id),Zm(a),De.trigger("afterinit",a),a}function pD(r){if(N(r)){var t=r;r=null,C(t,function(e){e.group!=null&&(r=e.group)}),r=r||"g_"+vD++,C(t,function(e){e.group=r})}return os[r]=!0,r}function jm(r){os[r]=!1}var gD=jm;function yD(r){G(r)?r=vn[r]:r instanceof ns||(r=Ah(r)),r instanceof ns&&!r.isDisposed()&&r.dispose()}function Ah(r){return vn[Ew(r,Dh)]}function mD(r){return vn[r]}function Lh(r,t){Km[r]=t}function Ih(r){lt(df,r)<0&&df.push(r)}function Ph(r,t){Rh(vf,r,t,rD)}function Qm(r){Hs("afterinit",r)}function Jm(r){Hs("afterupdate",r)}function Hs(r,t){De.on(r,t)}function _n(r,t,e){j(t)&&(e=t,t="");var n=V(r)?r.type:[r,r={event:t}][0];r.event=(r.event||n).toLowerCase(),t=r.event,!Zi[t]&&(Re(bd.test(n)&&bd.test(t)),is[n]||(is[n]={action:e,actionInfo:r}),Zi[t]=n)}function t_(r,t){Ps.register(r,t)}function _D(r){var t=Ps.get(r);if(t)return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()}function e_(r,t){Rh(as,r,t,Hm,"layout")}function Rr(r,t){Rh(as,r,t,Gm,"visual")}var Id=[];function Rh(r,t,e,n,i){if((j(t)||V(t))&&(e=t,t=n),!(lt(Id,e)>=0)){Id.push(e);var a=Dm.wrapStageHandler(e,i);a.__prio=t,a.__raw=e,r.push(a)}}function Eh(r,t){pf[r]=t}function SD(r){zp({createCanvas:r})}function r_(r,t,e){var n=zm("registerMap");n&&n(r,t,e)}function wD(r){var t=zm("getMap");return t&&t(r)}var n_=mT;Rr(Ch,hM);Rr(zs,cM);Rr(zs,vM);Rr(Ch,MM);Rr(zs,DM);Rr(Vm,$M);Ih(Tm);Ph(tD,uM);Eh("default",dM);_n({type:cn,event:cn,update:cn},Wt);_n({type:Do,event:Do,update:Do},Wt);_n({type:Wi,event:Wi,update:Wi},Wt);_n({type:Ao,event:Ao,update:Ao},Wt);_n({type:Ui,event:Ui,update:Ui},Wt);Lh("light",TM);Lh("dark",Pm);var xD={},Pd=[],bD={registerPreprocessor:Ih,registerProcessor:Ph,registerPostInit:Qm,registerPostUpdate:Jm,registerUpdateLifecycle:Hs,registerAction:_n,registerCoordinateSystem:t_,registerLayout:e_,registerVisual:Rr,registerTransform:n_,registerLoading:Eh,registerMap:r_,registerImpl:ZM,PRIORITY:Wm,ComponentModel:ot,ComponentView:oe,SeriesModel:Ye,ChartView:We,registerComponentModel:function(r){ot.registerClass(r)},registerComponentView:function(r){oe.registerClass(r)},registerSeriesModel:function(r){Ye.registerClass(r)},registerChartView:function(r){We.registerClass(r)},registerSubTypeDefaulter:function(r,t){ot.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){_m(r,t)}};function Lr(r){if(N(r)){C(r,function(t){Lr(t)});return}lt(Pd,r)>=0||(Pd.push(r),j(r)&&(r={install:r}),r.install(bD))}var TD=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(ot),i_=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),gf=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Le).models[0]},t.type="cartesian2dAxis",t}(ot);_e(gf,i_);var a_={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},CD=nt({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},a_),Oh=nt({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},a_),MD=nt({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Oh),DD=ft({logBase:10},Oh);const AD={category:CD,value:Oh,time:MD,log:DD};var LD=0,yf=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++LD}return r.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&W(n,ID);return new r({categories:i,needCollect:!i,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!G(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return e=i.get(t),e==null&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=Z(this.categories))},r}();function ID(r){return V(r)&&r.value!=null?r.value:r+""}var PD={value:1,category:1,time:1,log:1};function Rd(r,t,e,n){C(PD,function(i,a){var o=nt(nt({},AD[a],!0),n,!0),s=function(u){H(l,u);function l(){var f=u!==null&&u.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return l.prototype.mergeDefaultAndTheme=function(f,h){var v=ua(this),c=v?Fs(f):{},d=h.getTheme();nt(f,d.get(a+"Axis")),nt(f,this.getDefaultOption()),f.type=Ed(f),v&&Kn(f,c,v)},l.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=yf.createByAxisModel(this))},l.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},l.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},l.type=t+"Axis."+a,l.defaultOption=o,l}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",Ed)}function Ed(r){return r.type||(r.data?"category":"value")}var $e=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();fs($e);function mf(r){return r.type==="interval"||r.type==="log"}function RD(r,t,e,n){var i={},a=r[1]-r[0],o=i.interval=Yf(a/t,!0);e!=null&&o<e&&(o=i.interval=e),n!=null&&o>n&&(o=i.interval=n);var s=i.intervalPrecision=o_(o),u=i.niceTickExtent=[It(Math.ceil(r[0]/o)*o,s),It(Math.floor(r[1]/o)*o,s)];return ED(u,r),i}function ol(r){var t=Math.pow(10,ps(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,It(e*t)}function o_(r){return Ve(r)+2}function Od(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function ED(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),Od(r,0,t),Od(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Gs(r,t){return r>=t[0]&&r<=t[1]}function Vs(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function Ws(r,t){return r*(t[1]-t[0])+t[0]}var kh=function(r){H(t,r);function t(e){var n=r.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new yf({})),N(i)&&(i=new yf({categories:W(i,function(a){return V(a)?a.value:a})})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return t.prototype.parse=function(e){return e==null?NaN:G(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Gs(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Vs(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(Ws(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],n=this._extent,i=n[0];i<=n[1];)e.push({value:i}),i++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var n=e.ordinalNumbers,i=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,u=Math.min(s,n.length);o<u;++o){var l=n[o];i[o]=l,a[l]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;i.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var n=this._ticksByOrdinalNumber;return n&&e>=0&&e<n.length?n[e]:e},t.prototype.getRawOrdinalNumber=function(e){var n=this._ordinalNumbersByTick;return n&&e>=0&&e<n.length?n[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var n=this.getRawOrdinalNumber(e.value),i=this._ordinalMeta.categories[n];return i==null?"":i+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,n){this.unionExtent(e.getApproximateExtent(n))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}($e);$e.registerClass(kh);var en=It,ti=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Gs(e,this._extent)},t.prototype.normalize=function(e){return Vs(e,this._extent)},t.prototype.scale=function(e){return Ws(e,this._extent)},t.prototype.setExtent=function(e,n){var i=this._extent;isNaN(e)||(i[0]=parseFloat(e)),isNaN(n)||(i[1]=parseFloat(n))},t.prototype.unionExtent=function(e){var n=this._extent;e[0]<n[0]&&(n[0]=e[0]),e[1]>n[1]&&(n[1]=e[1]),this.setExtent(n[0],n[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=o_(e)},t.prototype.getTicks=function(e){var n=this._interval,i=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!n)return s;var u=1e4;i[0]<a[0]&&(e?s.push({value:en(a[0]-n,o)}):s.push({value:i[0]}));for(var l=a[0];l<=a[1]&&(s.push({value:l}),l=en(l+n,o),l!==s[s.length-1].value);)if(s.length>u)return[];var f=s.length?s[s.length-1].value:a[1];return i[1]>f&&(e?s.push({value:en(f+n,o)}):s.push({value:i[1]})),s},t.prototype.getMinorTicks=function(e){for(var n=this.getTicks(!0),i=[],a=this.getExtent(),o=1;o<n.length;o++){for(var s=n[o],u=n[o-1],l=0,f=[],h=s.value-u.value,v=h/e;l<e-1;){var c=en(u.value+(l+1)*v);c>a[0]&&c<a[1]&&f.push(c),l++}i.push(f)}return i},t.prototype.getLabel=function(e,n){if(e==null)return"";var i=n&&n.precision;i==null?i=Ve(e.value)||0:i==="auto"&&(i=this._intervalPrecision);var a=en(e.value,i,!0);return dh(a)},t.prototype.calcNiceTicks=function(e,n,i){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=RD(a,e,n,i);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var n=this._extent;if(n[0]===n[1])if(n[0]!==0){var i=Math.abs(n[0]);e.fixMax||(n[1]+=i/2),n[0]-=i/2}else n[1]=1;var a=n[1]-n[0];isFinite(a)||(n[0]=0,n[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(n[0]=en(Math.floor(n[0]/o)*o)),e.fixMax||(n[1]=en(Math.ceil(n[1]/o)*o))},t.prototype.setNiceExtent=function(e,n){this._niceExtent=[e,n]},t.type="interval",t}($e);$e.registerClass(ti);var OD=function(r,t,e,n){for(;e<n;){var i=e+n>>>1;r[i][1]<t?e=i+1:n=i}return e},s_=function(r){H(t,r);function t(e){var n=r.call(this,e)||this;return n.type="time",n}return t.prototype.getLabel=function(e){var n=this.getSetting("useUTC");return Ma(e.value,Cv[Zb(Un(this._minLevelUnit))]||Cv.second,n,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,n,i){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return Kb(e,n,i,o,a)},t.prototype.getTicks=function(){var e=this._interval,n=this._extent,i=[];if(!e)return i;i.push({value:n[0],level:0});var a=this.getSetting("useUTC"),o=GD(this._minLevelUnit,this._approxInterval,a,n);return i=i.concat(o),i.push({value:n[1],level:0}),i},t.prototype.calcNiceExtent=function(e){var n=this._extent;if(n[0]===n[1]&&(n[0]-=ye,n[1]+=ye),n[1]===-1/0&&n[0]===1/0){var i=new Date;n[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),n[0]=n[1]-ye}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,n,i){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,n!=null&&this._approxInterval<n&&(this._approxInterval=n),i!=null&&this._approxInterval>i&&(this._approxInterval=i);var s=go.length,u=Math.min(OD(go,this._approxInterval,0,s),s-1);this._interval=go[u][1],this._minLevelUnit=go[Math.max(u-1,0)][0]},t.prototype.parse=function(e){return wt(e)?e:+me(e)},t.prototype.contain=function(e){return Gs(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Vs(this.parse(e),this._extent)},t.prototype.scale=function(e){return Ws(e,this._extent)},t.type="time",t}(ti),go=[["second",hh],["minute",ch],["hour",Xi],["quarter-day",Xi*6],["half-day",Xi*12],["day",ye*1.2],["half-week",ye*3.5],["week",ye*7],["month",ye*31],["quarter",ye*95],["half-year",Tv/2],["year",Tv]];function kD(r,t,e,n){var i=me(t),a=me(e),o=function(d){return Mv(i,d,n)===Mv(a,d,n)},s=function(){return o("year")},u=function(){return s()&&o("month")},l=function(){return u()&&o("day")},f=function(){return l()&&o("hour")},h=function(){return f()&&o("minute")},v=function(){return h()&&o("second")},c=function(){return v()&&o("millisecond")};switch(r){case"year":return s();case"month":return u();case"day":return l();case"hour":return f();case"minute":return h();case"second":return v();case"millisecond":return c()}}function BD(r,t){return r/=ye,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function FD(r){var t=30*ye;return r/=t,r>6?6:r>3?3:r>2?2:1}function ND(r){return r/=Xi,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function kd(r,t){return r/=t?ch:hh,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function zD(r){return Yf(r,!0)}function HD(r,t,e){var n=new Date(r);switch(Un(t)){case"year":case"month":n[Uy(e)](0);case"day":n[Yy(e)](1);case"hour":n[qy(e)](0);case"minute":n[Xy(e)](0);case"second":n[$y(e)](0),n[Zy(e)](0)}return n.getTime()}function GD(r,t,e,n){var i=1e4,a=Vy,o=0;function s(L,I,E,O,R,k,F){for(var K=new Date(I),U=I,q=K[O]();U<E&&U<=n[1];)F.push({value:U}),q+=L,K[R](q),U=K.getTime();F.push({value:U,notAdd:!0})}function u(L,I,E){var O=[],R=!I.length;if(!kD(Un(L),n[0],n[1],e)){R&&(I=[{value:HD(new Date(n[0]),L,e)},{value:n[1]}]);for(var k=0;k<I.length-1;k++){var F=I[k].value,K=I[k+1].value;if(F!==K){var U=void 0,q=void 0,Q=void 0,ht=!1;switch(L){case"year":U=Math.max(1,Math.round(t/ye/365)),q=vh(e),Q=jb(e);break;case"half-year":case"quarter":case"month":U=FD(t),q=Yn(e),Q=Uy(e);break;case"week":case"half-week":case"day":U=BD(t),q=Es(e),Q=Yy(e),ht=!0;break;case"half-day":case"quarter-day":case"hour":U=ND(t),q=oa(e),Q=qy(e);break;case"minute":U=kd(t,!0),q=Os(e),Q=Xy(e);break;case"second":U=kd(t,!1),q=ks(e),Q=$y(e);break;case"millisecond":U=zD(t),q=Bs(e),Q=Zy(e);break}s(U,F,K,q,Q,ht,O),L==="year"&&E.length>1&&k===0&&E.unshift({value:E[0].value-U})}}for(var k=0;k<O.length;k++)E.push(O[k]);return O}}for(var l=[],f=[],h=0,v=0,c=0;c<a.length&&o++<i;++c){var d=Un(a[c]);if($b(a[c])){u(a[c],l[l.length-1]||[],f);var g=a[c+1]?Un(a[c+1]):null;if(d!==g){if(f.length){v=h,f.sort(function(L,I){return L.value-I.value});for(var p=[],y=0;y<f.length;++y){var m=f[y].value;(y===0||f[y-1].value!==m)&&(p.push(f[y]),m>=n[0]&&m<=n[1]&&h++)}var _=(n[1]-n[0])/t;if(h>_*1.5&&v>_/1.5||(l.push(p),h>_||r===a[c]))break}f=[]}}}for(var S=Tt(W(l,function(L){return Tt(L,function(I){return I.value>=n[0]&&I.value<=n[1]&&!I.notAdd})}),function(L){return L.length>0}),b=[],w=S.length-1,c=0;c<S.length;++c)for(var T=S[c],M=0;M<T.length;++M)b.push({value:T[M].value,level:w-c});b.sort(function(L,I){return L.value-I.value});for(var A=[],c=0;c<b.length;++c)(c===0||b[c].value!==b[c-1].value)&&A.push(b[c]);return A}$e.registerClass(s_);var Bd=$e.prototype,Ki=ti.prototype,VD=It,WD=Math.floor,UD=Math.ceil,yo=Math.pow,Te=Math.log,Bh=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new ti,e._interval=0,e}return t.prototype.getTicks=function(e){var n=this._originalScale,i=this._extent,a=n.getExtent(),o=Ki.getTicks.call(this,e);return W(o,function(s){var u=s.value,l=It(yo(this.base,u));return l=u===i[0]&&this._fixMin?mo(l,a[0]):l,l=u===i[1]&&this._fixMax?mo(l,a[1]):l,{value:l}},this)},t.prototype.setExtent=function(e,n){var i=Te(this.base);e=Te(Math.max(0,e))/i,n=Te(Math.max(0,n))/i,Ki.setExtent.call(this,e,n)},t.prototype.getExtent=function(){var e=this.base,n=Bd.getExtent.call(this);n[0]=yo(e,n[0]),n[1]=yo(e,n[1]);var i=this._originalScale,a=i.getExtent();return this._fixMin&&(n[0]=mo(n[0],a[0])),this._fixMax&&(n[1]=mo(n[1],a[1])),n},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var n=this.base;e[0]=Te(e[0])/Te(n),e[1]=Te(e[1])/Te(n),Bd.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,n){this.unionExtent(e.getApproximateExtent(n))},t.prototype.calcNiceTicks=function(e){e=e||10;var n=this._extent,i=n[1]-n[0];if(!(i===1/0||i<=0)){var a=Ag(i),o=e/i*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[It(UD(n[0]/a)*a),It(WD(n[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){Ki.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=Te(e)/Te(this.base),Gs(e,this._extent)},t.prototype.normalize=function(e){return e=Te(e)/Te(this.base),Vs(e,this._extent)},t.prototype.scale=function(e){return e=Ws(e,this._extent),yo(this.base,e)},t.type="log",t}($e),u_=Bh.prototype;u_.getMinorTicks=Ki.getMinorTicks;u_.getLabel=Ki.getLabel;function mo(r,t){return VD(r,Ve(t))}$e.registerClass(Bh);var YD=function(){function r(t,e,n){this._prepareParams(t,e,n)}return r.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;j(o)?this._modelMinNum=_o(t,o({min:n[0],max:n[1]})):o!=="dataMin"&&(this._modelMinNum=_o(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(j(s)?this._modelMaxNum=_o(t,s({min:n[0],max:n[1]})):s!=="dataMax"&&(this._modelMaxNum=_o(t,s)),i)this._axisDataLen=e.getCategories().length;else{var u=e.get("boundaryGap"),l=N(u)?u:[u||0,u||0];typeof l[0]=="boolean"||typeof l[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[pn(l[0],1),pn(l[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,a=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,u=this._modelMaxRaw==="dataMax"?n:this._modelMaxNum,l=s!=null,f=u!=null;s==null&&(s=t?i?0:NaN:e-a[0]*o),u==null&&(u=t?i?i-1:NaN:n+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(u==null||!isFinite(u))&&(u=NaN);var h=ji(s)||ji(u)||t&&!i;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!f&&(u=0));var v=this._determinedMin,c=this._determinedMax;return v!=null&&(s=v,l=!0),c!=null&&(u=c,f=!0),{min:s,max:u,minFixed:l,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[XD[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var n=qD[t];this[n]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),qD={min:"_determinedMin",max:"_determinedMax"},XD={min:"_dataMin",max:"_dataMax"};function $D(r,t,e){var n=r.rawExtentInfo;return n||(n=new YD(r,t,e),r.rawExtentInfo=n,n)}function _o(r,t){return t==null?null:ji(t)?NaN:r.parse(t)}function l_(r,t){var e=r.type,n=$D(r,t,r.getExtent()).calculate();r.setBlank(n.isBlank);var i=n.min,a=n.max,o=t.ecModel;if(o&&e==="time"){var s=um("bar",o),u=!1;if(C(s,function(h){u=u||h.getBaseAxis()===t.axis}),u){var l=lm(s),f=ZD(i,a,t,l);i=f.min,a=f.max}}return{extent:[i,a],fixMin:n.minFixed,fixMax:n.maxFixed}}function ZD(r,t,e,n){var i=e.axis.getExtent(),a=Math.abs(i[1]-i[0]),o=nC(n,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;C(o,function(c){s=Math.min(c.offset,s)});var u=-1/0;C(o,function(c){u=Math.max(c.offset+c.width,u)}),s=Math.abs(s),u=Math.abs(u);var l=s+u,f=t-r,h=1-(s+u)/a,v=f/h-f;return t+=v*(u/l),r-=v*(s/l),{min:r,max:t}}function _f(r,t){var e=t,n=l_(r,e),i=n.extent,a=e.get("splitNumber");r instanceof Bh&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),u=o==="interval"||o==="time";r.setExtent(i[0],i[1]),r.calcNiceExtent({splitNumber:a,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:u?e.get("minInterval"):null,maxInterval:u?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function f_(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new kh({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new s_({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new($e.getClass(t)||ti)}}function KD(r){var t=r.scale.getExtent(),e=t[0],n=t[1];return!(e>0&&n>0||e<0&&n<0)}function ei(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(n){return function(i,a){return r.scale.getFormattedLabel(i,a,n)}}(t):G(t)?function(n){return function(i){var a=r.scale.getLabel(i),o=n.replace("{value}",a??"");return o}}(t):j(t)?function(n){return function(i,a){return e!=null&&(a=i.value-e),n(Fh(r,i),a,i.level!=null?{level:i.level}:null)}}(t):function(n){return r.scale.getLabel(n)}}function Fh(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function jD(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var n,i,a=e.getExtent();e instanceof kh?i=e.count():(n=e.getTicks(),i=n.length);var o=r.getLabelModel(),s=ei(r),u,l=1;i>40&&(l=Math.ceil(i/40));for(var f=0;f<i;f+=l){var h=n?n[f]:{value:a[0]+f},v=s(h,f),c=o.getTextRect(v),d=QD(c,o.get("rotate")||0);u?u.union(d):u=d}return u}}function QD(r,t){var e=t*Math.PI/180,n=r.width,i=r.height,a=n*Math.abs(Math.cos(e))+Math.abs(i*Math.sin(e)),o=n*Math.abs(Math.sin(e))+Math.abs(i*Math.cos(e)),s=new it(r.x,r.y,a,o);return s}function Nh(r){var t=r.get("interval");return t??"auto"}function h_(r){return r.type==="category"&&Nh(r.getLabelModel())===0}function c_(r,t){var e={};return C(r.mapDimensionsAll(t),function(n){e[zy(r,n)]=!0}),yt(e)}function GI(r,t,e){t&&C(c_(t,e),function(n){var i=t.getApproximateExtent(n);i[0]<r[0]&&(r[0]=i[0]),i[1]>r[1]&&(r[1]=i[1])})}var JD=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return W(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),Tt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}(),Sf=["x","y"];function Fd(r){return r.type==="interval"||r.type==="time"}var tA=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Sf,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,n=this.getAxis("y").scale;if(!(!Fd(e)||!Fd(n))){var i=e.getExtent(),a=n.getExtent(),o=this.dataToPoint([i[0],a[0]]),s=this.dataToPoint([i[1],a[1]]),u=i[1]-i[0],l=a[1]-a[0];if(!(!u||!l)){var f=(s[0]-o[0])/u,h=(s[1]-o[1])/l,v=o[0]-i[0]*f,c=o[1]-a[0]*h,d=this._transform=[f,0,0,h,v,c];this._invTransform=_a([],d)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var n=this.getAxis("x"),i=this.getAxis("y");return n.contain(n.toLocalCoord(e[0]))&&i.contain(i.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,n){var i=this.dataToPoint(e),a=this.dataToPoint(n),o=this.getArea(),s=new it(i[0],i[1],a[0]-i[0],a[1]-i[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,n,i){i=i||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return Ut(i,e,this._transform);var s=this.getAxis("x"),u=this.getAxis("y");return i[0]=s.toGlobalCoord(s.dataToCoord(a,n)),i[1]=u.toGlobalCoord(u.dataToCoord(o,n)),i},t.prototype.clampData=function(e,n){var i=this.getAxis("x").scale,a=this.getAxis("y").scale,o=i.getExtent(),s=a.getExtent(),u=i.parse(e[0]),l=a.parse(e[1]);return n=n||[],n[0]=Math.min(Math.max(Math.min(o[0],o[1]),u),Math.max(o[0],o[1])),n[1]=Math.min(Math.max(Math.min(s[0],s[1]),l),Math.max(s[0],s[1])),n},t.prototype.pointToData=function(e,n){var i=[];if(this._invTransform)return Ut(i,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return i[0]=a.coordToData(a.toLocalCoord(e[0]),n),i[1]=o.coordToData(o.toLocalCoord(e[1]),n),i},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var n=this.getAxis("x").getGlobalExtent(),i=this.getAxis("y").getGlobalExtent(),a=Math.min(n[0],n[1])-e,o=Math.min(i[0],i[1])-e,s=Math.max(n[0],n[1])-a+e,u=Math.max(i[0],i[1])-o+e;return new it(a,o,s,u)},t}(JD),va=Mt();function v_(r,t){var e=W(t,function(n){return r.scale.parse(n)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function eA(r){var t=r.getLabelModel().get("customValues");if(t){var e=ei(r),n=r.scale.getExtent(),i=v_(r,t),a=Tt(i,function(o){return o>=n[0]&&o<=n[1]});return{labels:W(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?nA(r):aA(r)}function rA(r,t){var e=r.getTickModel().get("customValues");if(e){var n=r.scale.getExtent(),i=v_(r,e);return{ticks:Tt(i,function(a){return a>=n[0]&&a<=n[1]})}}return r.type==="category"?iA(r,t):{ticks:W(r.scale.getTicks(),function(a){return a.value})}}function nA(r){var t=r.getLabelModel(),e=d_(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function d_(r,t){var e=p_(r,"labels"),n=Nh(t),i=g_(e,n);if(i)return i;var a,o;return j(n)?a=__(r,n):(o=n==="auto"?oA(r):n,a=m_(r,o)),y_(e,n,{labels:a,labelCategoryInterval:o})}function iA(r,t){var e=p_(r,"ticks"),n=Nh(t),i=g_(e,n);if(i)return i;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),j(n))a=__(r,n,!0);else if(n==="auto"){var s=d_(r,r.getLabelModel());o=s.labelCategoryInterval,a=W(s.labels,function(u){return u.tickValue})}else o=n,a=m_(r,o,!0);return y_(e,n,{ticks:a,tickCategoryInterval:o})}function aA(r){var t=r.scale.getTicks(),e=ei(r);return{labels:W(t,function(n,i){return{level:n.level,formattedLabel:e(n,i),rawLabel:r.scale.getLabel(n),tickValue:n.value}})}}function p_(r,t){return va(r)[t]||(va(r)[t]=[])}function g_(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function y_(r,t,e){return r.push({key:t,value:e}),e}function oA(r){var t=va(r).autoInterval;return t??(va(r).autoInterval=r.calculateCategoryInterval())}function sA(r){var t=uA(r),e=ei(r),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=r.scale,a=i.getExtent(),o=i.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var u=a[0],l=r.dataToCoord(u+1)-r.dataToCoord(u),f=Math.abs(l*Math.cos(n)),h=Math.abs(l*Math.sin(n)),v=0,c=0;u<=a[1];u+=s){var d=0,g=0,p=Nf(e({value:u}),t.font,"center","top");d=p.width*1.3,g=p.height*1.3,v=Math.max(v,d,7),c=Math.max(c,g,7)}var y=v/f,m=c/h;isNaN(y)&&(y=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(y,m))),S=va(r.model),b=r.getExtent(),w=S.lastAutoInterval,T=S.lastTickCount;return w!=null&&T!=null&&Math.abs(w-_)<=1&&Math.abs(T-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function uA(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function m_(r,t,e){var n=ei(r),i=r.scale,a=i.getExtent(),o=r.getLabelModel(),s=[],u=Math.max((t||0)+1,1),l=a[0],f=i.count();l!==0&&u>1&&f/u>2&&(l=Math.round(Math.ceil(l/u)*u));var h=h_(r),v=o.get("showMinLabel")||h,c=o.get("showMaxLabel")||h;v&&l!==a[0]&&g(a[0]);for(var d=l;d<=a[1];d+=u)g(d);c&&d-u!==a[1]&&g(a[1]);function g(p){var y={value:p};s.push(e?p:{formattedLabel:n(y),rawLabel:i.getLabel(y),tickValue:p})}return s}function __(r,t,e){var n=r.scale,i=ei(r),a=[];return C(n.getTicks(),function(o){var s=n.getLabel(o),u=o.value;t(o.value,s)&&a.push(e?u:{formattedLabel:i(o),rawLabel:s,tickValue:u})}),a}var Nd=[0,1],S_=function(){function r(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return r.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return Dg(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},r.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&i.type==="ordinal"&&(n=n.slice(),zd(n,i.count())),Bl(t,Nd,n,e)},r.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&i.type==="ordinal"&&(n=n.slice(),zd(n,i.count()));var a=Bl(t,n,Nd,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),n=rA(this,e),i=n.ticks,a=W(i,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return lA(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var n=this.scale.getMinorTicks(e),i=W(n,function(a){return W(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return i},r.prototype.getViewLabels=function(){return eA(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);n===0&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},r.prototype.calculateCategoryInterval=function(){return sA(this)},r}();function zd(r,t){var e=r[1]-r[0],n=t,i=e/n/2;r[0]+=i,r[1]-=i}function lA(r,t,e,n){var i=t.length;if(!r.onBand||e||!i)return;var a=r.getExtent(),o,s;if(i===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var u=t[i-1].tickValue-t[0].tickValue,l=(t[i-1].coord-t[0].coord)/u;C(t,function(c){c.coord-=l/2});var f=r.scale.getExtent();s=1+f[1]-t[i-1].tickValue,o={coord:t[i-1].coord+l*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];v(t[0].coord,a[0])&&(n?t[0].coord=a[0]:t.shift()),n&&v(a[0],t[0].coord)&&t.unshift({coord:a[0]}),v(a[1],o.coord)&&(n?o.coord=a[1]:t.pop()),n&&v(o.coord,a[1])&&t.push({coord:a[1]});function v(c,d){return c=It(c),d=It(d),h?c>d:c<d}}var fA=function(r){H(t,r);function t(e,n,i,a,o){var s=r.call(this,e,n,i)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var n=this.getExtent();return n[0]=this.toGlobalCoord(n[0]),n[1]=this.toGlobalCoord(n[1]),e&&n[0]>n[1]&&n.reverse(),n},t.prototype.pointToData=function(e,n){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),n)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(S_);function wf(r,t,e){e=e||{};var n=r.coordinateSystem,i=t.axis,a={},o=i.getAxesOnZeroOf()[0],s=i.position,u=o?"onZero":s,l=i.dim,f=n.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],v={left:0,right:1,top:0,bottom:1,onZero:2},c=t.get("offset")||0,d=l==="x"?[h[2]-c,h[3]+c]:[h[0]-c,h[1]+c];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));d[v.onZero]=Math.max(Math.min(g,d[1]),d[0])}a.position=[l==="y"?d[v[u]]:h[0],l==="x"?d[v[u]]:h[3]],a.rotation=Math.PI/2*(l==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[v[s]]-d[v.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),$n(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var y=t.get(["axisLabel","rotate"]);return a.labelRotate=u==="top"?-y:y,a.z2=1,a}function Hd(r){return r.get("coordinateSystem")==="cartesian2d"}function Gd(r){var t={xAxisModel:null,yAxisModel:null};return C(t,function(e,n){var i=n.replace(/Model$/,""),a=r.getReferringComponents(i,Le).models[0];t[n]=a}),t}var sl=Math.log;function hA(r,t,e){var n=ti.prototype,i=n.getTicks.call(e),a=n.getTicks.call(e,!0),o=i.length-1,s=n.getInterval.call(e),u=l_(r,t),l=u.extent,f=u.fixMin,h=u.fixMax;if(r.type==="log"){var v=sl(r.base);l=[sl(l[0])/v,sl(l[1])/v]}r.setExtent(l[0],l[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var c=n.getExtent.call(r);f&&(l[0]=c[0]),h&&(l[1]=c[1]);var d=n.getInterval.call(r),g=l[0],p=l[1];if(f&&h)d=(p-g)/o;else if(f)for(p=l[0]+d*o;p<l[1]&&isFinite(p)&&isFinite(l[1]);)d=ol(d),p=l[0]+d*o;else if(h)for(g=l[1]-d*o;g>l[0]&&isFinite(g)&&isFinite(l[0]);)d=ol(d),g=l[1]-d*o;else{var y=r.getTicks().length-1;y>o&&(d=ol(d));var m=d*o;p=Math.ceil(l[1]/d)*d,g=It(p-m),g<0&&l[0]>=0?(g=0,p=It(m)):p>0&&l[1]<=0&&(p=0,g=-It(m))}var _=(i[0].value-a[0].value)/s,S=(i[o].value-a[o].value)/s;n.setExtent.call(r,g+d*_,p+d*S),n.setInterval.call(r,d),(_||S)&&n.setNiceExtent.call(r,g+d,p-d)}var cA=function(){function r(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Sf,this._initCartesian(t,e,n),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model);function i(o){var s,u=yt(o),l=u.length;if(l){for(var f=[],h=l-1;h>=0;h--){var v=+u[h],c=o[v],d=c.model,g=c.scale;mf(g)&&d.get("alignTicks")&&d.get("interval")==null?f.push(c):(_f(g,d),mf(g)&&(s=c))}f.length&&(s||(s=f.pop(),_f(s.scale,s.model)),C(f,function(p){hA(p.scale,p.model,s.scale)}))}}i(n.x),i(n.y);var a={};C(n.x,function(o){Vd(n,"y",o,a)}),C(n.y,function(o){Vd(n,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),a=!n&&t.get("containLabel"),o=yn(i,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;u(),a&&(C(s,function(l){if(!l.model.get(["axisLabel","inside"])){var f=jD(l);if(f){var h=l.isHorizontal()?"height":"width",v=l.model.get(["axisLabel","margin"]);o[h]-=f[h]+v,l.position==="top"?o.y+=f.height+v:l.position==="left"&&(o.x+=f.width+v)}}}),u()),C(this._coordsList,function(l){l.calcAffineTransform()});function u(){C(s,function(l){var f=l.isHorizontal(),h=f?[0,o.width]:[0,o.height],v=l.inverse?1:0;l.setExtent(h[v],h[1-v]),vA(l,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(n!=null)return n[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var n="x"+t+"y"+e;return this._coordsMap[n]}V(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,a=this._coordsList;i<a.length;i++)if(a[i].getAxis("x").index===t||a[i].getAxis("y").index===e)return a[i]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},r.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,n=t.xAxisModel||e&&e.getReferringComponents("xAxis",Le).models[0],i=t.yAxisModel||e&&e.getReferringComponents("yAxis",Le).models[0],a=t.gridModel,o=this._coordsList,s,u;if(e)s=e.coordinateSystem,lt(o,s)<0&&(s=null);else if(n&&i)s=this.getCartesian(n.componentIndex,i.componentIndex);else if(n)u=this.getAxis("x",n.componentIndex);else if(i)u=this.getAxis("y",i.componentIndex);else if(a){var l=a.coordinateSystem;l===this&&(s=this._coordsList[0])}return{cartesian:s,axis:u}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,n){var i=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},u={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!u.x||!u.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,C(s.x,function(f,h){C(s.y,function(v,c){var d="x"+h+"y"+c,g=new tA(d);g.master=i,g.model=t,i._coordsMap[d]=g,i._coordsList.push(g),g.addAxis(f),g.addAxis(v)})});function l(f){return function(h,v){if(ul(h,t)){var c=h.get("position");f==="x"?c!=="top"&&c!=="bottom"&&(c=o.bottom?"top":"bottom"):c!=="left"&&c!=="right"&&(c=o.left?"right":"left"),o[c]=!0;var d=new fA(f,f_(h),[0,0],h.get("type"),c),g=d.type==="category";d.onBand=g&&h.get("boundaryGap"),d.inverse=h.get("inverse"),h.axis=d,d.model=h,d.grid=a,d.index=v,a._axesList.push(d),s[f][v]=d,u[f]++}}}},r.prototype._updateScale=function(t,e){C(this._axesList,function(i){if(i.scale.setExtent(1/0,-1/0),i.type==="category"){var a=i.model.get("categorySortInfo");i.scale.setSortInfo(a)}}),t.eachSeries(function(i){if(Hd(i)){var a=Gd(i),o=a.xAxisModel,s=a.yAxisModel;if(!ul(o,e)||!ul(s,e))return;var u=this.getCartesian(o.componentIndex,s.componentIndex),l=i.getData(),f=u.getAxis("x"),h=u.getAxis("y");n(l,f),n(l,h)}},this);function n(i,a){C(c_(i,a.dim),function(o){a.scale.unionExtentFromData(i,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],n=[];return C(this.getCartesians(),function(i){var a=t!=null&&t!=="auto"?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(a);lt(e,a)<0&&e.push(a),lt(n,o)<0&&n.push(o)}),{baseAxes:e,otherAxes:n}},r.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,a){var o=new r(i,t,e);o.name="grid_"+a,o.resize(i,e,!0),i.coordinateSystem=o,n.push(o)}),t.eachSeries(function(i){if(Hd(i)){var a=Gd(i),o=a.xAxisModel,s=a.yAxisModel,u=o.getCoordSysModel(),l=u.coordinateSystem;i.coordinateSystem=l.getCartesian(o.componentIndex,s.componentIndex)}}),n},r.dimensions=Sf,r}();function ul(r,t){return r.getCoordSysModel()===t}function Vd(r,t,e,n){e.getAxesOnZeroOf=function(){return a?[a]:[]};var i=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),u=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(u!=null)Wd(i[u])&&(a=i[u]);else for(var l in i)if(i.hasOwnProperty(l)&&Wd(i[l])&&!n[f(i[l])]){a=i[l];break}a&&(n[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function Wd(r){return r&&r.type!=="category"&&r.type!=="time"&&KD(r)}function vA(r,t){var e=r.getExtent(),n=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(i){return i+t}:function(i){return n-i+t},r.toLocalCoord=r.dim==="x"?function(i){return i-t}:function(i){return n-i+t}}var wr=Math.PI,Mr=function(){function r(t,e){this.group=new ae,this.opt=e,this.axisModel=t,ft(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new ae({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return r.prototype.hasBuilder=function(t){return!!Ud[t]},r.prototype.add=function(t){Ud[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,n){var i=Uf(e-t),a,o;return ra(i)?(o=n>0?"top":"bottom",a="center"):ra(i-wr)?(o=n>0?"bottom":"top",a="center"):(o="middle",i>0&&i<wr?a=n>0?"right":"left":a=n>0?"left":"right"),{rotation:i,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),Ud={axisLine:function(r,t,e,n){var i=t.get(["axisLine","show"]);if(i==="auto"&&r.handleAutoShown&&(i=r.handleAutoShown("axisLine")),!!i){var a=t.axis.getExtent(),o=n.transform,s=[a[0],0],u=[a[1],0],l=s[0]>u[0];o&&(Ut(s,s,o),Ut(u,u,o));var f=B({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new sr({shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});aa(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var v=t.get(["axisLine","symbol"]);if(v!=null){var c=t.get(["axisLine","symbolSize"]);G(v)&&(v=[v,v]),(G(c)||wt(c))&&(c=[c,c]);var d=$T(t.get(["axisLine","symbolOffset"])||0,c),g=c[0],p=c[1];C([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-u[0])*(s[0]-u[0])+(s[1]-u[1])*(s[1]-u[1]))}],function(y,m){if(v[m]!=="none"&&v[m]!=null){var _=Ns(v[m],-g/2,-p/2,g,p,f.stroke,!0),S=y.r+y.offset,b=l?u:s;_.attr({rotation:y.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,n){var i=gA(e,n,t,r),a=mA(e,n,t,r);if(pA(t,a,i),yA(e,n,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=oC(W(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));uC(o)}},axisName:function(r,t,e,n){var i=$n(r.axisName,t.get("name"));if(i){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),u=t.get("nameGap")||0,l=t.axis.getExtent(),f=l[0]>l[1]?-1:1,h=[a==="start"?l[0]-f*u:a==="end"?l[1]+f*u:(l[0]+l[1])/2,qd(a)?r.labelOffset+o*u:0],v,c=t.get("nameRotate");c!=null&&(c=c*wr/180);var d;qd(a)?v=Mr.innerTextLayout(r.rotation,c??r.rotation,o):(v=dA(r.rotation,a,c||0,l),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(v.rotation)),!isFinite(d)&&(d=null)));var g=s.getFont(),p=t.get("nameTruncate",!0)||{},y=p.ellipsis,m=$n(r.nameTruncateMaxWidth,p.maxWidth,d),_=new Xt({x:h[0],y:h[1],rotation:v.rotation,silent:Mr.isLabelSilent(t),style:gn(s,{text:i,font:g,overflow:"truncate",width:m,ellipsis:y,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||v.textAlign,verticalAlign:s.get("verticalAlign")||v.textVerticalAlign}),z2:1});if(Ds({el:_,componentModel:t,itemName:i}),_.__fullText=i,_.anid="name",t.get("triggerEvent")){var S=Mr.makeAxisEventDataBase(t);S.targetType="axisName",S.name=i,dt(_).eventData=S}n.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function dA(r,t,e,n){var i=Uf(e-r),a,o,s=n[0]>n[1],u=t==="start"&&!s||t!=="start"&&s;return ra(i-wr/2)?(o=u?"bottom":"top",a="center"):ra(i-wr*1.5)?(o=u?"top":"bottom",a="center"):(o="middle",i<wr*1.5&&i>wr/2?a=u?"left":"right":a=u?"right":"left"),{rotation:i,textAlign:a,textVerticalAlign:o}}function pA(r,t,e){if(!h_(r.axis)){var n=r.get(["axisLabel","showMinLabel"]),i=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],u=t[t.length-2],l=e[0],f=e[1],h=e[e.length-1],v=e[e.length-2];n===!1?(le(a),le(l)):Yd(a,o)&&(n?(le(o),le(f)):(le(a),le(l))),i===!1?(le(s),le(h)):Yd(u,s)&&(i?(le(u),le(v)):(le(s),le(h)))}}function le(r){r&&(r.ignore=!0)}function Yd(r,t){var e=r&&r.getBoundingRect().clone(),n=t&&t.getBoundingRect().clone();if(!(!e||!n)){var i=ma([]);return cs(i,i,-r.rotation),e.applyTransform(br([],i,r.getLocalTransform())),n.applyTransform(br([],i,t.getLocalTransform())),e.intersect(n)}}function qd(r){return r==="middle"||r==="center"}function w_(r,t,e,n,i){for(var a=[],o=[],s=[],u=0;u<r.length;u++){var l=r[u].coord;o[0]=l,o[1]=0,s[0]=l,s[1]=e,t&&(Ut(o,o,t),Ut(s,s,t));var f=new sr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:n,z2:2,autoBatch:!0,silent:!0});aa(f.shape,f.style.lineWidth),f.anid=i+"_"+r[u].tickValue,a.push(f)}return a}function gA(r,t,e,n){var i=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&n.handleAutoShown&&(o=n.handleAutoShown("axisTick")),!(!o||i.scale.isBlank())){for(var s=a.getModel("lineStyle"),u=n.tickDirection*a.get("length"),l=i.getTicksCoords(),f=w_(l,t.transform,u,ft(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function yA(r,t,e,n){var i=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||i.scale.isBlank())){var o=i.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),u=n*a.get("length"),l=ft(s.getLineStyle(),ft(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=w_(o[f],t.transform,u,l,"minorticks_"+f),v=0;v<h.length;v++)r.add(h[v])}}function mA(r,t,e,n){var i=e.axis,a=$n(n.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||i.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),u=i.getViewLabels(),l=($n(n.labelRotate,o.get("rotate"))||0)*wr/180,f=Mr.innerTextLayout(n.rotation,l,n.labelDirection),h=e.getCategories&&e.getCategories(!0),v=[],c=Mr.isLabelSilent(e),d=e.get("triggerEvent");return C(u,function(g,p){var y=i.scale.type==="ordinal"?i.scale.getRawOrdinalNumber(g.tickValue):g.tickValue,m=g.formattedLabel,_=g.rawLabel,S=o;if(h&&h[y]){var b=h[y];V(b)&&b.textStyle&&(S=new gt(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),T=i.dataToCoord(y),M=S.getShallow("align",!0)||f.textAlign,A=J(S.getShallow("alignMinLabel",!0),M),L=J(S.getShallow("alignMaxLabel",!0),M),I=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,E=J(S.getShallow("verticalAlignMinLabel",!0),I),O=J(S.getShallow("verticalAlignMaxLabel",!0),I),R=new Xt({x:T,y:n.labelOffset+n.labelDirection*s,rotation:f.rotation,silent:c,z2:10+(g.level||0),style:gn(S,{text:m,align:p===0?A:p===u.length-1?L:M,verticalAlign:p===0?E:p===u.length-1?O:I,fill:j(w)?w(i.type==="category"?_:i.type==="value"?y+"":y,p):w})});if(R.anid="label_"+y,Ds({el:R,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return R.isTruncated},value:_,tickIndex:p}}),d){var k=Mr.makeAxisEventDataBase(e);k.targetType="axisLabel",k.value=_,k.tickIndex=p,i.type==="category"&&(k.dataIndex=y),dt(R).eventData=k}t.add(R),R.updateTransform(),v.push(R),r.add(R),R.decomposeTransform()}),v}}function _A(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return SA(e,r,t),e.seriesInvolved&&xA(e,r),e}function SA(r,t,e){var n=t.getComponent("tooltip"),i=t.getComponent("axisPointer"),a=i.get("link",!0)||[],o=[];C(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var u=da(s.model),l=r.coordSysAxesInfo[u]={};r.coordSysMap[u]=s;var f=s.model,h=f.getModel("tooltip",n);if(C(s.getAxes(),Ct(g,!1,null)),s.getTooltipAxes&&n&&h.get("show")){var v=h.get("trigger")==="axis",c=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(v||c)&&C(d.baseAxes,Ct(g,c?"cross":!0,v)),c&&C(d.otherAxes,Ct(g,"cross",!1))}function g(p,y,m){var _=m.model.getModel("axisPointer",i),S=_.get("show");if(!(!S||S==="auto"&&!p&&!xf(_))){y==null&&(y=_.get("triggerTooltip")),_=p?wA(m,h,i,t,p,y):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),T=da(m.model),M=y||b||m.type==="category",A=r.axesInfo[T]={key:T,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:y,triggerEmphasis:w,involveSeries:M,snap:b,useHandle:xf(_),seriesModels:[],linkGroup:null};l[T]=A,r.seriesInvolved=r.seriesInvolved||M;var L=bA(a,m);if(L!=null){var I=o[L]||(o[L]={axesInfo:{}});I.axesInfo[T]=A,I.mapper=a[L].mapper,A.linkGroup=I}}}})}function wA(r,t,e,n,i,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],u={};C(s,function(v){u[v]=et(o.get(v))}),u.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(u.type="line");var l=u.label||(u.label={});if(l.show==null&&(l.show=!1),i==="cross"){var f=o.get(["label","show"]);if(l.show=f??!0,!a){var h=u.lineStyle=o.get("crossStyle");h&&ft(l,h.textStyle)}}return r.model.getModel("axisPointer",new gt(u,e,n))}function xA(r,t){t.eachSeries(function(e){var n=e.coordinateSystem,i=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!n||i==="none"||i===!1||i==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||C(r.coordSysAxesInfo[da(n.model)],function(o){var s=o.axis;n.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function bA(r,t){for(var e=t.model,n=t.dim,i=0;i<r.length;i++){var a=r[i]||{};if(ll(a[n+"AxisId"],e.id)||ll(a[n+"AxisIndex"],e.componentIndex)||ll(a[n+"AxisName"],e.name))return i}}function ll(r,t){return r==="all"||N(r)&&lt(r,t)>=0||r===t}function TA(r){var t=zh(r);if(t){var e=t.axisPointerModel,n=t.axis.scale,i=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=n.parse(o));var s=xf(e);a==null&&(i.status=s?"show":"hide");var u=n.getExtent().slice();u[0]>u[1]&&u.reverse(),(o==null||o>u[1])&&(o=u[1]),o<u[0]&&(o=u[0]),i.value=o,s&&(i.status=t.axis.scale.isBlank()?"hide":"show")}}function zh(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[da(r)]}function CA(r){var t=zh(r);return t&&t.axisPointerModel}function xf(r){return!!r.get(["handle","show"])}function da(r){return r.type+"||"+r.id}var Xd={},x_=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,n,i,a){this.axisPointerClass&&TA(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},t.prototype.updateAxisPointer=function(e,n,i,a){this._doUpdateAxisPointerClass(e,i,!1)},t.prototype.remove=function(e,n){var i=this._axisPointer;i&&i.remove(n)},t.prototype.dispose=function(e,n){this._disposeAxisPointer(n),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,n,i){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=CA(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,n,i):this._disposeAxisPointer(n)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,n){Xd[e]=n},t.getAxisPointerClass=function(e){return e&&Xd[e]},t.type="axis",t}(oe),bf=Mt();function MA(r,t,e,n){var i=e.axis;if(!i.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),u=n.coordinateSystem.getRect(),l=i.getTicksCoords({tickModel:a,clamp:!0});if(l.length){var f=s.length,h=bf(r).splitAreaColors,v=Z(),c=0;if(h)for(var d=0;d<l.length;d++){var g=h.get(l[d].tickValue);if(g!=null){c=(g+(f-1)*d)%f;break}}var p=i.toGlobalCoord(l[0].coord),y=o.getAreaStyle();s=N(s)?s:[s];for(var d=1;d<l.length;d++){var m=i.toGlobalCoord(l[d].coord),_=void 0,S=void 0,b=void 0,w=void 0;i.isHorizontal()?(_=p,S=u.y,b=m-_,w=u.height,p=_+b):(_=u.x,S=p,b=u.width,w=m-S,p=S+w);var T=l[d-1].tickValue;T!=null&&v.set(T,c),t.add(new Lt({anid:T!=null?"area_"+T:null,shape:{x:_,y:S,width:b,height:w},style:ft({fill:s[c]},y),autoBatch:!0,silent:!0})),c=(c+1)%f}bf(r).splitAreaColors=v}}}function DA(r){bf(r).splitAreaColors=null}var AA=["axisLine","axisTickLabel","axisName"],LA=["splitArea","splitLine","minorSplitLine"],b_=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,n,i,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new ae,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),u=wf(s,e),l=new Mr(e,B({handleAutoShown:function(h){for(var v=s.coordinateSystem.getCartesians(),c=0;c<v.length;c++)if(mf(v[c].getOtherAxis(e.axis).scale))return!0;return!1}},u));C(AA,l.add,l),this._axisGroup.add(l.getGroup()),C(LA,function(h){e.get([h,"show"])&&IA[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||ly(o,this._axisGroup,e),r.prototype.render.call(this,e,n,i,a)}},t.prototype.remove=function(){DA(this)},t.type="cartesianAxis",t}(x_),IA={splitLine:function(r,t,e,n){var i=e.axis;if(!i.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),u=a.get("showMinLine")!==!1,l=a.get("showMaxLine")!==!1;s=N(s)?s:[s];for(var f=n.coordinateSystem.getRect(),h=i.isHorizontal(),v=0,c=i.getTicksCoords({tickModel:a}),d=[],g=[],p=o.getLineStyle(),y=0;y<c.length;y++){var m=i.toGlobalCoord(c[y].coord);if(!(y===0&&!u||y===c.length-1&&!l)){var _=c[y].tickValue;h?(d[0]=m,d[1]=f.y,g[0]=m,g[1]=f.y+f.height):(d[0]=f.x,d[1]=m,g[0]=f.x+f.width,g[1]=m);var S=v++%s.length,b=new sr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:g[0],y2:g[1]},style:ft({stroke:s[S]},p),silent:!0});aa(b.shape,p.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,n){var i=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=n.coordinateSystem.getRect(),u=i.isHorizontal(),l=i.getMinorTicksCoords();if(l.length)for(var f=[],h=[],v=o.getLineStyle(),c=0;c<l.length;c++)for(var d=0;d<l[c].length;d++){var g=i.toGlobalCoord(l[c][d].coord);u?(f[0]=g,f[1]=s.y,h[0]=g,h[1]=s.y+s.height):(f[0]=s.x,f[1]=g,h[0]=s.x+s.width,h[1]=g);var p=new sr({anid:"minor_line_"+l[c][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:v,silent:!0});aa(p.shape,v.lineWidth),t.add(p)}},splitArea:function(r,t,e,n){MA(r,t,e,n)}},T_=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(b_),PA=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=T_.type,e}return t.type="yAxis",t}(b_),RA=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,n){this.group.removeAll(),e.get("show")&&this.group.add(new Lt({shape:e.coordinateSystem.getRect(),style:ft({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(oe),$d={offset:0};function EA(r){r.registerComponentView(RA),r.registerComponentModel(TD),r.registerCoordinateSystem("cartesian2d",cA),Rd(r,"x",gf,$d),Rd(r,"y",gf,$d),r.registerComponentView(T_),r.registerComponentView(PA),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var OA=1e-8;function Zd(r,t){return Math.abs(r-t)<OA}function Kd(r,t,e){var n=0,i=r[0];if(!i)return!1;for(var a=1;a<r.length;a++){var o=r[a];n+=nr(i[0],i[1],o[0],o[1],t,e),i=o}var s=r[0];return(!Zd(i[0],s[0])||!Zd(i[1],s[1]))&&(n+=nr(i[0],i[1],s[0],s[1],t,e)),n!==0}var kA=[];function fl(r,t){for(var e=0;e<r.length;e++)Ut(r[e],r[e],t)}function jd(r,t,e,n){for(var i=0;i<r.length;i++){var a=r[i];n&&(a=n.project(a)),a&&isFinite(a[0])&&isFinite(a[1])&&(yr(t,t,a),mr(e,e,a))}}function BA(r){for(var t=0,e=0,n=0,i=r.length,a=r[i-1][0],o=r[i-1][1],s=0;s<i;s++){var u=r[s][0],l=r[s][1],f=a*l-u*o;t+=f,e+=(a+u)*f,n+=(o+l)*f,a=u,o=l}return t?[e/t/3,n/t/3,t]:[r[0][0]||0,r[0][1]||0]}var C_=function(){function r(t){this.name=t}return r.prototype.setCenter=function(t){this._center=t},r.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},r}(),Qd=function(){function r(t,e){this.type="polygon",this.exterior=t,this.interiors=e}return r}(),Jd=function(){function r(t){this.type="linestring",this.points=t}return r}(),FA=function(r){H(t,r);function t(e,n,i){var a=r.call(this,e)||this;return a.type="geoJSON",a.geometries=n,a._center=i&&[i[0],i[1]],a}return t.prototype.calcCenter=function(){for(var e=this.geometries,n,i=0,a=0;a<e.length;a++){var o=e[a],s=o.exterior,u=s&&s.length;u>i&&(n=o,i=u)}if(n)return BA(n.exterior);var l=this.getBoundingRect();return[l.x+l.width/2,l.y+l.height/2]},t.prototype.getBoundingRect=function(e){var n=this._rect;if(n&&!e)return n;var i=[1/0,1/0],a=[-1/0,-1/0],o=this.geometries;return C(o,function(s){s.type==="polygon"?jd(s.exterior,i,a,e):C(s.points,function(u){jd(u,i,a,e)})}),isFinite(i[0])&&isFinite(i[1])&&isFinite(a[0])&&isFinite(a[1])||(i[0]=i[1]=a[0]=a[1]=0),n=new it(i[0],i[1],a[0]-i[0],a[1]-i[1]),e||(this._rect=n),n},t.prototype.contain=function(e){var n=this.getBoundingRect(),i=this.geometries;if(!n.contain(e[0],e[1]))return!1;t:for(var a=0,o=i.length;a<o;a++){var s=i[a];if(s.type==="polygon"){var u=s.exterior,l=s.interiors;if(Kd(u,e[0],e[1])){for(var f=0;f<(l?l.length:0);f++)if(Kd(l[f],e[0],e[1]))continue t;return!0}}}return!1},t.prototype.transformTo=function(e,n,i,a){var o=this.getBoundingRect(),s=o.width/o.height;i?a||(a=i/s):i=s*a;for(var u=new it(e,n,i,a),l=o.calculateTransform(u),f=this.geometries,h=0;h<f.length;h++){var v=f[h];v.type==="polygon"?(fl(v.exterior,l),C(v.interiors,function(c){fl(c,l)})):C(v.points,function(c){fl(c,l)})}o=this._rect,o.copy(u),this._center=[o.x+o.width/2,o.y+o.height/2]},t.prototype.cloneShallow=function(e){e==null&&(e=this.name);var n=new t(e,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},t}(C_),VI=function(r){H(t,r);function t(e,n){var i=r.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}return t.prototype.calcCenter=function(){for(var e=this._elOnlyForCalculate,n=e.getBoundingRect(),i=[n.x+n.width/2,n.y+n.height/2],a=ma(kA),o=e;o&&!o.isGeoSVGGraphicRoot;)br(a,o.getLocalTransform(),a),o=o.parent;return _a(a,a),Ut(i,i,a),i},t}(C_);function NA(r){if(!r.UTF8Encoding)return r;var t=r,e=t.UTF8Scale;e==null&&(e=1024);var n=t.features;return C(n,function(i){var a=i.geometry,o=a.encodeOffsets,s=a.coordinates;if(o)switch(a.type){case"LineString":a.coordinates=M_(s,o,e);break;case"Polygon":hl(s,o,e);break;case"MultiLineString":hl(s,o,e);break;case"MultiPolygon":C(s,function(u,l){return hl(u,o[l],e)})}}),t.UTF8Encoding=!1,t}function hl(r,t,e){for(var n=0;n<r.length;n++)r[n]=M_(r[n],t[n],e)}function M_(r,t,e){for(var n=[],i=t[0],a=t[1],o=0;o<r.length;o+=2){var s=r.charCodeAt(o)-64,u=r.charCodeAt(o+1)-64;s=s>>1^-(s&1),u=u>>1^-(u&1),s+=i,u+=a,i=s,a=u,n.push([s/e,u/e])}return n}function tp(r,t){return r=NA(r),W(Tt(r.features,function(e){return e.geometry&&e.properties&&e.geometry.coordinates.length>0}),function(e){var n=e.properties,i=e.geometry,a=[];switch(i.type){case"Polygon":var o=i.coordinates;a.push(new Qd(o[0],o.slice(1)));break;case"MultiPolygon":C(i.coordinates,function(u){u[0]&&a.push(new Qd(u[0],u.slice(1)))});break;case"LineString":a.push(new Jd([i.coordinates]));break;case"MultiLineString":a.push(new Jd(i.coordinates))}var s=new FA(n[t||"name"],a,n.cp);return s.properties=n,s})}var nn=Mt(),ep=et,cl=pt,zA=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,n,i){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,!(!i&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,u=this._handle;if(!o||o==="hide"){s&&s.hide(),u&&u.hide();return}s&&s.show(),u&&u.show();var l={};this.makeElOption(l,a,t,e,n);var f=l.graphicKey;f!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new ae,this.createPointerEl(s,l,t,e),this.createLabelEl(s,l,t,e),n.getZr().add(s);else{var v=Ct(rp,e,h);this.updatePointerEl(s,l,v),this.updateLabelEl(s,l,v,e)}ip(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var n=e.get("animation"),i=t.axis,a=i.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(n==="auto"||n==null){var s=this.animationThreshold;if(a&&i.getBandWidth()>s)return!0;if(o){var u=zh(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/u>s}return!1}return n===!0},r.prototype.makeElOption=function(t,e,n,i,a){},r.prototype.createPointerEl=function(t,e,n,i){var a=e.pointer;if(a){var o=nn(t).pointerEl=new zx[a.type](ep(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,n,i){if(e.label){var a=nn(t).labelEl=new Xt(ep(e.label));t.add(a),np(a,i)}},r.prototype.updatePointerEl=function(t,e,n){var i=nn(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,n,i){var a=nn(t).labelEl;a&&(a.setStyle(e.label.style),n(a,{x:e.label.x,y:e.label.y}),np(a,i))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){i&&n.remove(i),this._handle=null;return}var s;this._handle||(s=!0,i=this._handle=Ms(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(l){vm(l.event)},onmousedown:cl(this._onHandleDragMove,this,0,0),drift:cl(this._onHandleDragMove,this),ondragend:cl(this._onHandleDragEnd,this)}),n.add(i)),ip(i,e,!1),i.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var u=a.get("size");N(u)||(u=[u,u]),i.scaleX=u[0]/2,i.scaleY=u[1]/2,cm(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){rp(this._axisPointerModel,!e&&this._moveAnimation,this._handle,vl(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(vl(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(vl(i)),nn(n).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null),ef(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}},r}();function rp(r,t,e,n){D_(nn(e).lastProp,n)||(nn(e).lastProp=n,t?Qn(e,n,r):(e.stopAnimation(),e.attr(n)))}function D_(r,t){if(V(r)&&V(t)){var e=!0;return C(t,function(n,i){e=e&&D_(r[i],n)}),!!e}else return r===t}function np(r,t){r[t.get(["label","show"])?"show":"hide"]()}function vl(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function ip(r,t,e){var n=t.get("z"),i=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(n!=null&&(a.z=n),i!=null&&(a.zlevel=i),a.silent=e)})}function HA(r){var t=r.get("type"),e=r.getModel(t+"Style"),n;return t==="line"?(n=e.getLineStyle(),n.fill=null):t==="shadow"&&(n=e.getAreaStyle(),n.stroke=null),n}function GA(r,t,e,n,i){var a=e.get("value"),o=A_(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),u=Da(s.get("padding")||0),l=s.getFont(),f=Nf(o,l),h=i.position,v=f.width+u[1]+u[3],c=f.height+u[0]+u[2],d=i.align;d==="right"&&(h[0]-=v),d==="center"&&(h[0]-=v/2);var g=i.verticalAlign;g==="bottom"&&(h[1]-=c),g==="middle"&&(h[1]-=c/2),VA(h,v,c,n);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:gn(s,{text:o,font:l,fill:s.getTextColor(),padding:u,backgroundColor:p}),z2:10}}function VA(r,t,e,n){var i=n.getWidth(),a=n.getHeight();r[0]=Math.min(r[0]+t,i)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function A_(r,t,e,n,i){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:i.precision}),o=i.formatter;if(o){var s={value:Fh(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};C(n,function(u){var l=e.getSeriesByIndex(u.seriesIndex),f=u.dataIndexInside,h=l&&l.getDataParams(f);h&&s.seriesData.push(h)}),G(o)?a=o.replace("{value}",a):j(o)&&(a=o(s))}return a}function L_(r,t,e){var n=xr();return cs(n,n,e.rotation),zo(n,n,e.position),rh([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],n)}function WA(r,t,e,n,i,a){var o=Mr.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=i.get(["label","margin"]),GA(t,n,i,a,{position:L_(n.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function UA(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function YA(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var qA=function(r){H(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,n,i,a,o){var s=i.axis,u=s.grid,l=a.get("type"),f=ap(u,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(n,!0));if(l&&l!=="none"){var v=HA(a),c=XA[l](s,h,f);c.style=v,e.graphicKey=c.type,e.pointer=c}var d=wf(u.model,i);WA(n,e,d,i,a,o)},t.prototype.getHandleTransform=function(e,n,i){var a=wf(n.axis.grid.model,n,{labelInside:!1});a.labelMargin=i.get(["handle","margin"]);var o=L_(n.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,n,i,a){var o=i.axis,s=o.grid,u=o.getGlobalExtent(!0),l=ap(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=n[f],h[f]=Math.min(u[1],h[f]),h[f]=Math.max(u[0],h[f]);var v=(l[1]+l[0])/2,c=[v,v];c[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:c,tooltipOption:d[f]}},t}(zA);function ap(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var XA={line:function(r,t,e){var n=UA([t,e[0]],[t,e[1]],op(r));return{type:"Line",subPixelOptimize:!0,shape:n}},shadow:function(r,t,e){var n=Math.max(1,r.getBandWidth()),i=e[1]-e[0];return{type:"Rect",shape:YA([t-n/2,e[0]],[n,i],op(r))}}};function op(r){return r.dim==="x"?0:1}var $A=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(ot),ir=Mt(),ZA=C;function I_(r,t,e){if(!Y.node){var n=t.getZr();ir(n).records||(ir(n).records={}),KA(n,t);var i=ir(n).records[r]||(ir(n).records[r]={});i.handler=e}}function KA(r,t){if(ir(r).initialized)return;ir(r).initialized=!0,e("click",Ct(sp,"click")),e("mousemove",Ct(sp,"mousemove")),e("globalout",QA);function e(n,i){r.on(n,function(a){var o=JA(t);ZA(ir(r).records,function(s){s&&i(s,a,o.dispatchAction)}),jA(o.pendings,t)})}}function jA(r,t){var e=r.showTip.length,n=r.hideTip.length,i;e?i=r.showTip[e-1]:n&&(i=r.hideTip[n-1]),i&&(i.dispatchAction=null,t.dispatchAction(i))}function QA(r,t,e){r.handler("leave",null,e)}function sp(r,t,e,n){t.handler(r,e,n)}function JA(r){var t={showTip:[],hideTip:[]},e=function(n){var i=t[n.type];i?i.push(n):(n.dispatchAction=e,r.dispatchAction(n))};return{dispatchAction:e,pendings:t}}function Tf(r,t){if(!Y.node){var e=t.getZr(),n=(ir(e).records||{})[r];n&&(ir(e).records[r]=null)}}var tL=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,n,i){var a=n.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";I_("axisPointer",i,function(s,u,l){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&l({type:"updateAxisPointer",currTrigger:s,x:u&&u.offsetX,y:u&&u.offsetY})})},t.prototype.remove=function(e,n){Tf("axisPointer",n)},t.prototype.dispose=function(e,n){Tf("axisPointer",n)},t.type="axisPointer",t}(oe);function P_(r,t){var e=[],n=r.seriesIndex,i;if(n==null||!(i=t.getSeriesByIndex(n)))return{point:[]};var a=i.getData(),o=xa(a,r);if(o==null||o<0||N(o))return{point:[]};var s=a.getItemGraphicEl(o),u=i.coordinateSystem;if(i.getTooltipPosition)e=i.getTooltipPosition(o)||[];else if(u&&u.dataToPoint)if(r.isStacked){var l=u.getBaseAxis(),f=u.getOtherAxis(l),h=f.dim,v=l.dim,c=h==="x"||h==="radius"?1:0,d=a.mapDimension(v),g=[];g[c]=a.get(d,o),g[1-c]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=u.dataToPoint(g)||[]}else e=u.dataToPoint(a.getValues(W(u.dimensions,function(y){return a.mapDimension(y)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var up=Mt();function eL(r,t,e){var n=r.currTrigger,i=[r.x,r.y],a=r,o=r.dispatchAction||pt(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){Bo(i)&&(i=P_({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var u=Bo(i),l=a.axesInfo,f=s.axesInfo,h=n==="leave"||Bo(i),v={},c={},d={list:[],map:{}},g={showPointer:Ct(nL,c),showTooltip:Ct(iL,d)};C(s.coordSysMap,function(y,m){var _=u||y.containPoint(i);C(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,T=uL(l,S);if(!h&&_&&(!l||T)){var M=T&&T.value;M==null&&!u&&(M=w.pointToData(i)),M!=null&&lp(S,M,g,!1,v)}})});var p={};return C(f,function(y,m){var _=y.linkGroup;_&&!c[m]&&C(_.axesInfo,function(S,b){var w=c[b];if(S!==y&&w){var T=w.value;_.mapper&&(T=y.axis.scale.parse(_.mapper(T,fp(S),fp(y)))),p[y.key]=T}})}),C(p,function(y,m){lp(f[m],y,g,!0,v)}),aL(c,f,v),oL(d,i,r,o),sL(f,o,e),v}}function lp(r,t,e,n,i){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=rL(t,r),s=o.payloadBatch,u=o.snapToValue;s[0]&&i.seriesIndex==null&&B(i,s[0]),!n&&r.snap&&a.containData(u)&&u!=null&&(t=u),e.showPointer(r,t,s),e.showTooltip(r,o,u)}}function rL(r,t){var e=t.axis,n=e.dim,i=r,a=[],o=Number.MAX_VALUE,s=-1;return C(t.seriesModels,function(u,l){var f=u.getData().mapDimensionsAll(n),h,v;if(u.getAxisTooltipData){var c=u.getAxisTooltipData(f,r,e);v=c.dataIndices,h=c.nestestValue}else{if(v=u.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!v.length)return;h=u.getData().get(f[0],v[0])}if(!(h==null||!isFinite(h))){var d=r-h,g=Math.abs(d);g<=o&&((g<o||d>=0&&s<0)&&(o=g,s=d,i=h,a.length=0),C(v,function(p){a.push({seriesIndex:u.seriesIndex,dataIndexInside:p,dataIndex:u.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:i}}function nL(r,t,e,n){r[t.key]={value:e,payloadBatch:n}}function iL(r,t,e,n){var i=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!i.length)){var u=t.coordSys.model,l=da(u),f=r.map[l];f||(f=r.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:i.slice()})}}function aL(r,t,e){var n=e.axesInfo=[];C(t,function(i,a){var o=i.axisPointerModel.option,s=r[a];s?(!i.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!i.useHandle&&(o.status="hide"),o.status==="show"&&n.push({axisDim:i.axis.dim,axisIndex:i.axis.model.componentIndex,value:o.value})})}function oL(r,t,e,n){if(Bo(t)||!r.list.length){n({type:"hideTip"});return}var i=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:i.dataIndexInside,dataIndex:i.dataIndex,seriesIndex:i.seriesIndex,dataByCoordSys:r.list})}function sL(r,t,e){var n=e.getZr(),i="axisPointerLastHighlights",a=up(n)[i]||{},o=up(n)[i]={};C(r,function(l,f){var h=l.axisPointerModel.option;h.status==="show"&&l.triggerEmphasis&&C(h.seriesDataIndices,function(v){var c=v.seriesIndex+" | "+v.dataIndex;o[c]=v})});var s=[],u=[];C(a,function(l,f){!o[f]&&u.push(l)}),C(o,function(l,f){!a[f]&&s.push(l)}),u.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function uL(r,t){for(var e=0;e<(r||[]).length;e++){var n=r[e];if(t.axis.dim===n.axisDim&&t.axis.model.componentIndex===n.axisIndex)return n}}function fp(r){var t=r.axis.model,e={},n=e.axisDim=r.axis.dim;return e.axisIndex=e[n+"AxisIndex"]=t.componentIndex,e.axisName=e[n+"AxisName"]=t.name,e.axisId=e[n+"AxisId"]=t.id,e}function Bo(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function R_(r){x_.registerAxisPointerClass("CartesianAxisPointer",qA),r.registerComponentModel($A),r.registerComponentView(tL),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!N(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=_A(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},eL)}function WI(r){Lr(EA),Lr(R_)}function UI(r,t,e){var n=t.getBoxLayoutParams(),i=t.get("padding"),a={width:e.getWidth(),height:e.getHeight()},o=yn(n,a,i);qn(t.get("orient"),r,t.get("itemGap"),o.width,o.height),rT(r,n,a,i)}function lL(r,t){var e=Da(t.get("padding")),n=t.getItemStyle(["color","opacity"]);return n.fill=t.get("backgroundColor"),r=new Lt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:n,silent:!0,z2:-1}),r}var fL=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(ot);function E_(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function O_(r){if(Y.domSupported){for(var t=document.documentElement.style,e=0,n=r.length;e<n;e++)if(r[e]in t)return r[e]}}var k_=O_(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),hL=O_(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function B_(r,t){if(!r)return t;t=ph(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function cL(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?e[t]:null}var vL=B_(hL,"transition"),Hh=B_(k_,"transform"),dL="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(Y.transform3dSupported?"will-change:transform;":"");function pL(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function gL(r,t,e){if(!G(e)||e==="inside")return"";var n=r.get("backgroundColor"),i=r.get("borderWidth");t=sa(t);var a=pL(e),o=Math.max(Math.round(i)*1.5,6),s="",u=Hh+":",l;lt(["left","right"],a)>-1?(s+="top:50%",u+="translateY(-50%) rotate("+(l=a==="left"?-225:-45)+"deg)"):(s+="left:50%",u+="translateX(-50%) rotate("+(l=a==="top"?225:45)+"deg)");var f=l*Math.PI/180,h=o+i,v=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),c=Math.round(((v-Math.SQRT2*i)/2+Math.SQRT2*i-(v-h)/2)*100)/100;s+=";"+a+":-"+c+"px";var d=t+" solid "+i+"px;",g=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+u+";","border-bottom:"+d,"border-right:"+d,"background-color:"+n+";"];return'<div style="'+g.join("")+'"></div>'}function yL(r,t){var e="cubic-bezier(0.23,1,0.32,1)",n=" "+r/2+"s "+e,i="opacity"+n+",visibility"+n;return t||(n=" "+r+"s "+e,i+=Y.transformSupported?","+Hh+n:",left"+n+",top"+n),vL+":"+i}function hp(r,t,e){var n=r.toFixed(0)+"px",i=t.toFixed(0)+"px";if(!Y.transformSupported)return e?"top:"+i+";left:"+n+";":[["top",i],["left",n]];var a=Y.transform3dSupported,o="translate"+(a?"3d":"")+"("+n+","+i+(a?",0":"")+")";return e?"top:0;left:0;"+Hh+":"+o+";":[["top",0],["left",0],[k_,o]]}function mL(r){var t=[],e=r.get("fontSize"),n=r.getTextColor();n&&t.push("color:"+n),t.push("font:"+r.getFont());var i=J(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+i+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,u=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+u+"px "+o+"px "+a),C(["decoration","align"],function(l){var f=r.get(l);f&&t.push("text-"+l+":"+f)}),t.join(";")}function _L(r,t,e){var n=[],i=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),u=r.get("shadowOffsetX"),l=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=nm(r,"html"),v=u+"px "+l+"px "+o+"px "+s;return n.push("box-shadow:"+v),t&&i&&n.push(yL(i,e)),a&&n.push("background-color:"+a),C(["width","color","radius"],function(c){var d="border-"+c,g=ph(d),p=r.get(g);p!=null&&n.push(d+":"+p+(c==="color"?"":"px"))}),n.push(mL(f)),h!=null&&n.push("padding:"+Da(h).join("px ")+"px"),n.join(";")+";"}function cp(r,t,e,n,i){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&Nb(r,o,e,n,i)}else{r[0]=n,r[1]=i;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var SL=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,Y.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var i=this._zr=t.getZr(),a=e.appendTo,o=a&&(G(a)?document.querySelector(a):Xn(a)?a:j(a)&&a(t.getDom()));cp(this._styleCoord,i,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(n),this._api=t,this._container=o;var s=this;n.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},n.onmousemove=function(u){if(u=u||window.event,!s._enterable){var l=i.handler,f=i.painter.getViewportRoot();he(f,u,!0),l.dispatch("mousemove",u)}},n.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),n=cL(e,"position"),i=e.style;i.position!=="absolute"&&n!=="absolute"&&(i.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,a=this._styleCoord;n.innerHTML?i.cssText=dL+_L(t,!this._firstShow,this._longHide)+hp(a[0],a[1],!0)+("border-color:"+sa(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,n,i,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(G(a)&&n.get("trigger")==="item"&&!E_(n)&&(s=gL(n,i,a)),G(t))o.innerHTML=t+s;else if(t){o.innerHTML="",N(t)||(t=[t]);for(var u=0;u<t.length;u++)Xn(t[u])&&t[u].parentNode!==o&&o.appendChild(t[u]);if(s&&o.childNodes.length){var l=document.createElement("div");l.innerHTML=s,o.appendChild(l)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var n=this._styleCoord;if(cp(n,this._zr,this._container,t,e),n[0]!=null&&n[1]!=null){var i=this.el.style,a=hp(n[0],n[1]);C(a,function(o){i[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",Y.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(pt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}(),wL=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),dp(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,n,i,a){var o=this;V(t)&&jt(""),this.el&&this._zr.remove(this.el);var s=n.getModel("textStyle");this.el=new Xt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:s.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:nm(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),C(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(l){o.el.style[l]=n.get(l)}),C(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(l){o.el.style[l]=s.get(l)||0}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),n=vp(t.style);return[e.width+n.left+n.right,e.height+n.top+n.bottom]},r.prototype.moveTo=function(t,e){var n=this.el;if(n){var i=this._styleCoord;dp(i,this._zr,t,e),t=i[0],e=i[1];var a=n.style,o=gr(a.borderWidth||0),s=vp(a);n.x=t+o+s.left,n.y=e+o+s.top,n.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(pt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function gr(r){return Math.max(0,r)}function vp(r){var t=gr(r.shadowBlur||0),e=gr(r.shadowOffsetX||0),n=gr(r.shadowOffsetY||0);return{left:gr(t-e),right:gr(t+e),top:gr(t-n),bottom:gr(t+n)}}function dp(r,t,e,n){r[0]=e,r[1]=n,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var xL=new Lt({shape:{x:-1,y:-1,width:2,height:2}}),bL=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,n){if(!(Y.node||!n.getDom())){var i=e.getComponent("tooltip"),a=this._renderMode=Ow(i.get("renderMode"));this._tooltipContent=a==="richText"?new wL(n):new SL(n,{appendTo:i.get("appendToBody",!0)?"body":i.get("appendTo",!0)})}},t.prototype.render=function(e,n,i){if(!(Y.node||!i.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=n,this._api=i;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?cm(this,"_updatePosition",50,"fixRate"):ef(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,n=e.get("triggerOn");I_("itemTooltip",this._api,pt(function(i,a,o){n!=="none"&&(n.indexOf(i)>=0?this._tryShow(a,o):i==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,n=this._ecModel,i=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!i.isDisposed()&&o.manuallyShowTip(e,n,i,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,n,i,a){if(!(a.from===this.uid||Y.node||!i.getDom())){var o=pp(a,i);this._ticket="";var s=a.dataByCoordSys,u=DL(a,n,i);if(u){var l=u.el.getBoundingRect().clone();l.applyTransform(u.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:u.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=xL;f.x=a.x,f.y=a.y,f.update(),dt(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,n,i,a))return;var h=P_(a,n),v=h.point[0],c=h.point[1];v!=null&&c!=null&&this._tryShow({offsetX:v,offsetY:c,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(i.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:i.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,n,i,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(pp(a,i))},t.prototype._manuallyAxisShowTip=function(e,n,i,a){var o=a.seriesIndex,s=a.dataIndex,u=n.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||u==null)){var l=n.getSeriesByIndex(o);if(l){var f=l.getData(),h=bi([f.getItemModel(s),l,(l.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return i.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,n){var i=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(i){var s=dt(i);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var u,l;Ni(i,function(f){if(dt(f).dataIndex!=null)return u=f,!0;if(dt(f).tooltipConfig!=null)return l=f,!0},!0),u?this._showSeriesItemTooltip(e,u,n):l?this._showComponentItemTooltip(e,l,n):this._hide(n)}else this._lastDataByCoordSys=null,this._hide(n)}},t.prototype._showOrMove=function(e,n){var i=e.get("showDelay");n=pt(n,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(n,i):n()},t.prototype._showAxisTooltip=function(e,n){var i=this._ecModel,a=this._tooltipModel,o=[n.offsetX,n.offsetY],s=bi([n.tooltipOption],a),u=this._renderMode,l=[],f=la("section",{blocks:[],noHeader:!0}),h=[],v=new Nu;C(e,function(m){C(m.dataByAxis,function(_){var S=i.getComponent(_.axisDim+"Axis",_.axisIndex),b=_.value;if(!(!S||b==null)){var w=A_(b,S.axis,i,_.seriesDataIndices,_.valueLabelOpt),T=la("section",{header:w,noHeader:!Ae(w),sortBlocks:!0,blocks:[]});f.blocks.push(T),C(_.seriesDataIndices,function(M){var A=i.getSeriesByIndex(M.seriesIndex),L=M.dataIndexInside,I=A.getDataParams(L);if(!(I.dataIndex<0)){I.axisDim=_.axisDim,I.axisIndex=_.axisIndex,I.axisType=_.axisType,I.axisId=_.axisId,I.axisValue=Fh(S.axis,{value:b}),I.axisValueLabel=w,I.marker=v.makeTooltipMarker("item",sa(I.color),u);var E=Lv(A.formatTooltip(L,!0,null)),O=E.frag;if(O){var R=bi([A],a).get("valueFormatter");T.blocks.push(R?B({valueFormatter:R},O):O)}E.text&&h.push(E.text),l.push(I)}})}})}),f.blocks.reverse(),h.reverse();var c=n.position,d=s.get("order"),g=Rv(f,v,u,d,i.get("useUTC"),s.get("textStyle"));g&&h.unshift(g);var p=u==="richText"?`

`:"<br/>",y=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,l)?this._updatePosition(s,c,o[0],o[1],this._tooltipContent,l):this._showTooltipContent(s,y,l,Math.random()+"",o[0],o[1],c,null,v)})},t.prototype._showSeriesItemTooltip=function(e,n,i){var a=this._ecModel,o=dt(n),s=o.seriesIndex,u=a.getSeriesByIndex(s),l=o.dataModel||u,f=o.dataIndex,h=o.dataType,v=l.getData(h),c=this._renderMode,d=e.positionDefault,g=bi([v.getItemModel(f),l,u&&(u.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=g.get("trigger");if(!(p!=null&&p!=="item")){var y=l.getDataParams(f,h),m=new Nu;y.marker=m.makeTooltipMarker("item",sa(y.color),c);var _=Lv(l.formatTooltip(f,!1,h)),S=g.get("order"),b=g.get("valueFormatter"),w=_.frag,T=w?Rv(b?B({valueFormatter:b},w):w,m,c,S,a.get("useUTC"),g.get("textStyle")):_.text,M="item_"+l.name+"_"+f;this._showOrMove(g,function(){this._showTooltipContent(g,T,y,M,e.offsetX,e.offsetY,e.position,e.target,m)}),i({type:"showTip",dataIndexInside:f,dataIndex:v.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,n,i){var a=this._renderMode==="html",o=dt(n),s=o.tooltipConfig,u=s.option||{},l=u.encodeHTMLContent;if(G(u)){var f=u;u={content:f,formatter:f},l=!0}l&&a&&u.content&&(u=et(u),u.content=Vt(u.content));var h=[u],v=this._ecModel.getComponent(o.componentMainType,o.componentIndex);v&&h.push(v),h.push({formatter:u.content});var c=e.positionDefault,d=bi(h,this._tooltipModel,c?{position:c}:null),g=d.get("content"),p=Math.random()+"",y=new Nu;this._showOrMove(d,function(){var m=et(d.get("formatterParams")||{});this._showTooltipContent(d,g,m,p,e.offsetX,e.offsetY,e.position,n,y)}),i({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,n,i,a,o,s,u,l,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var v=e.get("formatter");u=u||e.get("position");var c=n,d=this._getNearestPoint([o,s],i,e.get("trigger"),e.get("borderColor")),g=d.color;if(v)if(G(v)){var p=e.ecModel.get("useUTC"),y=N(i)?i[0]:i,m=y&&y.axisType&&y.axisType.indexOf("time")>=0;c=v,m&&(c=Ma(y.axisValue,c,p)),c=gh(c,i,!0)}else if(j(v)){var _=pt(function(S,b){S===this._ticket&&(h.setContent(b,f,e,g,u),this._updatePosition(e,u,o,s,h,i,l))},this);this._ticket=a,c=v(i,a,_)}else c=v;h.setContent(c,f,e,g,u),h.show(e,g),this._updatePosition(e,u,o,s,h,i,l)}},t.prototype._getNearestPoint=function(e,n,i,a){if(i==="axis"||N(n))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!N(n))return{color:a||n.color||n.borderColor}},t.prototype._updatePosition=function(e,n,i,a,o,s,u){var l=this._api.getWidth(),f=this._api.getHeight();n=n||e.get("position");var h=o.getSize(),v=e.get("align"),c=e.get("verticalAlign"),d=u&&u.getBoundingRect().clone();if(u&&d.applyTransform(u.transform),j(n)&&(n=n([i,a],s,o.el,d,{viewSize:[l,f],contentSize:h.slice()})),N(n))i=Gt(n[0],l),a=Gt(n[1],f);else if(V(n)){var g=n;g.width=h[0],g.height=h[1];var p=yn(g,{width:l,height:f});i=p.x,a=p.y,v=null,c=null}else if(G(n)&&u){var y=ML(n,d,h,e.get("borderWidth"));i=y[0],a=y[1]}else{var y=TL(i,a,o,l,f,v?null:20,c?null:20);i=y[0],a=y[1]}if(v&&(i-=gp(v)?h[0]/2:v==="right"?h[0]:0),c&&(a-=gp(c)?h[1]/2:c==="bottom"?h[1]:0),E_(e)){var y=CL(i,a,o,l,f);i=y[0],a=y[1]}o.moveTo(i,a)},t.prototype._updateContentNotChangedOnAxis=function(e,n){var i=this._lastDataByCoordSys,a=this._cbParamsList,o=!!i&&i.length===e.length;return o&&C(i,function(s,u){var l=s.dataByAxis||[],f=e[u]||{},h=f.dataByAxis||[];o=o&&l.length===h.length,o&&C(l,function(v,c){var d=h[c]||{},g=v.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&v.value===d.value&&v.axisType===d.axisType&&v.axisId===d.axisId&&g.length===p.length,o&&C(g,function(y,m){var _=p[m];o=o&&y.seriesIndex===_.seriesIndex&&y.dataIndex===_.dataIndex}),a&&C(v.seriesDataIndices,function(y){var m=y.seriesIndex,_=n[m],S=a[m];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=n,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,n){Y.node||!n.getDom()||(ef(this,"_updatePosition"),this._tooltipContent.dispose(),Tf("itemTooltip",n))},t.type="tooltip",t}(oe);function bi(r,t,e){var n=t.ecModel,i;e?(i=new gt(e,n,n),i=new gt(t.option,i,n)):i=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof gt&&(o=o.get("tooltip",!0)),G(o)&&(o={formatter:o}),o&&(i=new gt(o,i,n)))}return i}function pp(r,t){return r.dispatchAction||pt(t.dispatchAction,t)}function TL(r,t,e,n,i,a,o){var s=e.getSize(),u=s[0],l=s[1];return a!=null&&(r+u+a+2>n?r-=u+a:r+=a),o!=null&&(t+l+o>i?t-=l+o:t+=o),[r,t]}function CL(r,t,e,n,i){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,n)-o,t=Math.min(t+s,i)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function ML(r,t,e,n){var i=e[0],a=e[1],o=Math.ceil(Math.SQRT2*n)+8,s=0,u=0,l=t.width,f=t.height;switch(r){case"inside":s=t.x+l/2-i/2,u=t.y+f/2-a/2;break;case"top":s=t.x+l/2-i/2,u=t.y-a-o;break;case"bottom":s=t.x+l/2-i/2,u=t.y+f+o;break;case"left":s=t.x-i-o,u=t.y+f/2-a/2;break;case"right":s=t.x+l+o,u=t.y+f/2-a/2}return[s,u]}function gp(r){return r==="center"||r==="middle"}function DL(r,t,e){var n=Xf(r).queryOptionMap,i=n.keys()[0];if(!(!i||i==="series")){var a=ba(t,i,n.get(i),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),u;if(s.group.traverse(function(l){var f=dt(l).tooltipConfig;if(f&&f.name===r.name)return u=l,!0}),u)return{componentMainType:i,componentIndex:o.componentIndex,el:u}}}}function YI(r){Lr(R_),r.registerComponentModel(fL),r.registerComponentView(bL),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Wt),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Wt)}var AL=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},Cf=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,n,i){this.mergeDefaultAndTheme(e,i),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,n){r.prototype.mergeOption.call(this,e,n),this._updateSelector(e)},t.prototype._updateSelector=function(e){var n=e.selector,i=this.ecModel;n===!0&&(n=e.selector=["all","inverse"]),N(n)&&C(n,function(a,o){G(a)&&(a={type:a}),n[o]=nt(a,AL(i,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var n=!1,i=0;i<e.length;i++){var a=e[i].get("name");if(this.isSelected(a)){this.select(a),n=!0;break}}!n&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var n=[],i=[];e.eachRawSeries(function(u){var l=u.name;i.push(l);var f;if(u.legendVisualProvider){var h=u.legendVisualProvider,v=h.getAllNames();e.isSeriesFiltered(u)||(i=i.concat(v)),v.length?n=n.concat(v):f=!0}else f=!0;f&&qf(u)&&n.push(u.name)}),this._availableNames=i;var a=this.get("data")||n,o=Z(),s=W(a,function(u){return(G(u)||wt(u))&&(u={name:u}),o.get(u.name)?null:(o.set(u.name,!0),new gt(u,this,this.ecModel))},this);this._data=Tt(s,function(u){return!!u})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var n=this.option.selected,i=this.get("selectedMode");if(i==="single"){var a=this._data;C(a,function(o){n[o.get("name")]=!1})}n[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var n=this.option.selected;n.hasOwnProperty(e)||(n[e]=!0),this[n[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,n=this.option.selected;C(e,function(i){n[i.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,n=this.option.selected;C(e,function(i){var a=i.get("name",!0);n.hasOwnProperty(a)||(n[a]=!0),n[a]=!n[a]})},t.prototype.isSelected=function(e){var n=this.option.selected;return!(n.hasOwnProperty(e)&&!n[e])&&lt(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(ot),Bn=Ct,Mf=C,So=ae,F_=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new So),this.group.add(this._selectorGroup=new So),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,n,i){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var u=e.get("selector",!0),l=e.get("selectorPosition",!0);u&&(!l||l==="auto")&&(l=s==="horizontal"?"end":"start"),this.renderInner(o,e,n,i,u,s,l);var f=e.getBoxLayoutParams(),h={width:i.getWidth(),height:i.getHeight()},v=e.get("padding"),c=yn(f,h,v),d=this.layoutInner(e,o,c,a,u,l),g=yn(ft({width:d.width,height:d.height},f),h,v);this.group.x=g.x-d.x,this.group.y=g.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=lL(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,n,i,a,o,s,u){var l=this.getContentGroup(),f=Z(),h=n.get("selectedMode"),v=[];i.eachRawSeries(function(c){!c.get("legendHoverLink")&&v.push(c.id)}),Mf(n.getData(),function(c,d){var g=c.get("name");if(!this.newlineDisabled&&(g===""||g===`
`)){var p=new So;p.newline=!0,l.add(p);return}var y=i.getSeriesByName(g)[0];if(!f.get(g))if(y){var m=y.getData(),_=m.getVisual("legendLineStyle")||{},S=m.getVisual("legendIcon"),b=m.getVisual("style"),w=this._createItem(y,g,d,c,n,e,_,b,S,h,a);w.on("click",Bn(yp,g,null,a,v)).on("mouseover",Bn(Df,y.name,null,a,v)).on("mouseout",Bn(Af,y.name,null,a,v)),i.ssr&&w.eachChild(function(T){var M=dt(T);M.seriesIndex=y.seriesIndex,M.dataIndex=d,M.ssrType="legend"}),f.set(g,!0)}else i.eachRawSeries(function(T){if(!f.get(g)&&T.legendVisualProvider){var M=T.legendVisualProvider;if(!M.containName(g))return;var A=M.indexOfName(g),L=M.getItemVisual(A,"style"),I=M.getItemVisual(A,"legendIcon"),E=ie(L.fill);E&&E[3]===0&&(E[3]=.2,L=B(B({},L),{fill:Ir(E,"rgba")}));var O=this._createItem(T,g,d,c,n,e,{},L,I,h,a);O.on("click",Bn(yp,null,g,a,v)).on("mouseover",Bn(Df,null,g,a,v)).on("mouseout",Bn(Af,null,g,a,v)),i.ssr&&O.eachChild(function(R){var k=dt(R);k.seriesIndex=T.seriesIndex,k.dataIndex=d,k.ssrType="legend"}),f.set(g,!0)}},this)},this),o&&this._createSelector(o,n,a,s,u)},t.prototype._createSelector=function(e,n,i,a,o){var s=this.getSelectorGroup();Mf(e,function(l){var f=l.type,h=new Xt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){i.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:n.id})}});s.add(h);var v=n.getModel("selectorLabel"),c=n.getModel(["emphasis","selectorLabel"]);Hx(h,{normal:v,emphasis:c},{defaultText:l.title}),qo(h)})},t.prototype._createItem=function(e,n,i,a,o,s,u,l,f,h,v){var c=e.visualDrawType,d=o.get("itemWidth"),g=o.get("itemHeight"),p=o.isSelected(n),y=a.get("symbolRotate"),m=a.get("symbolKeepAspect"),_=a.get("icon");f=_||f||"roundRect";var S=LL(f,a,u,l,c,p,v),b=new So,w=a.getModel("textStyle");if(j(e.getLegendIcon)&&(!_||_==="inherit"))b.add(e.getLegendIcon({itemWidth:d,itemHeight:g,icon:f,iconRotate:y,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}));else{var T=_==="inherit"&&e.getData().getVisual("symbol")?y==="inherit"?e.getData().getVisual("symbolRotate"):y:0;b.add(IL({itemWidth:d,itemHeight:g,icon:f,iconRotate:T,itemStyle:S.itemStyle,symbolKeepAspect:m}))}var M=s==="left"?d+5:-5,A=s,L=o.get("formatter"),I=n;G(L)&&L?I=L.replace("{name}",n??""):j(L)&&(I=L(n));var E=p?w.getTextColor():a.get("inactiveColor");b.add(new Xt({style:gn(w,{text:I,x:M,y:g/2,fill:E,align:A,verticalAlign:"middle"},{inheritColor:E})}));var O=new Lt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),R=a.getModel("tooltip");return R.get("show")&&Ds({el:O,componentModel:o,itemName:n,itemTooltipOption:R.option}),b.add(O),b.eachChild(function(k){k.silent=!0}),O.silent=!h,this.getContentGroup().add(b),qo(b),b.__legendDataIndex=i,b},t.prototype.layoutInner=function(e,n,i,a,o,s){var u=this.getContentGroup(),l=this.getSelectorGroup();qn(e.get("orient"),u,e.get("itemGap"),i.width,i.height);var f=u.getBoundingRect(),h=[-f.x,-f.y];if(l.markRedraw(),u.markRedraw(),o){qn("horizontal",l,e.get("selectorItemGap",!0));var v=l.getBoundingRect(),c=[-v.x,-v.y],d=e.get("selectorButtonGap",!0),g=e.getOrient().index,p=g===0?"width":"height",y=g===0?"height":"width",m=g===0?"y":"x";s==="end"?c[g]+=f[p]+d:h[g]+=v[p]+d,c[1-g]+=f[y]/2-v[y]/2,l.x=c[0],l.y=c[1],u.x=h[0],u.y=h[1];var _={x:0,y:0};return _[p]=f[p]+d+v[p],_[y]=Math.max(f[y],v[y]),_[m]=Math.min(0,v[m]+c[1-g]),_}else return u.x=h[0],u.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(oe);function LL(r,t,e,n,i,a,o){function s(p,y){p.lineWidth==="auto"&&(p.lineWidth=y.lineWidth>0?2:0),Mf(p,function(m,_){p[_]==="inherit"&&(p[_]=y[_])})}var u=t.getModel("itemStyle"),l=u.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=u.getShallow("decal");l.decal=!h||h==="inherit"?n.decal:cf(h,o),l.fill==="inherit"&&(l.fill=n[i]),l.stroke==="inherit"&&(l.stroke=n[f]),l.opacity==="inherit"&&(l.opacity=(i==="fill"?n:e).opacity),s(l,n);var v=t.getModel("lineStyle"),c=v.getLineStyle();if(s(c,e),l.fill==="auto"&&(l.fill=n.fill),l.stroke==="auto"&&(l.stroke=n.fill),c.stroke==="auto"&&(c.stroke=n.fill),!a){var d=t.get("inactiveBorderWidth"),g=l[f];l.lineWidth=d==="auto"?n.lineWidth>0&&g?2:0:l.lineWidth,l.fill=t.get("inactiveColor"),l.stroke=t.get("inactiveBorderColor"),c.stroke=v.get("inactiveColor"),c.lineWidth=v.get("inactiveWidth")}return{itemStyle:l,lineStyle:c}}function IL(r){var t=r.icon||"roundRect",e=Ns(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function yp(r,t,e,n){Af(r,t,e,n),e.dispatchAction({type:"legendToggleSelect",name:r??t}),Df(r,t,e,n)}function N_(r){for(var t=r.getZr().storage.getDisplayList(),e,n=0,i=t.length;n<i&&!(e=t[n].states.emphasis);)n++;return e&&e.hoverLayer}function Df(r,t,e,n){N_(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:n})}function Af(r,t,e,n){N_(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:n})}function PL(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var n=0;n<t.length;n++)if(!t[n].isSelected(e.name))return!1;return!0})}function Ti(r,t,e){var n=r==="allSelect"||r==="inverseSelect",i={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){n?s[r]():s[r](t.name),mp(s,i),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){C(i,function(u,l){s[u?"select":"unSelect"](l)}),mp(s,o)}),n?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function mp(r,t){var e=t||{};return C(r.getData(),function(n){var i=n.get("name");if(!(i===`
`||i==="")){var a=r.isSelected(i);Dr(e,i)?e[i]=e[i]&&a:e[i]=a}}),e}function RL(r){r.registerAction("legendToggleSelect","legendselectchanged",Ct(Ti,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",Ct(Ti,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",Ct(Ti,"inverseSelect")),r.registerAction("legendSelect","legendselected",Ct(Ti,"select")),r.registerAction("legendUnSelect","legendunselected",Ct(Ti,"unSelect"))}function z_(r){r.registerComponentModel(Cf),r.registerComponentView(F_),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,PL),r.registerSubTypeDefaulter("legend",function(){return"plain"}),RL(r)}var EL=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,n,i){var a=Fs(e);r.prototype.init.call(this,e,n,i),_p(this,e,a)},t.prototype.mergeOption=function(e,n){r.prototype.mergeOption.call(this,e,n),_p(this,this.option,e)},t.type="legend.scroll",t.defaultOption=Bb(Cf.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Cf);function _p(r,t,e){var n=r.getOrient(),i=[1,1];i[n.index]=0,Kn(t,e,{type:"box",ignoreSize:!!i})}var Sp=ae,dl=["width","height"],pl=["x","y"],OL=function(r){H(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new Sp),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Sp)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,n,i,a,o,s,u){var l=this;r.prototype.renderInner.call(this,e,n,i,a,o,s,u);var f=this._controllerGroup,h=n.get("pageIconSize",!0),v=N(h)?h:[h,h];d("pagePrev",0);var c=n.getModel("pageTextStyle");f.add(new Xt({name:"pageText",style:{text:"xx/xx",fill:c.getTextColor(),font:c.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(g,p){var y=g+"DataIndex",m=Ms(n.get("pageIcons",!0)[n.getOrient().name][p],{onclick:pt(l._pageGo,l,y,n,a)},{x:-v[0]/2,y:-v[1]/2,width:v[0],height:v[1]});m.name=g,f.add(m)}},t.prototype.layoutInner=function(e,n,i,a,o,s){var u=this.getSelectorGroup(),l=e.getOrient().index,f=dl[l],h=pl[l],v=dl[1-l],c=pl[1-l];o&&qn("horizontal",u,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),g=u.getBoundingRect(),p=[-g.x,-g.y],y=et(i);o&&(y[f]=i[f]-g[f]-d);var m=this._layoutContentAndController(e,a,y,l,f,v,c,h);if(o){if(s==="end")p[l]+=m[f]+d;else{var _=g[f]+d;p[l]-=_,m[h]-=_}m[f]+=g[f]+d,p[1-l]+=m[c]+m[v]/2-g[v]/2,m[v]=Math.max(m[v],g[v]),m[c]=Math.min(m[c],g[c]+p[1-l]),u.x=p[0],u.y=p[1],u.markRedraw()}return m},t.prototype._layoutContentAndController=function(e,n,i,a,o,s,u,l){var f=this.getContentGroup(),h=this._containerGroup,v=this._controllerGroup;qn(e.get("orient"),f,e.get("itemGap"),a?i.width:null,a?null:i.height),qn("horizontal",v,e.get("pageButtonItemGap",!0));var c=f.getBoundingRect(),d=v.getBoundingRect(),g=this._showController=c[o]>i[o],p=[-c.x,-c.y];n||(p[a]=f[l]);var y=[0,0],m=[-d.x,-d.y],_=J(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(g){var S=e.get("pageButtonPosition",!0);S==="end"?m[a]+=i[o]-d[o]:y[a]+=d[o]+_}m[1-a]+=c[s]/2-d[s]/2,f.setPosition(p),h.setPosition(y),v.setPosition(m);var b={x:0,y:0};if(b[o]=g?i[o]:c[o],b[s]=Math.max(c[s],d[s]),b[u]=Math.min(0,d[u]+m[1-a]),h.__rectSize=i[o],g){var w={x:0,y:0};w[o]=Math.max(i[o]-d[o]-_,0),w[s]=b[s],h.setClipPath(new Lt({shape:w})),h.__rectSize=w[o]}else v.eachChild(function(M){M.attr({invisible:!0,silent:!0})});var T=this._getPageInfo(e);return T.pageIndex!=null&&Qn(f,{x:T.contentPosition[0],y:T.contentPosition[1]},g?e:null),this._updatePageInfoView(e,T),b},t.prototype._pageGo=function(e,n,i){var a=this._getPageInfo(n)[e];a!=null&&i.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:n.id})},t.prototype._updatePageInfoView=function(e,n){var i=this._controllerGroup;C(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",v=n[h]!=null,c=i.childOfName(f);c&&(c.setStyle("fill",v?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),c.cursor=v?"pointer":"default")});var a=i.childOfName("pageText"),o=e.get("pageFormatter"),s=n.pageIndex,u=s!=null?s+1:0,l=n.pageCount;a&&o&&a.setStyle("text",G(o)?o.replace("{current}",u==null?"":u+"").replace("{total}",l==null?"":l+""):o({current:u,total:l}))},t.prototype._getPageInfo=function(e){var n=e.get("scrollDataIndex",!0),i=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=dl[o],u=pl[o],l=this._findTargetItemIndex(n),f=i.children(),h=f[l],v=f.length,c=v?1:0,d={contentPosition:[i.x,i.y],pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var g=S(h);d.contentPosition[o]=-g.s;for(var p=l+1,y=g,m=g,_=null;p<=v;++p)_=S(f[p]),(!_&&m.e>y.s+a||_&&!b(_,y.s))&&(m.i>y.i?y=m:y=_,y&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=y.i),++d.pageCount)),m=_;for(var p=l-1,y=g,m=g,_=null;p>=-1;--p)_=S(f[p]),(!_||!b(m,_.s))&&y.i<m.i&&(m=y,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=y.i),++d.pageCount,++d.pageIndex),y=_;return d;function S(w){if(w){var T=w.getBoundingRect(),M=T[u]+w[u];return{s:M,e:M+T[s],i:w.__legendDataIndex}}}function b(w,T){return w.e>=T&&w.s<=T+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var n,i=this.getContentGroup(),a;return i.eachChild(function(o,s){var u=o.__legendDataIndex;a==null&&u!=null&&(a=s),u===e&&(n=s)}),n??a},t.type="legend.scroll",t}(F_);function kL(r){r.registerAction("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;n!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(i){i.setScrollDataIndex(n)})})}function BL(r){Lr(z_),r.registerComponentModel(EL),r.registerComponentView(OL),kL(r)}function qI(r){Lr(z_),Lr(BL)}function FL(r){return Ib(null,r)}var NL={isDimensionStacked:sh,enableDataStack:Ny,getStackedDimension:zy};function zL(r,t){var e=t;t instanceof gt||(e=new gt(t));var n=f_(e);return n.setExtent(r[0],r[1]),_f(n,e),n}function HL(r){_e(r,i_)}function GL(r,t){return t=t||{},gn(r,null,null,t.state!=="normal")}const VL=Object.freeze(Object.defineProperty({__proto__:null,createDimensions:Sb,createList:FL,createScale:zL,createSymbol:Ns,createTextStyle:GL,dataStack:NL,enableHoverEmphasis:qo,getECData:dt,getLayoutRect:yn,mixinAxisModelCommonMethods:HL},Symbol.toStringTag,{value:"Module"})),WL=Object.freeze(Object.defineProperty({__proto__:null,MAX_SAFE_INTEGER:mw,asc:dw,getPercentWithPrecision:pw,getPixelPrecision:Dg,getPrecision:Ve,getPrecisionSafe:Mg,isNumeric:Lg,isRadianAroundZero:ra,linearMap:Bl,nice:Yf,numericToNumber:na,parseDate:me,quantile:Sw,quantity:Ag,quantityExponent:ps,reformIntervals:ww,remRadian:Uf,round:It},Symbol.toStringTag,{value:"Module"})),UL=Object.freeze(Object.defineProperty({__proto__:null,format:Ma,parse:me},Symbol.toStringTag,{value:"Module"})),YL=Object.freeze(Object.defineProperty({__proto__:null,Arc:Ca,BezierCurve:Ts,BoundingRect:it,Circle:Ta,CompoundPath:jg,Ellipse:_s,Group:ae,Image:Pr,IncrementalDisplayable:ey,Line:sr,LinearGradient:Jg,Polygon:xs,Polyline:bs,RadialGradient:ty,Rect:Lt,Ring:ws,Sector:Ss,Text:Xt,clipPointsByRect:fy,clipRectByRect:hy,createIcon:Ms,extendPath:iy,extendShape:ny,getShapeClass:ay,getTransform:uy,initProps:Jf,makeImage:th,makePath:Cs,mergePath:sy,registerShape:Se,resizePath:eh,updateProps:Qn},Symbol.toStringTag,{value:"Module"})),qL=Object.freeze(Object.defineProperty({__proto__:null,addCommas:dh,capitalFirst:tT,encodeHTML:Vt,formatTime:Jb,formatTpl:gh,getTextRect:Qb,getTooltipMarker:Ky,normalizeCssArray:Da,toCamelCase:ph,truncateText:z1},Symbol.toStringTag,{value:"Module"})),XL=Object.freeze(Object.defineProperty({__proto__:null,bind:pt,clone:et,curry:Ct,defaults:ft,each:C,extend:B,filter:Tt,indexOf:lt,inherits:Ef,isArray:N,isFunction:j,isObject:V,isString:G,map:W,merge:nt,reduce:Ue},Symbol.toStringTag,{value:"Module"}));function $L(r){var t=ot.extend(r);return ot.registerClass(t),t}function ZL(r){var t=oe.extend(r);return oe.registerClass(t),t}function KL(r){var t=Ye.extend(r);return Ye.registerClass(t),t}function jL(r){var t=We.extend(r);return We.registerClass(t),t}const XI=Object.freeze(Object.defineProperty({__proto__:null,Axis:S_,ChartView:We,ComponentModel:ot,ComponentView:oe,List:By,Model:gt,PRIORITY:Wm,SeriesModel:Ye,color:wS,connect:pD,dataTool:xD,dependencies:jM,disConnect:gD,disconnect:jm,dispose:yD,env:Y,extendChartView:jL,extendComponentModel:$L,extendComponentView:ZL,extendSeriesModel:KL,format:qL,getCoordinateSystemDimensions:_D,getInstanceByDom:Ah,getInstanceById:mD,getMap:wD,graphic:YL,helper:VL,init:dD,innerDrawElementOnCanvas:Th,matrix:N1,number:WL,parseGeoJSON:tp,parseGeoJson:tp,registerAction:_n,registerCoordinateSystem:t_,registerLayout:e_,registerLoading:Eh,registerLocale:fh,registerMap:r_,registerPostInit:Qm,registerPostUpdate:Jm,registerPreprocessor:Ih,registerProcessor:Ph,registerTheme:Lh,registerTransform:n_,registerUpdateLifecycle:Hs,registerVisual:Rr,setCanvasCreator:SD,setPlatformAPI:zp,throttle:wh,time:UL,use:Lr,util:XL,vector:iS,version:KM,zrUtil:S1,zrender:VC},Symbol.toStringTag,{value:"Module"}));function wp(r,t,e){var n=or.createCanvas(),i=t.getWidth(),a=t.getHeight(),o=n.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=i+"px",o.height=a+"px",n.setAttribute("data-zr-dom-id",r)),n.width=i*e,n.height=a*e,n}var gl=function(r){H(t,r);function t(e,n,i){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;i=i||Uo,typeof e=="string"?o=wp(e,n,i):V(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(kf(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=n,a.dpr=i,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=wp("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,n,i,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,u=!1,l=new it(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new it(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,T=0;T<o.length;++T){var M=o[T];if(M.intersect(m)){var A=new it(0,0,0,0);A.copy(M),A.union(m),o[T]=A,S=!0;break}else if(u){l.copy(m),l.union(M);var L=m.width*m.height,I=M.width*M.height,E=l.width*l.height,O=E-L-I;O<b&&(b=O,w=T)}}if(u&&(o[w].union(m),S=!0),!S){var _=new it(0,0,0,0);_.copy(m),o.push(_)}u||(u=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var v=e[h];if(v){var c=v.shouldBePainted(i,a,!0,!0),d=v.__isRendered&&(v.__dirty&re||!c)?v.getPrevPaintRect():null;d&&f(d);var g=c&&(v.__dirty&re||!v.__isRendered)?v.getPaintRect():null;g&&f(g)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var v=n[h],c=v&&v.shouldBePainted(i,a,!0,!0);if(v&&(!c||!v.__zr)&&v.__isRendered){var d=v.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var y=h+1;y<o.length;)o[h].intersect(o[y])?(p=!0,o[h].union(o[y]),o.splice(y,1)):y++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,n){var i=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=n+"px"),a.width=e*i,a.height=n*i,s&&(s.width=e*i,s.height=n*i,i!==1&&this.ctxBack.scale(i,i))},t.prototype.clear=function(e,n,i){var a=this.dom,o=this.ctx,s=a.width,u=a.height;n=n||this.clearColor;var l=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,v=this;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,u/h));var c=this.domBack;function d(g,p,y,m){if(o.clearRect(g,p,y,m),n&&n!=="transparent"){var _=void 0;if(pa(n)){var S=n.global||n.__width===y&&n.__height===m;_=S&&n.__canvasGradient||ff(o,n,{x:0,y:0,width:y,height:m}),n.__canvasGradient=_,n.__width=y,n.__height=m}else Vp(n)&&(n.scaleX=n.scaleX||h,n.scaleY=n.scaleY||h,_=hf(o,n,{dirty:function(){v.setUnpainted(),v.painter.refresh()}}));o.save(),o.fillStyle=_||n,o.fillRect(g,p,y,m),o.restore()}l&&(o.save(),o.globalAlpha=f,o.drawImage(c,g,p,y,m),o.restore())}!i||l?d(0,0,s,u):i.length&&C(i,function(g){d(g.x*h,g.y*h,g.width*h,g.height*h)})},t}(qe),xp=1e5,rn=314159,wo=.01,QL=.001;function JL(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function tI(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var eI=function(){function r(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=n=B({},n||{}),this.dpr=n.devicePixelRatio||Uo,this._singleCanvas=a,this.root=t;var o=t.style;o&&(kf(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(a){var f=t,h=f.width,v=f.height;n.width!=null&&(h=n.width),n.height!=null&&(v=n.height),this.dpr=n.devicePixelRatio||1,f.width=h*this.dpr,f.height=v*this.dpr,this._width=h,this._height=v;var c=new gl(f,this,this.dpr);c.__builtin__=!0,c.initContext(),u[rn]=c,c.zlevel=rn,s.push(rn),this._domRoot=t}else{this._width=co(t,0,n),this._height=co(t,1,n);var l=this._domRoot=tI(this._width,this._height);t.appendChild(l)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var a=0;a<i.length;a++){var o=i[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var u=a===0?this._backgroundColor:null;s.refresh(u)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),!!e){for(var i={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(n||(n=this._hoverlayer=this.getLayer(xp)),a||(a=n.ctx,a.save()),un(a,s,i,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(xp)},r.prototype.paintOne=function(t,e){Th(t,e)},r.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,n),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(l){l.afterBrush&&l.afterBrush()});else{var u=this;Jo(function(){u._paintList(t,e,n,i)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(rn).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer(function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)})},r.prototype._doPaintList=function(t,e,n){for(var i=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||n)&&a.push(l)}for(var f=!0,h=!1,v=function(g){var p=a[g],y=p.ctx,m=o&&p.createRepaintRects(t,e,c._width,c._height),_=n?p.__startIndex:p.__drawIndex,S=!n&&p.incremental&&Date.now,b=S&&Date.now(),w=p.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,w,m);else if(_===p.__startIndex){var T=t[_];(!T.incremental||!T.notClear||n)&&p.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=p.__startIndex);var M,A=function(O){var R={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(M=_;M<p.__endIndex;M++){var k=t[M];if(k.__inHover&&(h=!0),i._doPaintEl(k,p,o,O,R,M===p.__endIndex-1),S){var F=Date.now()-b;if(F>15)break}}R.prevElClipPaths&&y.restore()};if(m)if(m.length===0)M=p.__endIndex;else for(var L=c.dpr,I=0;I<m.length;++I){var E=m[I];y.save(),y.beginPath(),y.rect(E.x*L,E.y*L,E.width*L,E.height*L),y.clip(),A(E),y.restore()}else y.save(),A(),y.restore();p.__drawIndex=M,p.__drawIndex<p.__endIndex&&(f=!1)},c=this,d=0;d<a.length;d++)v(d);return Y.wxa&&C(this._layers,function(g){g&&g.ctx&&g.ctx.draw&&g.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,n,i,a,o){var s=e.ctx;if(n){var u=t.getPaintRect();(!i||u&&u.intersect(i))&&(un(s,t,a,o),t.setPrevPaintRect(u))}else un(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=rn);var n=this._layers[t];return n||(n=new gl("zr_"+t,this,this.dpr),n.zlevel=t,n.__builtin__=!0,this._layerConfig[t]?nt(n,this._layerConfig[t],!0):this._layerConfig[t-wo]&&nt(n,this._layerConfig[t-wo],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},r.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,a=i.length,o=this._domRoot,s=null,u=-1;if(!n[t]&&JL(e)){if(a>0&&t>i[0]){for(u=0;u<a-1&&!(i[u]<t&&i[u+1]>t);u++);s=n[i[u]]}if(i.splice(u+1,0,t),n[t]=e,!e.virtual)if(s){var l=s.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var a=n[i];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var a=n[i],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var a=n[i],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,v){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var n=1;n<t.length;n++){var i=t[n];if(i.zlevel!==t[n-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,u;for(u=0;u<t.length;u++){var i=t[u],l=i.zlevel,f=void 0;s!==l&&(s=l,o=0),i.incremental?(f=this.getLayer(l+QL,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(l+(o>0?wo:0),this._needsManuallyCompositing),f.__builtin__||us("ZLevel "+l+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==u&&(f.__dirty=!0),f.__startIndex=u,f.incremental?f.__drawIndex=-1:f.__drawIndex=u,e(u),a=f),i.__dirty&re&&!i.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=u))}e(u),this.eachBuiltinLayer(function(h,v){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,C(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?nt(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var a=this._zlevelList[i];if(a===t||a===t+wo){var o=this._layers[a];nt(o,n[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(lt(n,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,a=this.root;if(t!=null&&(i.width=t),e!=null&&(i.height=e),t=co(a,0,i),e=co(a,1,i),n.style.display="",this._width!==t||e!==this._height){n.style.width=t+"px",n.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(rn).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[rn].dom;var e=new gl("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?n.drawImage(h.dom,0,0,i,a):h.renderToCanvas&&(n.save(),h.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,l=s.length;u<l;u++){var f=s[u];un(n,f,o,u===l-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();function $I(r){r.registerPainter("canvas",eI)}var xo={};const H_=j0(Q0);var Je={},tr={},yl={},bp;function rI(){return bp||(bp=1,function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var t=1,e=function(){return"".concat(t++)};r.default=e}(yl)),yl}var Ci={},Mi={},ml={},Tp;function G_(){return Tp||(Tp=1,function(r){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var t=function(n){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:60,a=null;return function(){for(var o=this,s=arguments.length,u=new Array(s),l=0;l<s;l++)u[l]=arguments[l];clearTimeout(a),a=setTimeout(function(){n.apply(o,u)},i)}};r.default=t}(ml)),ml}var er={},Cp;function Gh(){if(Cp)return er;Cp=1,Object.defineProperty(er,"__esModule",{value:!0}),er.SizeSensorId=er.SensorTabIndex=er.SensorClassName=void 0;var r="size-sensor-id";er.SizeSensorId=r;var t="size-sensor-object";er.SensorClassName=t;var e="-1";return er.SensorTabIndex=e,er}var Mp;function nI(){if(Mp)return Mi;Mp=1,Object.defineProperty(Mi,"__esModule",{value:!0}),Mi.createSensor=void 0;var r=e(G_()),t=Gh();function e(i){return i&&i.__esModule?i:{default:i}}var n=function(a,o){var s=void 0,u=[],l=function(){getComputedStyle(a).position==="static"&&(a.style.position="relative");var g=document.createElement("object");return g.onload=function(){g.contentDocument.defaultView.addEventListener("resize",f),f()},g.style.display="block",g.style.position="absolute",g.style.top="0",g.style.left="0",g.style.height="100%",g.style.width="100%",g.style.overflow="hidden",g.style.pointerEvents="none",g.style.zIndex="-1",g.style.opacity="0",g.setAttribute("class",t.SensorClassName),g.setAttribute("tabindex",t.SensorTabIndex),g.type="text/html",a.appendChild(g),g.data="about:blank",g},f=(0,r.default)(function(){u.forEach(function(d){d(a)})}),h=function(g){s||(s=l()),u.indexOf(g)===-1&&u.push(g)},v=function(){s&&s.parentNode&&(s.contentDocument&&s.contentDocument.defaultView.removeEventListener("resize",f),s.parentNode.removeChild(s),a.removeAttribute(t.SizeSensorId),s=void 0,u=[],o&&o())},c=function(g){var p=u.indexOf(g);p!==-1&&u.splice(p,1),u.length===0&&s&&v()};return{element:a,bind:h,destroy:v,unbind:c}};return Mi.createSensor=n,Mi}var Di={},Dp;function iI(){if(Dp)return Di;Dp=1,Object.defineProperty(Di,"__esModule",{value:!0}),Di.createSensor=void 0;var r=Gh(),t=e(G_());function e(i){return i&&i.__esModule?i:{default:i}}var n=function(a,o){var s=void 0,u=[],l=(0,t.default)(function(){u.forEach(function(d){d(a)})}),f=function(){var g=new ResizeObserver(l);return g.observe(a),l(),g},h=function(g){s||(s=f()),u.indexOf(g)===-1&&u.push(g)},v=function(){s.disconnect(),u=[],s=void 0,a.removeAttribute(r.SizeSensorId),o&&o()},c=function(g){var p=u.indexOf(g);p!==-1&&u.splice(p,1),u.length===0&&s&&v()};return{element:a,bind:h,destroy:v,unbind:c}};return Di.createSensor=n,Di}var Ap;function aI(){if(Ap)return Ci;Ap=1,Object.defineProperty(Ci,"__esModule",{value:!0}),Ci.createSensor=void 0;var r=nI(),t=iI(),e=typeof ResizeObserver<"u"?t.createSensor:r.createSensor;return Ci.createSensor=e,Ci}var Lp;function oI(){if(Lp)return tr;Lp=1,Object.defineProperty(tr,"__esModule",{value:!0}),tr.removeSensor=tr.getSensor=tr.Sensors=void 0;var r=n(rI()),t=aI(),e=Gh();function n(u){return u&&u.__esModule?u:{default:u}}var i={};tr.Sensors=i;function a(u){u&&i[u]&&delete i[u]}var o=function(l){var f=l.getAttribute(e.SizeSensorId);if(f&&i[f])return i[f];var h=(0,r.default)();l.setAttribute(e.SizeSensorId,h);var v=(0,t.createSensor)(l,function(){return a(h)});return i[h]=v,v};tr.getSensor=o;var s=function(l){var f=l.element.getAttribute(e.SizeSensorId);l.destroy(),a(f)};return tr.removeSensor=s,tr}var Ip;function sI(){if(Ip)return Je;Ip=1,Object.defineProperty(Je,"__esModule",{value:!0}),Je.ver=Je.clear=Je.bind=void 0;var r=oI(),t=function(a,o){var s=(0,r.getSensor)(a);return s.bind(o),function(){s.unbind(o)}};Je.bind=t;var e=function(a){var o=(0,r.getSensor)(a);(0,r.removeSensor)(o)};Je.clear=e;var n="1.0.2";return Je.ver=n,Je}var Ai={},Pp;function uI(){if(Pp)return Ai;Pp=1,Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.pick=void 0;function r(t,e){var n={};return e.forEach(function(i){n[i]=t[i]}),n}return Ai.pick=r,Ai}var Li={},Rp;function lI(){if(Rp)return Li;Rp=1,Object.defineProperty(Li,"__esModule",{value:!0}),Li.isFunction=void 0;function r(t){return typeof t=="function"}return Li.isFunction=r,Li}var Ii={},Ep;function fI(){if(Ep)return Ii;Ep=1,Object.defineProperty(Ii,"__esModule",{value:!0}),Ii.isString=void 0;function r(t){return typeof t=="string"}return Ii.isString=r,Ii}var Pi={},_l,Op;function hI(){return Op||(Op=1,_l=function r(t,e){if(t===e)return!0;if(t&&e&&typeof t=="object"&&typeof e=="object"){if(t.constructor!==e.constructor)return!1;var n,i,a;if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(i=n;i--!==0;)if(!r(t[i],e[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===e.source&&t.flags===e.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===e.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===e.toString();if(a=Object.keys(t),n=a.length,n!==Object.keys(e).length)return!1;for(i=n;i--!==0;)if(!Object.prototype.hasOwnProperty.call(e,a[i]))return!1;for(i=n;i--!==0;){var o=a[i];if(!r(t[o],e[o]))return!1}return!0}return t!==t&&e!==e}),_l}var kp;function cI(){if(kp)return Pi;kp=1,Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.isEqual=void 0;var r=H_,t=(0,r.__importDefault)(hI());return Pi.isEqual=t.default,Pi}var Bp;function vI(){if(Bp)return xo;Bp=1,Object.defineProperty(xo,"__esModule",{value:!0});var r=H_,t=(0,r.__importStar)(J0()),e=sI(),n=uI(),i=lI(),a=fI(),o=cI(),s=function(u){(0,r.__extends)(l,u);function l(f){var h=u.call(this,f)||this;return h.echarts=f.echarts,h.ele=null,h.isInitialResize=!0,h}return l.prototype.componentDidMount=function(){this.renderNewEcharts()},l.prototype.componentDidUpdate=function(f){var h=this.props.shouldSetOption;if(!((0,i.isFunction)(h)&&!h(f,this.props))){if(!(0,o.isEqual)(f.theme,this.props.theme)||!(0,o.isEqual)(f.opts,this.props.opts)||!(0,o.isEqual)(f.onEvents,this.props.onEvents)){this.dispose(),this.renderNewEcharts();return}var v=["option","notMerge","lazyUpdate","showLoading","loadingOption"];(0,o.isEqual)((0,n.pick)(this.props,v),(0,n.pick)(f,v))||this.updateEChartsOption(),(!(0,o.isEqual)(f.style,this.props.style)||!(0,o.isEqual)(f.className,this.props.className))&&this.resize()}},l.prototype.componentWillUnmount=function(){this.dispose()},l.prototype.getEchartsInstance=function(){return this.echarts.getInstanceByDom(this.ele)||this.echarts.init(this.ele,this.props.theme,this.props.opts)},l.prototype.dispose=function(){if(this.ele){try{(0,e.clear)(this.ele)}catch(f){console.warn(f)}this.echarts.dispose(this.ele)}},l.prototype.renderNewEcharts=function(){var f=this,h=this.props,v=h.onEvents,c=h.onChartReady,d=this.updateEChartsOption();this.bindEvents(d,v||{}),(0,i.isFunction)(c)&&c(d),this.ele&&(0,e.bind)(this.ele,function(){f.resize()})},l.prototype.bindEvents=function(f,h){function v(d,g){(0,a.isString)(d)&&(0,i.isFunction)(g)&&f.on(d,function(p){g(p,f)})}for(var c in h)Object.prototype.hasOwnProperty.call(h,c)&&v(c,h[c])},l.prototype.updateEChartsOption=function(){var f=this.props,h=f.option,v=f.notMerge,c=v===void 0?!1:v,d=f.lazyUpdate,g=d===void 0?!1:d,p=f.showLoading,y=f.loadingOption,m=y===void 0?null:y,_=this.getEchartsInstance();return _.setOption(h,c,g),p?_.showLoading(m):_.hideLoading(),_},l.prototype.resize=function(){var f=this.getEchartsInstance();if(!this.isInitialResize)try{f.resize()}catch(h){console.warn(h)}this.isInitialResize=!1},l.prototype.render=function(){var f=this,h=this.props,v=h.style,c=h.className,d=c===void 0?"":c,g=(0,r.__assign)({height:300},v);return t.default.createElement("div",{ref:function(p){f.ele=p},style:g,className:"echarts-for-react "+d})},l}(t.PureComponent);return xo.default=s,xo}var dI=vI();const pI=Np(dI);var zi={exports:{}};zi.exports;var Fp;function gI(){return Fp||(Fp=1,function(r,t){var e=200,n="__lodash_hash_undefined__",i=800,a=16,o=9007199254740991,s="[object Arguments]",u="[object Array]",l="[object AsyncFunction]",f="[object Boolean]",h="[object Date]",v="[object Error]",c="[object Function]",d="[object GeneratorFunction]",g="[object Map]",p="[object Number]",y="[object Null]",m="[object Object]",_="[object Proxy]",S="[object RegExp]",b="[object Set]",w="[object String]",T="[object Undefined]",M="[object WeakMap]",A="[object ArrayBuffer]",L="[object DataView]",I="[object Float32Array]",E="[object Float64Array]",O="[object Int8Array]",R="[object Int16Array]",k="[object Int32Array]",F="[object Uint8Array]",K="[object Uint8ClampedArray]",U="[object Uint16Array]",q="[object Uint32Array]",Q=/[\\^$.*+?()[\]{}|]/g,ht=/^\[object .+?Constructor\]$/,st=/^(?:0|[1-9]\d*)$/,$={};$[I]=$[E]=$[O]=$[R]=$[k]=$[F]=$[K]=$[U]=$[q]=!0,$[s]=$[u]=$[A]=$[f]=$[L]=$[h]=$[v]=$[c]=$[g]=$[p]=$[m]=$[S]=$[b]=$[w]=$[M]=!1;var Ft=typeof Na=="object"&&Na&&Na.Object===Object&&Na,Oe=typeof self=="object"&&self&&self.Object===Object&&self,te=Ft||Oe||Function("return this")(),ke=t&&!t.nodeType&&t,se=ke&&!0&&r&&!r.nodeType&&r,Pt=se&&se.exports===ke,xt=Pt&&Ft.process,X=function(){try{var x=se&&se.require&&se.require("util").types;return x||xt&&xt.binding&&xt.binding("util")}catch{}}(),tt=X&&X.isTypedArray;function Ze(x,D,P){switch(P.length){case 0:return x.call(D);case 1:return x.call(D,P[0]);case 2:return x.call(D,P[0],P[1]);case 3:return x.call(D,P[0],P[1],P[2])}return x.apply(D,P)}function Dt(x,D){for(var P=-1,z=Array(x);++P<x;)z[P]=D(P);return z}function Aa(x){return function(D){return x(D)}}function La(x,D){return x==null?void 0:x[D]}function V_(x,D){return function(P){return x(D(P))}}var W_=Array.prototype,U_=Function.prototype,Ia=Object.prototype,Us=te["__core-js_shared__"],Pa=U_.toString,lr=Ia.hasOwnProperty,Vh=function(){var x=/[^.]+$/.exec(Us&&Us.keys&&Us.keys.IE_PROTO||"");return x?"Symbol(src)_1."+x:""}(),Wh=Ia.toString,Y_=Pa.call(Object),q_=RegExp("^"+Pa.call(lr).replace(Q,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ra=Pt?te.Buffer:void 0,Uh=te.Symbol,Yh=te.Uint8Array;Ra&&Ra.allocUnsafe;var qh=V_(Object.getPrototypeOf,Object),Xh=Object.create,X_=Ia.propertyIsEnumerable,$_=W_.splice,Er=Uh?Uh.toStringTag:void 0,Ea=function(){try{var x=Xs(Object,"defineProperty");return x({},"",{}),x}catch{}}(),Z_=Ra?Ra.isBuffer:void 0,$h=Math.max,K_=Date.now,Zh=Xs(te,"Map"),ri=Xs(Object,"create"),j_=function(){function x(){}return function(D){if(!kr(D))return{};if(Xh)return Xh(D);x.prototype=D;var P=new x;return x.prototype=void 0,P}}();function Or(x){var D=-1,P=x==null?0:x.length;for(this.clear();++D<P;){var z=x[D];this.set(z[0],z[1])}}function Q_(){this.__data__=ri?ri(null):{},this.size=0}function J_(x){var D=this.has(x)&&delete this.__data__[x];return this.size-=D?1:0,D}function t0(x){var D=this.__data__;if(ri){var P=D[x];return P===n?void 0:P}return lr.call(D,x)?D[x]:void 0}function e0(x){var D=this.__data__;return ri?D[x]!==void 0:lr.call(D,x)}function r0(x,D){var P=this.__data__;return this.size+=this.has(x)?0:1,P[x]=ri&&D===void 0?n:D,this}Or.prototype.clear=Q_,Or.prototype.delete=J_,Or.prototype.get=t0,Or.prototype.has=e0,Or.prototype.set=r0;function Ke(x){var D=-1,P=x==null?0:x.length;for(this.clear();++D<P;){var z=x[D];this.set(z[0],z[1])}}function n0(){this.__data__=[],this.size=0}function i0(x){var D=this.__data__,P=Oa(D,x);if(P<0)return!1;var z=D.length-1;return P==z?D.pop():$_.call(D,P,1),--this.size,!0}function a0(x){var D=this.__data__,P=Oa(D,x);return P<0?void 0:D[P][1]}function o0(x){return Oa(this.__data__,x)>-1}function s0(x,D){var P=this.__data__,z=Oa(P,x);return z<0?(++this.size,P.push([x,D])):P[z][1]=D,this}Ke.prototype.clear=n0,Ke.prototype.delete=i0,Ke.prototype.get=a0,Ke.prototype.has=o0,Ke.prototype.set=s0;function Sn(x){var D=-1,P=x==null?0:x.length;for(this.clear();++D<P;){var z=x[D];this.set(z[0],z[1])}}function u0(){this.size=0,this.__data__={hash:new Or,map:new(Zh||Ke),string:new Or}}function l0(x){var D=Ba(this,x).delete(x);return this.size-=D?1:0,D}function f0(x){return Ba(this,x).get(x)}function h0(x){return Ba(this,x).has(x)}function c0(x,D){var P=Ba(this,x),z=P.size;return P.set(x,D),this.size+=P.size==z?0:1,this}Sn.prototype.clear=u0,Sn.prototype.delete=l0,Sn.prototype.get=f0,Sn.prototype.has=h0,Sn.prototype.set=c0;function wn(x){var D=this.__data__=new Ke(x);this.size=D.size}function v0(){this.__data__=new Ke,this.size=0}function d0(x){var D=this.__data__,P=D.delete(x);return this.size=D.size,P}function p0(x){return this.__data__.get(x)}function g0(x){return this.__data__.has(x)}function y0(x,D){var P=this.__data__;if(P instanceof Ke){var z=P.__data__;if(!Zh||z.length<e-1)return z.push([x,D]),this.size=++P.size,this;P=this.__data__=new Sn(z)}return P.set(x,D),this.size=P.size,this}wn.prototype.clear=v0,wn.prototype.delete=d0,wn.prototype.get=p0,wn.prototype.has=g0,wn.prototype.set=y0;function m0(x,D){var P=Ks(x),z=!P&&Zs(x),rt=!P&&!z&&tc(x),ct=!P&&!z&&!rt&&rc(x),_t=P||z||rt||ct,at=_t?Dt(x.length,String):[],St=at.length;for(var xe in x)_t&&(xe=="length"||rt&&(xe=="offset"||xe=="parent")||ct&&(xe=="buffer"||xe=="byteLength"||xe=="byteOffset")||Qh(xe,St))||at.push(xe);return at}function Ys(x,D,P){(P!==void 0&&!Fa(x[D],P)||P===void 0&&!(D in x))&&qs(x,D,P)}function _0(x,D,P){var z=x[D];(!(lr.call(x,D)&&Fa(z,P))||P===void 0&&!(D in x))&&qs(x,D,P)}function Oa(x,D){for(var P=x.length;P--;)if(Fa(x[P][0],D))return P;return-1}function qs(x,D,P){D=="__proto__"&&Ea?Ea(x,D,{configurable:!0,enumerable:!0,value:P,writable:!0}):x[D]=P}var S0=E0();function ka(x){return x==null?x===void 0?T:y:Er&&Er in Object(x)?O0(x):H0(x)}function Kh(x){return ni(x)&&ka(x)==s}function w0(x){if(!kr(x)||N0(x))return!1;var D=Qs(x)?q_:ht;return D.test(U0(x))}function x0(x){return ni(x)&&ec(x.length)&&!!$[ka(x)]}function b0(x){if(!kr(x))return z0(x);var D=Jh(x),P=[];for(var z in x)z=="constructor"&&(D||!lr.call(x,z))||P.push(z);return P}function jh(x,D,P,z,rt){x!==D&&S0(D,function(ct,_t){if(rt||(rt=new wn),kr(ct))T0(x,D,_t,P,jh,z,rt);else{var at=z?z($s(x,_t),ct,_t+"",x,D,rt):void 0;at===void 0&&(at=ct),Ys(x,_t,at)}},nc)}function T0(x,D,P,z,rt,ct,_t){var at=$s(x,P),St=$s(D,P),xe=_t.get(St);if(xe){Ys(x,P,xe);return}var ee=ct?ct(at,St,P+"",x,D,_t):void 0,ii=ee===void 0;if(ii){var Js=Ks(St),tu=!Js&&tc(St),ac=!Js&&!tu&&rc(St);ee=St,Js||tu||ac?Ks(at)?ee=at:Y0(at)?ee=I0(at):tu?(ii=!1,ee=D0(St)):ac?(ii=!1,ee=L0(St)):ee=[]:q0(St)||Zs(St)?(ee=at,Zs(at)?ee=X0(at):(!kr(at)||Qs(at))&&(ee=k0(St))):ii=!1}ii&&(_t.set(St,ee),rt(ee,St,z,ct,_t),_t.delete(St)),Ys(x,P,ee)}function C0(x,D){return V0(G0(x,D,ic),x+"")}var M0=Ea?function(x,D){return Ea(x,"toString",{configurable:!0,enumerable:!1,value:Z0(D),writable:!0})}:ic;function D0(x,D){return x.slice()}function A0(x){var D=new x.constructor(x.byteLength);return new Yh(D).set(new Yh(x)),D}function L0(x,D){var P=A0(x.buffer);return new x.constructor(P,x.byteOffset,x.length)}function I0(x,D){var P=-1,z=x.length;for(D||(D=Array(z));++P<z;)D[P]=x[P];return D}function P0(x,D,P,z){var rt=!P;P||(P={});for(var ct=-1,_t=D.length;++ct<_t;){var at=D[ct],St=void 0;St===void 0&&(St=x[at]),rt?qs(P,at,St):_0(P,at,St)}return P}function R0(x){return C0(function(D,P){var z=-1,rt=P.length,ct=rt>1?P[rt-1]:void 0,_t=rt>2?P[2]:void 0;for(ct=x.length>3&&typeof ct=="function"?(rt--,ct):void 0,_t&&B0(P[0],P[1],_t)&&(ct=rt<3?void 0:ct,rt=1),D=Object(D);++z<rt;){var at=P[z];at&&x(D,at,z,ct)}return D})}function E0(x){return function(D,P,z){for(var rt=-1,ct=Object(D),_t=z(D),at=_t.length;at--;){var St=_t[++rt];if(P(ct[St],St,ct)===!1)break}return D}}function Ba(x,D){var P=x.__data__;return F0(D)?P[typeof D=="string"?"string":"hash"]:P.map}function Xs(x,D){var P=La(x,D);return w0(P)?P:void 0}function O0(x){var D=lr.call(x,Er),P=x[Er];try{x[Er]=void 0;var z=!0}catch{}var rt=Wh.call(x);return z&&(D?x[Er]=P:delete x[Er]),rt}function k0(x){return typeof x.constructor=="function"&&!Jh(x)?j_(qh(x)):{}}function Qh(x,D){var P=typeof x;return D=D??o,!!D&&(P=="number"||P!="symbol"&&st.test(x))&&x>-1&&x%1==0&&x<D}function B0(x,D,P){if(!kr(P))return!1;var z=typeof D;return(z=="number"?js(P)&&Qh(D,P.length):z=="string"&&D in P)?Fa(P[D],x):!1}function F0(x){var D=typeof x;return D=="string"||D=="number"||D=="symbol"||D=="boolean"?x!=="__proto__":x===null}function N0(x){return!!Vh&&Vh in x}function Jh(x){var D=x&&x.constructor,P=typeof D=="function"&&D.prototype||Ia;return x===P}function z0(x){var D=[];if(x!=null)for(var P in Object(x))D.push(P);return D}function H0(x){return Wh.call(x)}function G0(x,D,P){return D=$h(D===void 0?x.length-1:D,0),function(){for(var z=arguments,rt=-1,ct=$h(z.length-D,0),_t=Array(ct);++rt<ct;)_t[rt]=z[D+rt];rt=-1;for(var at=Array(D+1);++rt<D;)at[rt]=z[rt];return at[D]=P(_t),Ze(x,this,at)}}function $s(x,D){if(!(D==="constructor"&&typeof x[D]=="function")&&D!="__proto__")return x[D]}var V0=W0(M0);function W0(x){var D=0,P=0;return function(){var z=K_(),rt=a-(z-P);if(P=z,rt>0){if(++D>=i)return arguments[0]}else D=0;return x.apply(void 0,arguments)}}function U0(x){if(x!=null){try{return Pa.call(x)}catch{}try{return x+""}catch{}}return""}function Fa(x,D){return x===D||x!==x&&D!==D}var Zs=Kh(function(){return arguments}())?Kh:function(x){return ni(x)&&lr.call(x,"callee")&&!X_.call(x,"callee")},Ks=Array.isArray;function js(x){return x!=null&&ec(x.length)&&!Qs(x)}function Y0(x){return ni(x)&&js(x)}var tc=Z_||K0;function Qs(x){if(!kr(x))return!1;var D=ka(x);return D==c||D==d||D==l||D==_}function ec(x){return typeof x=="number"&&x>-1&&x%1==0&&x<=o}function kr(x){var D=typeof x;return x!=null&&(D=="object"||D=="function")}function ni(x){return x!=null&&typeof x=="object"}function q0(x){if(!ni(x)||ka(x)!=m)return!1;var D=qh(x);if(D===null)return!0;var P=lr.call(D,"constructor")&&D.constructor;return typeof P=="function"&&P instanceof P&&Pa.call(P)==Y_}var rc=tt?Aa(tt):x0;function X0(x){return P0(x,nc(x))}function nc(x){return js(x)?m0(x):b0(x)}var $0=R0(function(x,D,P){jh(x,D,P)});function Z0(x){return function(){return x}}function ic(x){return x}function K0(){return!1}r.exports=$0}(zi,zi.exports)),zi.exports}var yI=gI();const mI=Np(yI),ZI=({option:r,ref:t,...e})=>{const n=t1(),i=oc.useMemo(()=>"ontouchstart"in window||navigator.maxTouchPoints>0,[]),a=oc.useMemo(()=>({padding:[7,10],axisPointer:{type:"none"},textStyle:{fontFamily:"Plus Jakarta Sans",fontWeight:400,fontSize:12,color:n.vars.palette.common.white},backgroundColor:sc[800],borderWidth:0,borderColor:n.vars.palette.menuDivider,extraCssText:"box-shadow: none;",transitionDuration:0,confine:!0,triggerOn:i?"click":"mousemove|click",...n.applyStyles("dark",{backgroundColor:sc[900],borderWidth:1})}),[n,r]);return e1.jsx(r1,{component:pI,ref:t,option:{...r,tooltip:mI(a,r.tooltip)},...e})};export{$T as $,r_ as A,Ib as B,We as C,it as D,dt as E,EA as F,ae as G,or as H,vy as I,AI as J,Lt as K,sr as L,Hx as M,W as N,Ps as O,mt as P,R_ as Q,ZI as R,Ss as S,Jg as T,Ko as U,N as V,Fl as W,Nl as X,DI as Y,Xt as Z,H as _,Pr as a,cf as a$,ry as a0,EI as a1,V as a2,sh as a3,zu as a4,Zn as a5,hg as a6,Ht as a7,pt as a8,sa as a9,vm as aA,qe as aB,yt as aC,Dr as aD,Yo as aE,lx as aF,bs as aG,xs as aH,_s as aI,Ta as aJ,ty as aK,Ae as aL,br as aM,cs as aN,Qp as aO,zo as aP,xr as aQ,Z as aR,Re as aS,VI as aT,FA as aU,tp as aV,Rs as aW,Mt as aX,jg as aY,Sa as aZ,Yw as a_,ft as aa,xa as ab,bI as ac,kw as ad,nh as ae,Ar as af,yg as ag,am as ah,ot as ai,J as aj,RI as ak,yn as al,oe as am,Fy as an,By as ao,Bb as ap,Jp as aq,Px as ar,wh as as,Ct as at,OI as au,kI as av,_n as aw,Wt as ax,et as ay,NI as az,B as b,sg as b$,Ds as b0,MI as b1,Fw as b2,PI as b3,nt as b4,la as b5,Vf as b6,Ut as b7,Ff as b8,_a as b9,$D as bA,Ms as bB,Wx as bC,UI as bD,lL as bE,Nf as bF,Y as bG,Xn as bH,vC as bI,Tt as bJ,xu as bK,zI as bL,SI as bM,AM as bN,HI as bO,Bt as bP,Da as bQ,rT as bR,pn as bS,Yg as bT,rh as bU,wI as bV,LM as bW,ww as bX,qn as bY,vt as bZ,Uc as b_,$1 as ba,_e as bb,Le as bc,v1 as bd,$n as be,yr as bf,mr as bg,Hc as bh,Ue as bi,gt as bj,Ni as bk,BI as bl,Ir as bm,gg as bn,ie as bo,_S as bp,mS as bq,Kx as br,kx as bs,uy as bt,fy as bu,lt as bv,xI as bw,dw as bx,Dg as by,GI as bz,gn as c,nS as c0,FI as c1,Hi as c2,gw as c3,p1 as c4,zx as c5,PT as c6,i_ as c7,AD as c8,Mr as c9,ws as ca,sy as cb,S_ as cc,ti as cd,hA as ce,CI as d,C as e,Ns as f,LI as g,II as h,Jf as i,G as j,j as k,Bl as l,wt as m,GS as n,Ye as o,Gt as p,XI as q,It as r,Bw as s,TI as t,Qn as u,Lr as v,WI as w,$I as x,YI as y,qI as z};
