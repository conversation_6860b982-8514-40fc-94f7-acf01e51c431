import{n as Ue,aX as Ne,l as je,al as Ze,V as Le,p as F,bZ as N,b_ as xe,aj as we,aG as Oe,aa as qe,af as Me,b$ as De,c0 as ce,e as ne,m as Ge,c1 as He,_ as pe,S as ke,b as H,ar as _e,C as $e,Z as Je,i as ue,u as Te,Y as Ke,d as Qe,t as We,M as ea,J as aa,c2 as ta,a8 as Pe,at as Re,b3 as ra,c3 as na,bh as ia,o as la,bl as oa}from"./ReactEchart-C_a4bTea.js";import{L as sa,d as va}from"./LegendVisualProvider-CgY2uWFN.js";import{g as ie}from"./sectorHelper-BreWr0VT.js";import{c as ga}from"./createSeriesDataSimply-DIBZSMqc.js";var Ce=Math.PI*2,le=Math.PI/180;function Ve(a,i){return Ze(a.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})}function Be(a,i){var r=Ve(a,i),e=a.get("center"),n=a.get("radius");Le(n)||(n=[0,n]);var l=F(r.width,i.getWidth()),t=F(r.height,i.getHeight()),s=Math.min(l,t),c=F(n[0],s/2),u=F(n[1],s/2),o,p,f=a.coordinateSystem;if(f){var g=f.dataToPoint(e);o=g[0]||0,p=g[1]||0}else Le(e)||(e=[e,e]),o=F(e[0],l)+r.x,p=F(e[1],t)+r.y;return{cx:o,cy:p,r0:c,r:u}}function ca(a,i,r){i.eachSeriesByType(a,function(e){var n=e.getData(),l=n.mapDimension("value"),t=Ve(e,r),s=Be(e,r),c=s.cx,u=s.cy,o=s.r,p=s.r0,f=-e.get("startAngle")*le,g=e.get("endAngle"),v=e.get("padAngle")*le;g=g==="auto"?f-Ce:-g*le;var h=e.get("minAngle")*le,d=h+v,m=0;n.each(l,function(B){!isNaN(B)&&m++});var S=n.getSum(l),b=Math.PI/(S||m)*2,D=e.get("clockwise"),w=e.get("roseType"),T=e.get("stillShowZeroSum"),O=n.getDataExtent(l);O[0]=0;var y=D?1:-1,A=[f,g],R=y*v/2;Ue(A,!D),f=A[0],g=A[1];var Z=Ee(e);Z.startAngle=f,Z.endAngle=g,Z.clockwise=D;var Y=Math.abs(g-f),E=Y,_=0,V=f;if(n.setLayout({viewRect:t,r:o}),n.each(l,function(B,k){var L;if(isNaN(B)){n.setItemLayout(k,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:D,cx:c,cy:u,r0:p,r:w?NaN:o});return}w!=="area"?L=S===0&&T?b:B*b:L=Y/m,L<d?(L=d,E-=d):_+=B;var C=V+y*L,P=0,M=0;v>L?(P=V+y*L/2,M=P):(P=V+R,M=C-R),n.setItemLayout(k,{angle:L,startAngle:P,endAngle:M,clockwise:D,cx:c,cy:u,r0:p,r:w?je(B,O,[p,o]):o}),V=C}),E<Ce&&m)if(E<=.001){var X=Y/m;n.each(l,function(B,k){if(!isNaN(B)){var L=n.getItemLayout(k);L.angle=X;var C=0,P=0;X<v?(C=f+y*(k+1/2)*X,P=C):(C=f+y*k*X+R,P=f+y*(k+1)*X-R),L.startAngle=C,L.endAngle=P}})}else b=E/_,V=f,n.each(l,function(B,k){if(!isNaN(B)){var L=n.getItemLayout(k),C=L.angle===d?d:B*b,P=0,M=0;C<v?(P=V+y*C/2,M=P):(P=V+R,M=V+y*C-R),L.startAngle=P,L.endAngle=M,V+=y*C}})})}var Ee=Ne();function Ye(a,i,r,e,n,l,t,s){var c=n-a,u=l-i,o=r-a,p=e-i,f=Math.sqrt(o*o+p*p);o/=f,p/=f;var g=c*o+u*p,v=g/f;v*=f;var h=t[0]=a+v*o,d=t[1]=i+v*p;return Math.sqrt((h-n)*(h-n)+(d-l)*(d-l))}var U=new N,x=new N,I=new N,j=new N,z=new N,oe=[],G=new N;function ua(a,i){if(i<=180&&i>0){i=i/180*Math.PI,U.fromArray(a[0]),x.fromArray(a[1]),I.fromArray(a[2]),N.sub(j,U,x),N.sub(z,I,x);var r=j.len(),e=z.len();if(!(r<.001||e<.001)){j.scale(1/r),z.scale(1/e);var n=j.dot(z),l=Math.cos(i);if(l<n){var t=Ye(x.x,x.y,I.x,I.y,U.x,U.y,oe);G.fromArray(oe),G.scaleAndAdd(z,t/Math.tan(Math.PI-i));var s=I.x!==x.x?(G.x-x.x)/(I.x-x.x):(G.y-x.y)/(I.y-x.y);if(isNaN(s))return;s<0?N.copy(G,x):s>1&&N.copy(G,I),G.toArray(a[1])}}}}function fa(a,i,r){if(r<=180&&r>0){r=r/180*Math.PI,U.fromArray(a[0]),x.fromArray(a[1]),I.fromArray(a[2]),N.sub(j,x,U),N.sub(z,I,x);var e=j.len(),n=z.len();if(!(e<.001||n<.001)){j.scale(1/e),z.scale(1/n);var l=j.dot(i),t=Math.cos(r);if(l<t){var s=Ye(x.x,x.y,I.x,I.y,U.x,U.y,oe);G.fromArray(oe);var c=Math.PI/2,u=Math.acos(z.dot(i)),o=c+u-r;if(o>=c)N.copy(G,I);else{G.scaleAndAdd(z,s/Math.tan(Math.PI/2-o));var p=I.x!==x.x?(G.x-x.x)/(I.x-x.x):(G.y-x.y)/(I.y-x.y);if(isNaN(p))return;p<0?N.copy(G,x):p>1&&N.copy(G,I)}G.toArray(a[1])}}}}function fe(a,i,r,e){var n=r==="normal",l=n?a:a.ensureState(r);l.ignore=i;var t=e.get("smooth");t&&t===!0&&(t=.3),l.shape=l.shape||{},t>0&&(l.shape.smooth=t);var s=e.getModel("lineStyle").getLineStyle();n?a.useStyle(s):l.style=s}function ha(a,i){var r=i.smooth,e=i.points;if(e)if(a.moveTo(e[0][0],e[0][1]),r>0&&e.length>=3){var n=De(e[0],e[1]),l=De(e[1],e[2]);if(!n||!l){a.lineTo(e[1][0],e[1][1]),a.lineTo(e[2][0],e[2][1]);return}var t=Math.min(n,l)*r,s=ce([],e[1],e[0],t/n),c=ce([],e[1],e[2],t/l),u=ce([],s,c,.5);a.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),a.bezierCurveTo(c[0],c[1],c[0],c[1],e[2][0],e[2][1])}else for(var o=1;o<e.length;o++)a.lineTo(e[o][0],e[o][1])}function pa(a,i,r){var e=a.getTextGuideLine(),n=a.getTextContent();if(!n){e&&a.removeTextGuideLine();return}for(var l=i.normal,t=l.get("show"),s=n.ignore,c=0;c<xe.length;c++){var u=xe[c],o=i[u],p=u==="normal";if(o){var f=o.get("show"),g=p?s:we(n.states[u]&&n.states[u].ignore,s);if(g||!we(f,t)){var v=p?e:e&&e.states[u];v&&(v.ignore=!0),e&&fe(e,!0,u,o);continue}e||(e=new Oe,a.setTextGuideLine(e),!p&&(s||!t)&&fe(e,!0,"normal",i.normal),a.stateProxy&&(e.stateProxy=a.stateProxy)),fe(e,!1,u,o)}}if(e){qe(e.style,r),e.style.fill=null;var h=l.get("showAbove"),d=a.textGuideLineConfig=a.textGuideLineConfig||{};d.showAbove=h||!1,e.buildPath=ha}}function da(a,i){i=i||"labelLine";for(var r={normal:a.getModel(i)},e=0;e<Me.length;e++){var n=Me[e];r[n]=a.getModel([n,i])}return r}var ya=Math.PI/180;function Ie(a,i,r,e,n,l,t,s,c,u){if(a.length<2)return;function o(h){for(var d=h.rB,m=d*d,S=0;S<h.list.length;S++){var b=h.list[S],D=Math.abs(b.label.y-r),w=e+b.len,T=w*w,O=Math.sqrt(Math.abs((1-D*D/m)*T)),y=i+(O+b.len2)*n,A=y-b.label.x,R=b.targetTextWidth-A*n;ze(b,R,!0),b.label.x=y}}function p(h){for(var d={list:[],maxY:0},m={list:[],maxY:0},S=0;S<h.length;S++)if(h[S].labelAlignTo==="none"){var b=h[S],D=b.label.y>r?m:d,w=Math.abs(b.label.y-r);if(w>=D.maxY){var T=b.label.x-i-b.len2*n,O=e+b.len,y=Math.abs(T)<O?Math.sqrt(w*w/(1-T*T/O/O)):O;D.rB=y,D.maxY=w}D.list.push(b)}o(d),o(m)}for(var f=a.length,g=0;g<f;g++)if(a[g].position==="outer"&&a[g].labelAlignTo==="labelLine"){var v=a[g].label.x-u;a[g].linePoints[1][0]+=v,a[g].label.x=u}He(a,c,c+t)&&p(a)}function ma(a,i,r,e,n,l,t,s){for(var c=[],u=[],o=Number.MAX_VALUE,p=-Number.MAX_VALUE,f=0;f<a.length;f++){var g=a[f].label;he(a[f])||(g.x<i?(o=Math.min(o,g.x),c.push(a[f])):(p=Math.max(p,g.x),u.push(a[f])))}for(var f=0;f<a.length;f++){var v=a[f];if(!he(v)&&v.linePoints){if(v.labelStyleWidth!=null)continue;var g=v.label,h=v.linePoints,d=void 0;v.labelAlignTo==="edge"?g.x<i?d=h[2][0]-v.labelDistance-t-v.edgeDistance:d=t+n-v.edgeDistance-h[2][0]-v.labelDistance:v.labelAlignTo==="labelLine"?g.x<i?d=o-t-v.bleedMargin:d=t+n-p-v.bleedMargin:g.x<i?d=g.x-t-v.bleedMargin:d=t+n-g.x-v.bleedMargin,v.targetTextWidth=d,ze(v,d)}}Ie(u,i,r,e,1,n,l,t,s,p),Ie(c,i,r,e,-1,n,l,t,s,o);for(var f=0;f<a.length;f++){var v=a[f];if(!he(v)&&v.linePoints){var g=v.label,h=v.linePoints,m=v.labelAlignTo==="edge",S=g.style.padding,b=S?S[1]+S[3]:0,D=g.style.backgroundColor?0:b,w=v.rect.width+D,T=h[1][0]-h[2][0];m?g.x<i?h[2][0]=t+v.edgeDistance+w+v.labelDistance:h[2][0]=t+n-v.edgeDistance-w-v.labelDistance:(g.x<i?h[2][0]=g.x+v.labelDistance:h[2][0]=g.x-v.labelDistance,h[1][0]=h[2][0]+T),h[1][1]=h[2][1]=g.y}}}function ze(a,i,r){if(r===void 0&&(r=!1),a.labelStyleWidth==null){var e=a.label,n=e.style,l=a.rect,t=n.backgroundColor,s=n.padding,c=s?s[1]+s[3]:0,u=n.overflow,o=l.width+(t?0:c);if(i<o||r){var p=l.height;if(u&&u.match("break")){e.setStyle("backgroundColor",null),e.setStyle("width",i-c);var f=e.getBoundingRect();e.setStyle("width",Math.ceil(f.width)),e.setStyle("backgroundColor",t)}else{var g=i-c,v=i<o?g:r?g>a.unconstrainedWidth?null:g:null;e.setStyle("width",v)}var h=e.getBoundingRect();l.width=h.width;var d=(e.style.margin||0)+2.1;l.height=h.height+d,l.y-=(l.height-p)/2}}}function he(a){return a.position==="center"}function ba(a){var i=a.getData(),r=[],e,n,l=!1,t=(a.get("minShowLabelAngle")||0)*ya,s=i.getLayout("viewRect"),c=i.getLayout("r"),u=s.width,o=s.x,p=s.y,f=s.height;function g(T){T.ignore=!0}function v(T){if(!T.ignore)return!0;for(var O in T.states)if(T.states[O].ignore===!1)return!0;return!1}i.each(function(T){var O=i.getItemGraphicEl(T),y=O.shape,A=O.getTextContent(),R=O.getTextGuideLine(),Z=i.getItemModel(T),Y=Z.getModel("label"),E=Y.get("position")||Z.get(["emphasis","label","position"]),_=Y.get("distanceToLabelLine"),V=Y.get("alignTo"),X=F(Y.get("edgeDistance"),u),B=Y.get("bleedMargin"),k=Z.getModel("labelLine"),L=k.get("length");L=F(L,u);var C=k.get("length2");if(C=F(C,u),Math.abs(y.endAngle-y.startAngle)<t){ne(A.states,g),A.ignore=!0,R&&(ne(R.states,g),R.ignore=!0);return}if(v(A)){var P=(y.startAngle+y.endAngle)/2,M=Math.cos(P),q=Math.sin(P),Q,te,de,re;e=y.cx,n=y.cy;var $=E==="inside"||E==="inner";if(E==="center")Q=y.cx,te=y.cy,re="center";else{var se=($?(y.r+y.r0)/2*M:y.r*M)+e,ve=($?(y.r+y.r0)/2*q:y.r*q)+n;if(Q=se+M*3,te=ve+q*3,!$){var ye=se+M*(L+c-y.r),me=ve+q*(L+c-y.r),be=ye+(M<0?-1:1)*C,Se=me;V==="edge"?Q=M<0?o+X:o+u-X:Q=be+(M<0?-_:_),te=Se,de=[[se,ve],[ye,me],[be,Se]]}re=$?"center":V==="edge"?M>0?"right":"left":M>0?"left":"right"}var W=Math.PI,J=0,ee=Y.get("rotate");if(Ge(ee))J=ee*(W/180);else if(E==="center")J=0;else if(ee==="radial"||ee===!0){var Xe=M<0?-P+W:-P;J=Xe}else if(ee==="tangential"&&E!=="outside"&&E!=="outer"){var K=Math.atan2(M,q);K<0&&(K=W*2+K);var Fe=q>0;Fe&&(K=W+K),J=K-W}if(l=!!J,A.x=Q,A.y=te,A.rotation=J,A.setStyle({verticalAlign:"middle"}),$){A.setStyle({align:re});var ge=A.states.select;ge&&(ge.x+=A.x,ge.y+=A.y)}else{var ae=A.getBoundingRect().clone();ae.applyTransform(A.getComputedTransform());var Ae=(A.style.margin||0)+2.1;ae.y-=Ae/2,ae.height+=Ae,r.push({label:A,labelLine:R,position:E,len:L,len2:C,minTurnAngle:k.get("minTurnAngle"),maxSurfaceAngle:k.get("maxSurfaceAngle"),surfaceNormal:new N(M,q),linePoints:de,textAlign:re,labelDistance:_,labelAlignTo:V,edgeDistance:X,bleedMargin:B,rect:ae,unconstrainedWidth:ae.width,labelStyleWidth:A.style.width})}O.setTextConfig({inside:$})}}),!l&&a.get("avoidLabelOverlap")&&ma(r,e,n,c,u,f,o,p);for(var h=0;h<r.length;h++){var d=r[h],m=d.label,S=d.labelLine,b=isNaN(m.x)||isNaN(m.y);if(m){m.setStyle({align:d.textAlign}),b&&(ne(m.states,g),m.ignore=!0);var D=m.states.select;D&&(D.x+=m.x,D.y+=m.y)}if(S){var w=d.linePoints;b||!w?(ne(S.states,g),S.ignore=!0):(ua(w,d.minTurnAngle),fa(w,d.surfaceNormal,d.maxSurfaceAngle),S.setShape({points:w}),m.__hostTarget.textGuideLineConfig={anchor:new N(w[0][0],w[0][1])})}}}var Sa=function(a){pe(i,a);function i(r,e,n){var l=a.call(this)||this;l.z2=2;var t=new Je;return l.setTextContent(t),l.updateData(r,e,n,!0),l}return i.prototype.updateData=function(r,e,n,l){var t=this,s=r.hostModel,c=r.getItemModel(e),u=c.getModel("emphasis"),o=r.getItemLayout(e),p=H(ie(c.getModel("itemStyle"),o,!0),o);if(isNaN(p.startAngle)){t.setShape(p);return}if(l){t.setShape(p);var f=s.getShallow("animationType");s.ecModel.ssr?(ue(t,{scaleX:0,scaleY:0},s,{dataIndex:e,isFrom:!0}),t.originX=p.cx,t.originY=p.cy):f==="scale"?(t.shape.r=o.r0,ue(t,{shape:{r:o.r}},s,e)):n!=null?(t.setShape({startAngle:n,endAngle:n}),ue(t,{shape:{startAngle:o.startAngle,endAngle:o.endAngle}},s,e)):(t.shape.endAngle=o.startAngle,Te(t,{shape:{endAngle:o.endAngle}},s,e))}else Ke(t),Te(t,{shape:p},s,e);t.useStyle(r.getItemVisual(e,"style")),Qe(t,c);var g=(o.startAngle+o.endAngle)/2,v=s.get("selectedOffset"),h=Math.cos(g)*v,d=Math.sin(g)*v,m=c.getShallow("cursor");m&&t.attr("cursor",m),this._updateLabel(s,r,e),t.ensureState("emphasis").shape=H({r:o.r+(u.get("scale")&&u.get("scaleSize")||0)},ie(u.getModel("itemStyle"),o)),H(t.ensureState("select"),{x:h,y:d,shape:ie(c.getModel(["select","itemStyle"]),o)}),H(t.ensureState("blur"),{shape:ie(c.getModel(["blur","itemStyle"]),o)});var S=t.getTextGuideLine(),b=t.getTextContent();S&&H(S.ensureState("select"),{x:h,y:d}),H(b.ensureState("select"),{x:h,y:d}),We(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},i.prototype._updateLabel=function(r,e,n){var l=this,t=e.getItemModel(n),s=t.getModel("labelLine"),c=e.getItemVisual(n,"style"),u=c&&c.fill,o=c&&c.opacity;ea(l,aa(t),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:u,defaultOpacity:o,defaultText:r.getFormattedLabel(n,"normal")||e.getName(n)});var p=l.getTextContent();l.setTextConfig({position:null,rotation:null}),p.attr({z2:10});var f=r.get(["label","position"]);if(f!=="outside"&&f!=="outer")l.removeTextGuideLine();else{var g=this.getTextGuideLine();g||(g=new Oe,this.setTextGuideLine(g)),pa(this,da(t),{stroke:u,opacity:ta(s.get(["lineStyle","opacity"]),o,1)})}},i}(ke),Aa=function(a){pe(i,a);function i(){var r=a!==null&&a.apply(this,arguments)||this;return r.ignoreLabelLineUpdate=!0,r}return i.prototype.render=function(r,e,n,l){var t=r.getData(),s=this._data,c=this.group,u;if(!s&&t.count()>0){for(var o=t.getItemLayout(0),p=1;isNaN(o&&o.startAngle)&&p<t.count();++p)o=t.getItemLayout(p);o&&(u=o.startAngle)}if(this._emptyCircleSector&&c.remove(this._emptyCircleSector),t.count()===0&&r.get("showEmptyCircle")){var f=Ee(r),g=new ke({shape:H(Be(r,n),f)});g.useStyle(r.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=g,c.add(g)}t.diff(s).add(function(v){var h=new Sa(t,v,u);t.setItemGraphicEl(v,h),c.add(h)}).update(function(v,h){var d=s.getItemGraphicEl(h);d.updateData(t,v,u),d.off("click"),c.add(d),t.setItemGraphicEl(v,d)}).remove(function(v){var h=s.getItemGraphicEl(v);_e(h,r,v)}).execute(),ba(r),r.get("animationTypeUpdate")!=="expansion"&&(this._data=t)},i.prototype.dispose=function(){},i.prototype.containPoint=function(r,e){var n=e.getData(),l=n.getItemLayout(0);if(l){var t=r[0]-l.cx,s=r[1]-l.cy,c=Math.sqrt(t*t+s*s);return c<=l.r&&c>=l.r0}},i.type="pie",i}($e),La=Ne(),xa=function(a){pe(i,a);function i(){return a!==null&&a.apply(this,arguments)||this}return i.prototype.init=function(r){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new sa(Pe(this.getData,this),Pe(this.getRawData,this)),this._defaultLabelLine(r)},i.prototype.mergeOption=function(){a.prototype.mergeOption.apply(this,arguments)},i.prototype.getInitialData=function(){return ga(this,{coordDimensions:["value"],encodeDefaulter:Re(ra,this)})},i.prototype.getDataParams=function(r){var e=this.getData(),n=La(e),l=n.seats;if(!l){var t=[];e.each(e.mapDimension("value"),function(c){t.push(c)}),l=n.seats=na(t,e.hostModel.get("percentPrecision"))}var s=a.prototype.getDataParams.call(this,r);return s.percent=l[r]||0,s.$vars.push("percent"),s},i.prototype._defaultLabelLine=function(r){ia(r,"labelLine",["show"]);var e=r.labelLine,n=r.emphasis.labelLine;e.show=e.show&&r.label.show,n.show=n.show&&r.emphasis.label.show},i.type="series.pie",i.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},i}(la);function wa(a){return{seriesType:a,reset:function(i,r){var e=i.getData();e.filterSelf(function(n){var l=e.mapDimension("value"),t=e.get(l,n);return!(Ge(t)&&!isNaN(t)&&t<0)})}}}function Ca(a){a.registerChartView(Aa),a.registerSeriesModel(xa),oa("pie",a.registerAction),a.registerLayout(Re(ca,"pie")),a.registerProcessor(va("pie")),a.registerProcessor(wa("pie"))}export{Ca as i};
