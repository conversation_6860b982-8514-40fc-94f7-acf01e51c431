import{_ as E,B as ie,e as ne,o as se,ap as pe,aq as Z,m as fe,V as me,I as Ae,i as V,u as k,Y as be,ae as ye,ar as j,G as Se,N as _e,E as z,C as xe,S as oe,K as he,b as le,J as De,M as we,g as Le,t as Ie,d as Pe,as as Ce,P as Oe,at as ke,au as Te,av as Re}from"./ReactEchart-C_a4bTea.js";import{c as Me,b as ue,S as q,g as Ve,d as Ee,e as Ne}from"./sausage-DWu_h7Kw.js";import{g as Be}from"./sectorHelper-BreWr0VT.js";var U=function(i){E(t,i);function t(){var e=i!==null&&i.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.getInitialData=function(e,r){return ie(null,this,{useEncodeDefaulter:!0})},t.prototype.getMarkerPosition=function(e,r,a){var n=this.coordinateSystem;if(n&&n.clampData){var h=n.clampData(e),o=n.dataToPoint(h);if(a)ne(n.getAxes(),function(d,v){if(d.type==="category"&&r!=null){var c=d.getTicksCoords(),S=d.getTickModel().get("alignWithLabel"),A=h[v],y=r[v]==="x1"||r[v]==="y1";if(y&&!S&&(A+=1),c.length<2)return;if(c.length===2){o[v]=d.toGlobalCoord(d.getExtent()[y?1:0]);return}for(var f=void 0,m=void 0,_=1,b=0;b<c.length;b++){var D=c[b].coord,x=b===c.length-1?c[b-1].tickValue+_:c[b].tickValue;if(x===A){m=D;break}else if(x<A)f=D;else if(f!=null&&x>A){m=(D+f)/2;break}b===1&&(_=x-c[0].tickValue)}m==null&&(f?f&&(m=c[c.length-1].coord):m=c[0].coord),o[v]=d.toGlobalCoord(m)}});else{var s=this.getData(),u=s.getLayout("offset"),l=s.getLayout("size"),g=n.getBaseAxis().isHorizontal()?0:1;o[g]+=u+l/2}return o}return[NaN,NaN]},t.type="series.__base_bar__",t.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},t}(se);se.registerClass(U);var We=function(i){E(t,i);function t(){var e=i!==null&&i.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.getInitialData=function(){return ie(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},t.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},t.prototype.getProgressiveThreshold=function(){var e=this.get("progressiveThreshold"),r=this.get("largeThreshold");return r>e&&(e=r),e},t.prototype.brushSelector=function(e,r,a){return a.rect(r.getItemLayout(e))},t.type="series.bar",t.dependencies=["grid","polar"],t.defaultOption=pe(U.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),t}(U);function Ge(i,t){t=t||{};var e=t.isRoundCap;return function(r,a,n){var h=a.position;if(!h||h instanceof Array)return Z(r,a,n);var o=i(h),s=a.distance!=null?a.distance:5,u=this.shape,l=u.cx,g=u.cy,d=u.r,v=u.r0,c=(d+v)/2,S=u.startAngle,A=u.endAngle,y=(S+A)/2,f=e?Math.abs(d-v)/2:0,m=Math.cos,_=Math.sin,b=l+d*m(S),D=g+d*_(S),x="left",P="top";switch(o){case"startArc":b=l+(v-s)*m(y),D=g+(v-s)*_(y),x="center",P="top";break;case"insideStartArc":b=l+(v+s)*m(y),D=g+(v+s)*_(y),x="center",P="bottom";break;case"startAngle":b=l+c*m(S)+T(S,s+f,!1),D=g+c*_(S)+R(S,s+f,!1),x="right",P="middle";break;case"insideStartAngle":b=l+c*m(S)+T(S,-s+f,!1),D=g+c*_(S)+R(S,-s+f,!1),x="left",P="middle";break;case"middle":b=l+c*m(y),D=g+c*_(y),x="center",P="middle";break;case"endArc":b=l+(d+s)*m(y),D=g+(d+s)*_(y),x="center",P="bottom";break;case"insideEndArc":b=l+(d-s)*m(y),D=g+(d-s)*_(y),x="center",P="top";break;case"endAngle":b=l+c*m(A)+T(A,s+f,!0),D=g+c*_(A)+R(A,s+f,!0),x="left",P="middle";break;case"insideEndAngle":b=l+c*m(A)+T(A,-s+f,!0),D=g+c*_(A)+R(A,-s+f,!0),x="right",P="middle";break;default:return Z(r,a,n)}return r=r||{},r.x=b,r.y=D,r.align=x,r.verticalAlign=P,r}}function Fe(i,t,e,r){if(fe(r)){i.setTextConfig({rotation:r});return}else if(me(t)){i.setTextConfig({rotation:0});return}var a=i.shape,n=a.clockwise?a.startAngle:a.endAngle,h=a.clockwise?a.endAngle:a.startAngle,o=(n+h)/2,s,u=e(t);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":s=o;break;case"startAngle":case"insideStartAngle":s=n;break;case"endAngle":case"insideEndAngle":s=h;break;default:i.setTextConfig({rotation:0});return}var l=Math.PI*1.5-s;u==="middle"&&l>Math.PI/2&&l<Math.PI*1.5&&(l-=Math.PI),i.setTextConfig({rotation:l})}function T(i,t,e){return t*Math.sin(i)*(e?-1:1)}function R(i,t,e){return t*Math.cos(i)*(e?1:-1)}var F=Math.max,Y=Math.min;function Ye(i,t){var e=i.getArea&&i.getArea();if(ue(i,"cartesian2d")){var r=i.getBaseAxis();if(r.type!=="category"||!r.onBand){var a=t.getLayout("bandWidth");r.isHorizontal()?(e.x-=a,e.width+=a*2):(e.y-=a,e.height+=a*2)}}return e}var Ue=function(i){E(t,i);function t(){var e=i.call(this)||this;return e.type=t.type,e._isFirstFrame=!0,e}return t.prototype.render=function(e,r,a,n){this._model=e,this._removeOnRenderedListener(a),this._updateDrawMode(e);var h=e.get("coordinateSystem");(h==="cartesian2d"||h==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(e,r,a):this._renderNormal(e,r,a,n))},t.prototype.incrementalPrepareRender=function(e){this._clear(),this._updateDrawMode(e),this._updateLargeClip(e)},t.prototype.incrementalRender=function(e,r){this._progressiveEls=[],this._incrementalRenderLarge(e,r)},t.prototype.eachRendered=function(e){Ae(this._progressiveEls||this.group,e)},t.prototype._updateDrawMode=function(e){var r=e.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},t.prototype._renderNormal=function(e,r,a,n){var h=this.group,o=e.getData(),s=this._data,u=e.coordinateSystem,l=u.getBaseAxis(),g;u.type==="cartesian2d"?g=l.isHorizontal():u.type==="polar"&&(g=l.dim==="angle");var d=e.isAnimationEnabled()?e:null,v=ze(e,u);v&&this._enableRealtimeSort(v,o,a);var c=e.get("clip",!0)||v,S=Ye(u,o);h.removeClipPath();var A=e.get("roundCap",!0),y=e.get("showBackground",!0),f=e.getModel("backgroundStyle"),m=f.get("borderRadius")||0,_=[],b=this._backgroundEls,D=n&&n.isInitSort,x=n&&n.type==="changeAxisOrder";function P(p){var C=M[u.type](o,p),w=Ke(u,g,C);return w.useStyle(f.getItemStyle()),u.type==="cartesian2d"?w.setShape("r",m):w.setShape("cornerRadius",m),_[p]=w,w}o.diff(s).add(function(p){var C=o.getItemModel(p),w=M[u.type](o,p,C);if(y&&P(p),!(!o.hasValue(p)||!$[u.type](w))){var O=!1;c&&(O=H[u.type](S,w));var L=J[u.type](e,o,p,w,g,d,l.model,!1,A);v&&(L.forceLabelAnimation=!0),ee(L,o,p,C,w,e,g,u.type==="polar"),D?L.attr({shape:w}):v?K(v,d,L,w,p,g,!1,!1):V(L,{shape:w},e,p),o.setItemGraphicEl(p,L),h.add(L),L.ignore=O}}).update(function(p,C){var w=o.getItemModel(p),O=M[u.type](o,p,w);if(y){var L=void 0;b.length===0?L=P(C):(L=b[C],L.useStyle(f.getItemStyle()),u.type==="cartesian2d"?L.setShape("r",m):L.setShape("cornerRadius",m),_[p]=L);var de=M[u.type](o,p),ve=ce(g,de,u);k(L,{shape:ve},d,p)}var I=s.getItemGraphicEl(C);if(!o.hasValue(p)||!$[u.type](O)){h.remove(I);return}var W=!1;if(c&&(W=H[u.type](S,O),W&&h.remove(I)),I?be(I):I=J[u.type](e,o,p,O,g,d,l.model,!!I,A),v&&(I.forceLabelAnimation=!0),x){var X=I.getTextContent();if(X){var G=ye(X);G.prevValue!=null&&(G.prevValue=G.value)}}else ee(I,o,p,w,O,e,g,u.type==="polar");D?I.attr({shape:O}):v?K(v,d,I,O,p,g,!0,x):k(I,{shape:O},e,p,null),o.setItemGraphicEl(p,I),I.ignore=W,h.add(I)}).remove(function(p){var C=s.getItemGraphicEl(p);C&&j(C,e,p)}).execute();var N=this._backgroundGroup||(this._backgroundGroup=new Se);N.removeAll();for(var B=0;B<_.length;++B)N.add(_[B]);h.add(N),this._backgroundEls=_,this._data=o},t.prototype._renderLarge=function(e,r,a){this._clear(),re(e,this.group),this._updateLargeClip(e)},t.prototype._incrementalRenderLarge=function(e,r){this._removeBackground(),re(r,this.group,this._progressiveEls,!0)},t.prototype._updateLargeClip=function(e){var r=e.get("clip",!0)&&Me(e.coordinateSystem,!1,e),a=this.group;r?a.setClipPath(r):a.removeClipPath()},t.prototype._enableRealtimeSort=function(e,r,a){var n=this;if(r.count()){var h=e.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(r,e,a),this._isFirstFrame=!1;else{var o=function(s){var u=r.getItemGraphicEl(s),l=u&&u.shape;return l&&Math.abs(h.isHorizontal()?l.height:l.width)||0};this._onRendered=function(){n._updateSortWithinSameData(r,o,h,a)},a.getZr().on("rendered",this._onRendered)}}},t.prototype._dataSort=function(e,r,a){var n=[];return e.each(e.mapDimension(r.dim),function(h,o){var s=a(o);s=s??NaN,n.push({dataIndex:o,mappedValue:s,ordinalNumber:h})}),n.sort(function(h,o){return o.mappedValue-h.mappedValue}),{ordinalNumbers:_e(n,function(h){return h.ordinalNumber})}},t.prototype._isOrderChangedWithinSameData=function(e,r,a){for(var n=a.scale,h=e.mapDimension(a.dim),o=Number.MAX_VALUE,s=0,u=n.getOrdinalMeta().categories.length;s<u;++s){var l=e.rawIndexOf(h,n.getRawOrdinalNumber(s)),g=l<0?Number.MIN_VALUE:r(e.indexOfRawIndex(l));if(g>o)return!0;o=g}return!1},t.prototype._isOrderDifferentInView=function(e,r){for(var a=r.scale,n=a.getExtent(),h=Math.max(0,n[0]),o=Math.min(n[1],a.getOrdinalMeta().categories.length-1);h<=o;++h)if(e.ordinalNumbers[h]!==a.getRawOrdinalNumber(h))return!0},t.prototype._updateSortWithinSameData=function(e,r,a,n){if(this._isOrderChangedWithinSameData(e,r,a)){var h=this._dataSort(e,a,r);this._isOrderDifferentInView(h,a)&&(this._removeOnRenderedListener(n),n.dispatchAction({type:"changeAxisOrder",componentType:a.dim+"Axis",axisId:a.index,sortInfo:h}))}},t.prototype._dispatchInitSort=function(e,r,a){var n=r.baseAxis,h=this._dataSort(e,n,function(o){return e.get(e.mapDimension(r.otherAxis.dim),o)});a.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",isInitSort:!0,axisId:n.index,sortInfo:h})},t.prototype.remove=function(e,r){this._clear(this._model),this._removeOnRenderedListener(r)},t.prototype.dispose=function(e,r){this._removeOnRenderedListener(r)},t.prototype._removeOnRenderedListener=function(e){this._onRendered&&(e.getZr().off("rendered",this._onRendered),this._onRendered=null)},t.prototype._clear=function(e){var r=this.group,a=this._data;e&&e.isAnimationEnabled()&&a&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],a.eachItemGraphicEl(function(n){j(n,e,z(n).dataIndex)})):r.removeAll(),this._data=null,this._isFirstFrame=!0},t.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},t.type="bar",t}(xe),H={cartesian2d:function(i,t){var e=t.width<0?-1:1,r=t.height<0?-1:1;e<0&&(t.x+=t.width,t.width=-t.width),r<0&&(t.y+=t.height,t.height=-t.height);var a=i.x+i.width,n=i.y+i.height,h=F(t.x,i.x),o=Y(t.x+t.width,a),s=F(t.y,i.y),u=Y(t.y+t.height,n),l=o<h,g=u<s;return t.x=l&&h>a?o:h,t.y=g&&s>n?u:s,t.width=l?0:o-h,t.height=g?0:u-s,e<0&&(t.x+=t.width,t.width=-t.width),r<0&&(t.y+=t.height,t.height=-t.height),l||g},polar:function(i,t){var e=t.r0<=t.r?1:-1;if(e<0){var r=t.r;t.r=t.r0,t.r0=r}var a=Y(t.r,i.r),n=F(t.r0,i.r0);t.r=a,t.r0=n;var h=a-n<0;if(e<0){var r=t.r;t.r=t.r0,t.r0=r}return h}},J={cartesian2d:function(i,t,e,r,a,n,h,o,s){var u=new he({shape:le({},r),z2:1});if(u.__dataIndex=e,u.name="item",n){var l=u.shape,g=a?"height":"width";l[g]=0}return u},polar:function(i,t,e,r,a,n,h,o,s){var u=!a&&s?q:oe,l=new u({shape:r,z2:1});l.name="item";var g=ge(a);if(l.calculateTextPosition=Ge(g,{isRoundCap:u===q}),n){var d=l.shape,v=a?"r":"endAngle",c={};d[v]=a?r.r0:r.startAngle,c[v]=r[v],(o?k:V)(l,{shape:c},n)}return l}};function ze(i,t){var e=i.get("realtimeSort",!0),r=t.getBaseAxis();if(e&&r.type==="category"&&t.type==="cartesian2d")return{baseAxis:r,otherAxis:t.getOtherAxis(r)}}function K(i,t,e,r,a,n,h,o){var s,u;n?(u={x:r.x,width:r.width},s={y:r.y,height:r.height}):(u={y:r.y,height:r.height},s={x:r.x,width:r.width}),o||(h?k:V)(e,{shape:s},t,a,null);var l=t?i.baseAxis.model:null;(h?k:V)(e,{shape:u},l,a)}function Q(i,t){for(var e=0;e<t.length;e++)if(!isFinite(i[t[e]]))return!0;return!1}var Xe=["x","y","width","height"],Ze=["cx","cy","r","startAngle","endAngle"],$={cartesian2d:function(i){return!Q(i,Xe)},polar:function(i){return!Q(i,Ze)}},M={cartesian2d:function(i,t,e){var r=i.getItemLayout(t),a=e?qe(e,r):0,n=r.width>0?1:-1,h=r.height>0?1:-1;return{x:r.x+n*a/2,y:r.y+h*a/2,width:r.width-n*a,height:r.height-h*a}},polar:function(i,t,e){var r=i.getItemLayout(t);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}}};function je(i){return i.startAngle!=null&&i.endAngle!=null&&i.startAngle===i.endAngle}function ge(i){return function(t){var e=t?"Arc":"Angle";return function(r){switch(r){case"start":case"insideStart":case"end":case"insideEnd":return r+e;default:return r}}}(i)}function ee(i,t,e,r,a,n,h,o){var s=t.getItemVisual(e,"style");if(o){if(!n.get("roundCap")){var l=i.shape,g=Be(r.getModel("itemStyle"),l,!0);le(l,g),i.setShape(l)}}else{var u=r.get(["itemStyle","borderRadius"])||0;i.setShape("r",u)}i.useStyle(s);var d=r.getShallow("cursor");d&&i.attr("cursor",d);var v=o?h?a.r>=a.r0?"endArc":"startArc":a.endAngle>=a.startAngle?"endAngle":"startAngle":h?a.height>=0?"bottom":"top":a.width>=0?"right":"left",c=De(r);we(i,c,{labelFetcher:n,labelDataIndex:e,defaultText:Ve(n.getData(),e),inheritColor:s.fill,defaultOpacity:s.opacity,defaultOutsidePosition:v});var S=i.getTextContent();if(o&&S){var A=r.get(["label","position"]);i.textConfig.inside=A==="middle"?!0:null,Fe(i,A==="outside"?v:A,ge(h),r.get(["label","rotate"]))}Le(S,c,n.getRawValue(e),function(f){return Ee(t,f)});var y=r.getModel(["emphasis"]);Ie(i,y.get("focus"),y.get("blurScope"),y.get("disabled")),Pe(i,r),je(a)&&(i.style.fill="none",i.style.stroke="none",ne(i.states,function(f){f.style&&(f.style.fill=f.style.stroke="none")}))}function qe(i,t){var e=i.get(["itemStyle","borderColor"]);if(!e||e==="none")return 0;var r=i.get(["itemStyle","borderWidth"])||0,a=isNaN(t.width)?Number.MAX_VALUE:Math.abs(t.width),n=isNaN(t.height)?Number.MAX_VALUE:Math.abs(t.height);return Math.min(r,a,n)}var He=function(){function i(){}return i}(),te=function(i){E(t,i);function t(e){var r=i.call(this,e)||this;return r.type="largeBar",r}return t.prototype.getDefaultShape=function(){return new He},t.prototype.buildPath=function(e,r){for(var a=r.points,n=this.baseDimIdx,h=1-this.baseDimIdx,o=[],s=[],u=this.barWidth,l=0;l<a.length;l+=3)s[n]=u,s[h]=a[l+2],o[n]=a[l+n],o[h]=a[l+h],e.rect(o[0],o[1],s[0],s[1])},t}(Oe);function re(i,t,e,r){var a=i.getData(),n=a.getLayout("valueAxisHorizontal")?1:0,h=a.getLayout("largeDataIndices"),o=a.getLayout("size"),s=i.getModel("backgroundStyle"),u=a.getLayout("largeBackgroundPoints");if(u){var l=new te({shape:{points:u},incremental:!!r,silent:!0,z2:0});l.baseDimIdx=n,l.largeDataIndices=h,l.barWidth=o,l.useStyle(s.getItemStyle()),t.add(l),e&&e.push(l)}var g=new te({shape:{points:a.getLayout("largePoints")},incremental:!!r,ignoreCoarsePointer:!0,z2:1});g.baseDimIdx=n,g.largeDataIndices=h,g.barWidth=o,t.add(g),g.useStyle(a.getVisual("style")),g.style.stroke=null,z(g).seriesIndex=i.seriesIndex,i.get("silent")||(g.on("mousedown",ae),g.on("mousemove",ae)),e&&e.push(g)}var ae=Ce(function(i){var t=this,e=Je(t,i.offsetX,i.offsetY);z(t).dataIndex=e>=0?e:null},30,!1);function Je(i,t,e){for(var r=i.baseDimIdx,a=1-r,n=i.shape.points,h=i.largeDataIndices,o=[],s=[],u=i.barWidth,l=0,g=n.length/3;l<g;l++){var d=l*3;if(s[r]=u,s[a]=n[d+2],o[r]=n[d+r],o[a]=n[d+a],s[a]<0&&(o[a]+=s[a],s[a]=-s[a]),t>=o[0]&&t<=o[0]+s[0]&&e>=o[1]&&e<=o[1]+s[1])return h[l]}return-1}function ce(i,t,e){if(ue(e,"cartesian2d")){var r=t,a=e.getArea();return{x:i?r.x:a.x,y:i?a.y:r.y,width:i?r.width:a.width,height:i?a.height:r.height}}else{var a=e.getArea(),n=t;return{cx:a.cx,cy:a.cy,r0:i?a.r0:n.r0,r:i?a.r:n.r,startAngle:i?n.startAngle:0,endAngle:i?n.endAngle:Math.PI*2}}}function Ke(i,t,e){var r=i.type==="polar"?oe:he;return new r({shape:ce(t,e,i),silent:!0,z2:0})}function tt(i){i.registerChartView(Ue),i.registerSeriesModel(We),i.registerLayout(i.PRIORITY.VISUAL.LAYOUT,ke(Te,"bar")),i.registerLayout(i.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Re("bar")),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,Ne("bar")),i.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(t,e){var r=t.componentType||"series";e.eachComponent({mainType:r,query:t},function(a){t.sortInfo&&a.axis.setCategorySortInfo(t.sortInfo)})})}export{tt as i};
