import{c3 as P,c2 as W,r as y,cb as j,j as x,aV as O,c5 as V,el as L,em as M,aF as a,c6 as k,cd as F,ce as z,cl as H}from"./index-CP4gzJXp.js";import{g as N}from"./getValidReactChildren-BILH45Ct.js";function U(o){return W("MuiButtonGroup",o)}const t=P("MuiButtonGroup",["root","contained","outlined","text","disableElevation","disabled","firstButton","fullWidth","horizontal","vertical","colorPrimary","colorSecondary","grouped","groupedHorizontal","groupedVertical","groupedText","groupedTextHorizontal","groupedTextVertical","groupedTextPrimary","groupedTextSecondary","groupedOutlined","groupedOutlinedHorizontal","groupedOutlinedVertical","groupedOutlinedPrimary","groupedOutlinedSecondary","groupedContained","groupedContainedHorizontal","groupedContainedVertical","groupedContainedPrimary","groupedContainedSecondary","lastButton","middleButton"]),D=(o,r)=>{const{ownerState:e}=o;return[{[`& .${t.grouped}`]:r.grouped},{[`& .${t.grouped}`]:r[`grouped${a(e.orientation)}`]},{[`& .${t.grouped}`]:r[`grouped${a(e.variant)}`]},{[`& .${t.grouped}`]:r[`grouped${a(e.variant)}${a(e.orientation)}`]},{[`& .${t.grouped}`]:r[`grouped${a(e.variant)}${a(e.color)}`]},{[`& .${t.firstButton}`]:r.firstButton},{[`& .${t.lastButton}`]:r.lastButton},{[`& .${t.middleButton}`]:r.middleButton},r.root,r[e.variant],e.disableElevation===!0&&r.disableElevation,e.fullWidth&&r.fullWidth,e.orientation==="vertical"&&r.vertical]},q=o=>{const{classes:r,color:e,disabled:u,disableElevation:c,fullWidth:B,orientation:i,variant:n}=o,l={root:["root",n,i,B&&"fullWidth",c&&"disableElevation",`color${a(e)}`],grouped:["grouped",`grouped${a(i)}`,`grouped${a(n)}`,`grouped${a(n)}${a(i)}`,`grouped${a(n)}${a(e)}`,u&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return k(l,U,r)},A=O("div",{name:"MuiButtonGroup",slot:"Root",overridesResolver:D})(F(({theme:o})=>({display:"inline-flex",borderRadius:(o.vars||o).shape.borderRadius,variants:[{props:{variant:"contained"},style:{boxShadow:(o.vars||o).shadows[2]}},{props:{disableElevation:!0},style:{boxShadow:"none"}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{orientation:"vertical"},style:{flexDirection:"column",[`& .${t.lastButton},& .${t.middleButton}`]:{borderTopRightRadius:0,borderTopLeftRadius:0},[`& .${t.firstButton},& .${t.middleButton}`]:{borderBottomRightRadius:0,borderBottomLeftRadius:0}}},{props:{orientation:"horizontal"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${t.lastButton},& .${t.middleButton}`]:{borderTopLeftRadius:0,borderBottomLeftRadius:0}}},{props:{variant:"text",orientation:"horizontal"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderRight:o.vars?`1px solid rgba(${o.vars.palette.common.onBackgroundChannel} / 0.23)`:`1px solid ${o.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"}`,[`&.${t.disabled}`]:{borderRight:`1px solid ${(o.vars||o).palette.action.disabled}`}}}},{props:{variant:"text",orientation:"vertical"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderBottom:o.vars?`1px solid rgba(${o.vars.palette.common.onBackgroundChannel} / 0.23)`:`1px solid ${o.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"}`,[`&.${t.disabled}`]:{borderBottom:`1px solid ${(o.vars||o).palette.action.disabled}`}}}},...Object.entries(o.palette).filter(z()).flatMap(([r])=>[{props:{variant:"text",color:r},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderColor:o.vars?`rgba(${o.vars.palette[r].mainChannel} / 0.5)`:H(o.palette[r].main,.5)}}}]),{props:{variant:"outlined",orientation:"horizontal"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderRightColor:"transparent","&:hover":{borderRightColor:"currentColor"}},[`& .${t.lastButton},& .${t.middleButton}`]:{marginLeft:-1}}},{props:{variant:"outlined",orientation:"vertical"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderBottomColor:"transparent","&:hover":{borderBottomColor:"currentColor"}},[`& .${t.lastButton},& .${t.middleButton}`]:{marginTop:-1}}},{props:{variant:"contained",orientation:"horizontal"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderRight:`1px solid ${(o.vars||o).palette.grey[400]}`,[`&.${t.disabled}`]:{borderRight:`1px solid ${(o.vars||o).palette.action.disabled}`}}}},{props:{variant:"contained",orientation:"vertical"},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderBottom:`1px solid ${(o.vars||o).palette.grey[400]}`,[`&.${t.disabled}`]:{borderBottom:`1px solid ${(o.vars||o).palette.action.disabled}`}}}},...Object.entries(o.palette).filter(z(["dark"])).map(([r])=>({props:{variant:"contained",color:r},style:{[`& .${t.firstButton},& .${t.middleButton}`]:{borderColor:(o.vars||o).palette[r].dark}}}))],[`& .${t.grouped}`]:{minWidth:40,boxShadow:"none",props:{variant:"contained"},style:{"&:hover":{boxShadow:"none"}}}}))),K=y.forwardRef(function(r,e){const u=j({props:r,name:"MuiButtonGroup"}),{children:c,className:B,color:i="primary",component:n="div",disabled:l=!1,disableElevation:g=!1,disableFocusRipple:$=!1,disableRipple:b=!1,fullWidth:v=!1,orientation:G="horizontal",size:f="medium",variant:m="outlined",...S}=u,C={...u,color:i,component:n,disabled:l,disableElevation:g,disableFocusRipple:$,disableRipple:b,fullWidth:v,orientation:G,size:f,variant:m},d=q(C),T=y.useMemo(()=>({className:d.grouped,color:i,disabled:l,disableElevation:g,disableFocusRipple:$,disableRipple:b,fullWidth:v,size:f,variant:m}),[i,l,g,$,b,v,f,m,d.grouped]),R=N(c),w=R.length,E=p=>{const s=p===0,h=p===w-1;return s&&h?"":s?d.firstButton:h?d.lastButton:d.middleButton};return x.jsx(A,{as:n,role:"group",className:V(d.root,B),ref:e,ownerState:C,...S,children:x.jsx(L.Provider,{value:T,children:R.map((p,s)=>x.jsx(M.Provider,{value:E(s),children:p},s))})})});export{K as B,t as b,U as g};
