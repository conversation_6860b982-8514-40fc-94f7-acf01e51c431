import{j as l,c as i,M as m}from"./index-CP4gzJXp.js";import{S as o}from"./StyledFormControl-S1BH4GL9.js";const d=[{value:1,label:"Last month"},{value:6,label:"Last 6 months"},{value:12,label:"Last 12 months"}],c=({options:t=d,onChange:a,defaultValue:s,sx:n})=>{const r=e=>{a&&a(e)};return l.jsx(o,{sx:{width:150,minWidth:120,...n},size:"small",variant:"filled",children:l.jsx(i,{select:!0,defaultValue:s,size:"small",onChange:({target:{value:e}})=>r(e),children:t.map(e=>l.jsx(m,{value:e.value,children:e.label},e.value))})})};export{c as D};
