<?php $__env->startSection('title', __('auth.forgot_password') . ' - SMM Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-header">
    <h1><?php echo e(__('auth.reset_password')); ?></h1>
    <p><?php echo e(__('auth.send_reset_link')); ?></p>
</div>

<?php if($errors->any()): ?>
    <div class="alert alert-error">
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div><?php echo e($error); ?></div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php endif; ?>

<?php if(session('status')): ?>
    <div class="alert alert-success">
        <?php echo e(session('status')); ?>

    </div>
<?php endif; ?>

<form method="POST" action="<?php echo e(route('password.email')); ?>">
    <?php echo csrf_field(); ?>
    
    <div class="form-group">
        <label for="email" class="form-label"><?php echo e(__('auth.email')); ?></label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="<?php echo e(old('email')); ?>" 
            required 
            autocomplete="email"
            placeholder="<?php echo e(__('auth.email')); ?>"
        >
    </div>

    <button type="submit" class="btn-primary">
        <?php echo e(__('auth.send_reset_link')); ?>

    </button>
</form>

<div class="auth-links">
    <div>
        <a href="<?php echo e(route('login')); ?>"><?php echo e(__('auth.sign_in_here')); ?></a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('auth.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Làm\SmmPanel\resources\views/auth/forgot-password.blade.php ENDPATH**/ ?>