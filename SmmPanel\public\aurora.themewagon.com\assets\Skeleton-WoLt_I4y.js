import{c3 as w,c2 as x,r as b,cb as k,j as $,aV as R,c5 as S,c6 as M,cd as A,cl as U,ch as m,ci as g}from"./index-CP4gzJXp.js";function N(t){return String(parseFloat(t)).length===String(t).length}function v(t){return String(t).match(/[\d.\-+]*\s*(.*)/)[1]||""}function o(t){return parseFloat(t)}function P(t){return(e,a)=>{const n=v(e);if(n===a)return e;let s=o(e);n!=="px"&&(n==="em"||n==="rem")&&(s=o(e)*o(t));let r=s;if(a!=="px")if(a==="em")r=s/o(t);else if(a==="rem")r=s/o(t);else return e;return parseFloat(r.toFixed(5))+a}}function T({size:t,grid:e}){const a=t-t%e,n=a+e;return t-a<n-t?a:n}function V({lineHeight:t,pixels:e,htmlFontSize:a}){return e/(t*a)}function W({cssProperty:t,min:e,max:a,unit:n="rem",breakpoints:s=[600,900,1200],transform:r=null}){const i={[t]:`${e}${n}`},c=(a-e)/s[s.length-1];return s.forEach(u=>{let l=e+c*u;r!==null&&(l=r(l)),i[`@media (min-width:${u}px)`]={[t]:`${Math.round(l*1e4)/1e4}${n}`}}),i}function X(t){return x("MuiSkeleton",t)}const D=w("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]),j=t=>{const{classes:e,variant:a,animation:n,hasChildren:s,width:r,height:i}=t;return M({root:["root",a,n,s&&"withChildren",s&&!r&&"fitContent",s&&!i&&"heightAuto"]},X,e)},p=g`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`,h=g`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`,B=typeof p!="string"?m`
        animation: ${p} 2s ease-in-out 0.5s infinite;
      `:null,E=typeof h!="string"?m`
        &::after {
          animation: ${h} 2s linear 0.5s infinite;
        }
      `:null,L=R("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,e[a.variant],a.animation!==!1&&e[a.animation],a.hasChildren&&e.withChildren,a.hasChildren&&!a.width&&e.fitContent,a.hasChildren&&!a.height&&e.heightAuto]}})(A(({theme:t})=>{const e=v(t.shape.borderRadius)||"px",a=o(t.shape.borderRadius);return{display:"block",backgroundColor:t.vars?t.vars.palette.Skeleton.bg:U(t.palette.text.primary,t.palette.mode==="light"?.11:.13),height:"1.2em",variants:[{props:{variant:"text"},style:{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${a}${e}/${Math.round(a/.6*10)/10}${e}`,"&:empty:before":{content:'"\\00a0"'}}},{props:{variant:"circular"},style:{borderRadius:"50%"}},{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:({ownerState:n})=>n.hasChildren,style:{"& > *":{visibility:"hidden"}}},{props:({ownerState:n})=>n.hasChildren&&!n.width,style:{maxWidth:"fit-content"}},{props:({ownerState:n})=>n.hasChildren&&!n.height,style:{height:"auto"}},{props:{animation:"pulse"},style:B||{animation:`${p} 2s ease-in-out 0.5s infinite`}},{props:{animation:"wave"},style:{position:"relative",overflow:"hidden",WebkitMaskImage:"-webkit-radial-gradient(white, black)","&::after":{background:`linear-gradient(
                90deg,
                transparent,
                ${(t.vars||t).palette.action.hover},
                transparent
              )`,content:'""',position:"absolute",transform:"translateX(-100%)",bottom:0,left:0,right:0,top:0}}},{props:{animation:"wave"},style:E||{"&::after":{animation:`${h} 2s linear 0.5s infinite`}}}]}})),F=b.forwardRef(function(e,a){const n=k({props:e,name:"MuiSkeleton"}),{animation:s="pulse",className:r,component:i="span",height:c,style:u,variant:l="text",width:y,...d}=n,f={...n,animation:s,component:i,variant:l,hasChildren:!!d.children},C=j(f);return $.jsx(L,{as:i,ref:a,className:S(C.root,r),ownerState:f,...d,style:{width:y,height:c,...u}})});export{F as S,T as a,v as b,P as c,V as f,X as g,N as i,W as r,D as s,o as t};
