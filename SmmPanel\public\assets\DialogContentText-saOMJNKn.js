import{c3 as l,c2 as c,r as i,cb as x,j as C,aV as g,c5 as p,c6 as u,T as d,eg as T}from"./index-CP4gzJXp.js";function m(t){return c("MuiDialogContentText",t)}const w=l("MuiDialogContentText",["root"]),D=t=>{const{classes:s}=t,o=u({root:["root"]},m,s);return{...s,...o}},y=g(d,{shouldForwardProp:t=>T(t)||t==="classes",name:"MuiDialogContentText",slot:"Root"})({}),M=i.forwardRef(function(s,e){const o=x({props:s,name:"MuiDialogContentText"}),{children:f,className:r,...a}=o,n=D(a);return C.jsx(y,{component:"p",variant:"body1",color:"textSecondary",ref:e,ownerState:a,className:p(n.root,r),...o,classes:n})});export{M as D,w as d,m as g};
