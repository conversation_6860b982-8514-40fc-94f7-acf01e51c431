import{r,j as e,K as a,T as s,L as l,d as c,bp as p}from"./index-CP4gzJXp.js";import{A as x}from"./Alert-BWvPB4gW.js";const d=({docLink:t,sx:o})=>{const[n,i]=r.useState(!0);return e.jsx(a,{in:n,children:e.jsxs(x,{severity:"info",sx:{maxWidth:480,[`& .${p.action}`]:{pl:0},...o},icon:e.jsx(c,{icon:"material-symbols:info-outline-rounded"}),onClose:()=>{i(!1)},children:[e.jsx(s,{sx:{fontWeight:700,mb:1},children:"This is a View-Only page"}),e.jsxs(s,{variant:"body2",children:["Please follow the",e.jsx(l,{href:t,sx:{mx:.5},children:"documentation"}),"to implement it in your projects after getting full access to the purchased theme."]})]})})};export{d as V};
