import{j as s,S as t,B as k,al as o}from"./index-CP4gzJXp.js";const n="M",e=400,r=400,i={g:"@lottiefiles/creator 1.30.0"},c=[{ty:4,nm:"S",sr:1,st:0,op:120,ip:0,hasMask:!1,ao:0,ks:{a:{a:0,k:[0,0,0]},s:{a:0,k:[100,100,100]},sk:{a:0,k:0},p:{a:0,k:[200,200,0]},r:{a:1,k:[{o:{x:.333,y:.123},i:{x:.667,y:.877},s:[0],t:0},{s:[360],t:40}]},sa:{a:0,k:0},o:{a:0,k:100}},shapes:[{ty:"gr",nm:"E",it:[{ty:"el",nm:"E",d:1,p:{a:0,k:[0,0]},s:{a:0,k:[200,200]}},{ty:"st",nm:"S",lc:2,lj:1,ml:4,o:{a:0,k:100},w:{a:0,k:30},c:{a:0,k:[.2,.522,.941]}},{ty:"tr",a:{a:0,k:[0,0]},s:{a:0,k:[100,100]},sk:{a:0,k:0},p:{a:0,k:[0,0]},r:{a:0,k:0},sa:{a:0,k:0},o:{a:0,k:100}}]},{ty:"tm",nm:"T",e:{a:1,k:[{o:{x:.61,y:0},i:{x:.39,y:1},s:[.1],t:0},{s:[100],t:30}]},o:{a:0,k:0},s:{a:1,k:[{o:{x:.61,y:0},i:{x:.39,y:1},s:[0],t:10},{s:[99.99],t:40}]},m:1}],ind:1},{ty:4,nm:"S",sr:1,st:0,op:120,ip:0,hasMask:!1,ao:0,ks:{a:{a:0,k:[0,0]},s:{a:0,k:[100,100,100]},sk:{a:0,k:0},p:{a:0,k:[200,200]},r:{a:0,k:0},sa:{a:0,k:0},o:{a:0,k:20}},shapes:[{ty:"gr",nm:"E",it:[{ty:"el",nm:"E",d:1,p:{a:0,k:[0,0]},s:{a:0,k:[200,200]}},{ty:"st",nm:"S",lc:2,lj:1,ml:4,o:{a:0,k:100},w:{a:0,k:30},c:{a:0,k:[.2,.522,.941]}},{ty:"tr",a:{a:0,k:[0,0]},s:{a:0,k:[100,100]},sk:{a:0,k:0},p:{a:0,k:[0,0]},r:{a:0,k:0},sa:{a:0,k:0},o:{a:0,k:100}}]},{ty:"tm",nm:"T",e:{a:0,k:100},o:{a:0,k:0},s:{a:0,k:0},m:1}],ind:2}],m="5.7.0",y=30,l=40,x=0,p=[],h={nm:n,h:e,w:r,meta:i,layers:c,v:m,fr:y,op:l,ip:x,assets:p},j=a=>s.jsx(t,{...a,sx:[{alignItems:"center",justifyContent:"center",height:1},...Array.isArray(a.sx)?a.sx:[a.sx]],children:s.jsx(k,{sx:{height:130,width:130,opacity:.7},children:s.jsx(o,{animationData:h})})});export{j as default};
