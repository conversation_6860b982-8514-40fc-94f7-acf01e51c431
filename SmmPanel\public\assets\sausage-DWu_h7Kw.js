import{_ as Z,B as Ht,G as x,f as nt,o as Bt,U as ft,V as Ut,W as Wt,X as Xt,u as Q,Y as Zt,i as tt,J as st,$ as jt,a as qt,b as ct,M as Et,t as ot,E as K,a0 as gt,a1 as Jt,I as $t,a2 as Kt,N as ut,a3 as Y,m as Nt,a4 as j,a5 as Qt,a6 as mt,a7 as pt,P as vt,r as $,S as Yt,K as xt,k as J,a8 as te,a9 as yt,aa as bt,d as dt,ab as St,C as at,ac as _t,Z as ee,ad as ae,ae as re,e as Ct,T as ie,af as Dt,ag as ne,ah as oe,j as le}from"./ReactEchart-C_a4bTea.js";var se=function(i){Z(e,i);function e(){var t=i!==null&&i.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t){return Ht(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var a=new x,o=nt("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);a.add(o),o.setStyle(t.lineStyle);var r=this.getData().getVisual("symbol"),n=this.getData().getVisual("symbolRotate"),l=r==="none"?"circle":r,u=t.itemHeight*.8,s=nt(l,(t.itemWidth-u)/2,(t.itemHeight-u)/2,u,u,t.itemStyle.fill);a.add(s),s.setStyle(t.itemStyle);var h=t.iconRotate==="inherit"?n:t.iconRotate||0;return s.rotation=h*Math.PI/180,s.setOrigin([t.itemWidth/2,t.itemHeight/2]),l.indexOf("empty")>-1&&(s.style.stroke=s.style.fill,s.style.fill="#fff",s.style.lineWidth=2),a},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(Bt);function Ot(i,e){var t=i.mapDimensionsAll("defaultedLabel"),a=t.length;if(a===1){var o=ft(i,e,t[0]);return o!=null?o+"":null}else if(a){for(var r=[],n=0;n<t.length;n++)r.push(ft(i,e,t[n]));return r.join(" ")}}function ue(i,e){var t=i.mapDimensionsAll("defaultedLabel");if(!Ut(e))return e+"";for(var a=[],o=0;o<t.length;o++){var r=i.getDimensionIndex(t[o]);r>=0&&a.push(e[r])}return a.join(" ")}var ht=function(i){Z(e,i);function e(t,a,o,r){var n=i.call(this)||this;return n.updateData(t,a,o,r),n}return e.prototype._createSymbol=function(t,a,o,r,n){this.removeAll();var l=nt(t,-1,-1,2,2,null,n);l.attr({z2:100,culling:!0,scaleX:r[0]/2,scaleY:r[1]/2}),l.drift=ve,this._symbolType=t,this.add(l)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){Wt(this.childAt(0))},e.prototype.downplay=function(){Xt(this.childAt(0))},e.prototype.setZ=function(t,a){var o=this.childAt(0);o.zlevel=t,o.z=a},e.prototype.setDraggable=function(t,a){var o=this.childAt(0);o.draggable=t,o.cursor=!a&&t?"move":o.cursor},e.prototype.updateData=function(t,a,o,r){this.silent=!1;var n=t.getItemVisual(a,"symbol")||"circle",l=t.hostModel,u=e.getSymbolSize(t,a),s=n!==this._symbolType,h=r&&r.disableAnimation;if(s){var v=t.getItemVisual(a,"symbolKeepAspect");this._createSymbol(n,t,a,u,v)}else{var c=this.childAt(0);c.silent=!1;var f={scaleX:u[0]/2,scaleY:u[1]/2};h?c.attr(f):Q(c,f,l,a),Zt(c)}if(this._updateCommon(t,a,u,o,r),s){var c=this.childAt(0);if(!h){var f={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,tt(c,f,l,a)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,a,o,r,n){var l=this.childAt(0),u=t.hostModel,s,h,v,c,f,g,m,d,y;if(r&&(s=r.emphasisItemStyle,h=r.blurItemStyle,v=r.selectItemStyle,c=r.focus,f=r.blurScope,m=r.labelStatesModels,d=r.hoverScale,y=r.cursorStyle,g=r.emphasisDisabled),!r||t.hasItemOption){var p=r&&r.itemModel?r.itemModel:t.getItemModel(a),b=p.getModel("emphasis");s=b.getModel("itemStyle").getItemStyle(),v=p.getModel(["select","itemStyle"]).getItemStyle(),h=p.getModel(["blur","itemStyle"]).getItemStyle(),c=b.get("focus"),f=b.get("blurScope"),g=b.get("disabled"),m=st(p),d=b.getShallow("scale"),y=p.getShallow("cursor")}var P=t.getItemVisual(a,"symbolRotate");l.attr("rotation",(P||0)*Math.PI/180||0);var S=jt(t.getItemVisual(a,"symbolOffset"),o);S&&(l.x=S[0],l.y=S[1]),y&&l.attr("cursor",y);var _=t.getItemVisual(a,"style"),D=_.fill;if(l instanceof qt){var L=l.style;l.useStyle(ct({image:L.image,x:L.x,y:L.y,width:L.width,height:L.height},_))}else l.__isEmptyBrush?l.useStyle(ct({},_)):l.useStyle(_),l.style.decal=null,l.setColor(D,n&&n.symbolInnerColor),l.style.strokeNoScale=!0;var w=t.getItemVisual(a,"liftZ"),A=this._z2;w!=null?A==null&&(this._z2=l.z2,l.z2+=w):A!=null&&(l.z2=A,this._z2=null);var k=n&&n.useNameLabel;Et(l,m,{labelFetcher:u,labelDataIndex:a,defaultText:I,inheritColor:D,defaultOpacity:_.opacity});function I(C){return k?t.getName(C):Ot(t,C)}this._sizeX=o[0]/2,this._sizeY=o[1]/2;var T=l.ensureState("emphasis");T.style=s,l.ensureState("select").style=v,l.ensureState("blur").style=h;var M=d==null||d===!0?Math.max(1.1,3/this._sizeY):isFinite(d)&&d>0?+d:1;T.scaleX=this._sizeX*M,T.scaleY=this._sizeY*M,this.setSymbolScale(1),ot(this,c,f,g)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,a,o){var r=this.childAt(0),n=K(this).dataIndex,l=o&&o.animation;if(this.silent=r.silent=!0,o&&o.fadeLabel){var u=r.getTextContent();u&&gt(u,{style:{opacity:0}},a,{dataIndex:n,removeOpt:l,cb:function(){r.removeTextContent()}})}else r.removeTextContent();gt(r,{style:{opacity:0},scaleX:0,scaleY:0},a,{dataIndex:n,cb:t,removeOpt:l})},e.getSymbolSize=function(t,a){return Jt(t.getItemVisual(a,"symbolSize"))},e}(x);function ve(i,e){this.parent.drift(i,e)}function rt(i,e,t,a){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(a.isIgnore&&a.isIgnore(t))&&!(a.clipShape&&!a.clipShape.contain(e[0],e[1]))&&i.getItemVisual(t,"symbol")!=="none"}function Pt(i){return i!=null&&!Kt(i)&&(i={isIgnore:i}),i||{}}function At(i){var e=i.hostModel,t=e.getModel("emphasis");return{emphasisItemStyle:t.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:t.get("focus"),blurScope:t.get("blurScope"),emphasisDisabled:t.get("disabled"),hoverScale:t.get("scale"),labelStatesModels:st(e),cursorStyle:e.get("cursor")}}var he=function(){function i(e){this.group=new x,this._SymbolCtor=e||ht}return i.prototype.updateData=function(e,t){this._progressiveEls=null,t=Pt(t);var a=this.group,o=e.hostModel,r=this._data,n=this._SymbolCtor,l=t.disableAnimation,u=At(e),s={disableAnimation:l},h=t.getSymbolPoint||function(v){return e.getItemLayout(v)};r||a.removeAll(),e.diff(r).add(function(v){var c=h(v);if(rt(e,c,v,t)){var f=new n(e,v,u,s);f.setPosition(c),e.setItemGraphicEl(v,f),a.add(f)}}).update(function(v,c){var f=r.getItemGraphicEl(c),g=h(v);if(!rt(e,g,v,t)){a.remove(f);return}var m=e.getItemVisual(v,"symbol")||"circle",d=f&&f.getSymbolType&&f.getSymbolType();if(!f||d&&d!==m)a.remove(f),f=new n(e,v,u,s),f.setPosition(g);else{f.updateData(e,v,u,s);var y={x:g[0],y:g[1]};l?f.attr(y):Q(f,y,o)}a.add(f),e.setItemGraphicEl(v,f)}).remove(function(v){var c=r.getItemGraphicEl(v);c&&c.fadeOut(function(){a.remove(c)},o)}).execute(),this._getSymbolPoint=h,this._data=e},i.prototype.updateLayout=function(){var e=this,t=this._data;t&&t.eachItemGraphicEl(function(a,o){var r=e._getSymbolPoint(o);a.setPosition(r),a.markRedraw()})},i.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=At(e),this._data=null,this.group.removeAll()},i.prototype.incrementalUpdate=function(e,t,a){this._progressiveEls=[],a=Pt(a);function o(u){u.isGroup||(u.incremental=!0,u.ensureState("emphasis").hoverLayer=!0)}for(var r=e.start;r<e.end;r++){var n=t.getItemLayout(r);if(rt(t,n,r,a)){var l=new this._SymbolCtor(t,r,this._seriesScope);l.traverse(o),l.setPosition(n),this.group.add(l),t.setItemGraphicEl(r,l),this._progressiveEls.push(l)}}},i.prototype.eachRendered=function(e){$t(this._progressiveEls||this.group,e)},i.prototype.remove=function(e){var t=this.group,a=this._data;a&&e?a.eachItemGraphicEl(function(o){o.fadeOut(function(){t.remove(o)},a.hostModel)}):t.removeAll()},i}();function Mt(i,e,t){var a=i.getBaseAxis(),o=i.getOtherAxis(a),r=fe(o,t),n=a.dim,l=o.dim,u=e.mapDimension(l),s=e.mapDimension(n),h=l==="x"||l==="radius"?1:0,v=ut(i.dimensions,function(g){return e.mapDimension(g)}),c=!1,f=e.getCalculationInfo("stackResultDimension");return Y(e,v[0])&&(c=!0,v[0]=f),Y(e,v[1])&&(c=!0,v[1]=f),{dataDimsForPoint:v,valueStart:r,valueAxisDim:l,baseAxisDim:n,stacked:!!c,valueDim:u,baseDim:s,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function fe(i,e){var t=0,a=i.scale.getExtent();return e==="start"?t=a[0]:e==="end"?t=a[1]:Nt(e)&&!isNaN(e)?t=e:a[0]>0?t=a[0]:a[1]<0&&(t=a[1]),t}function Gt(i,e,t,a){var o=NaN;i.stacked&&(o=t.get(t.getCalculationInfo("stackedOverDimension"),a)),isNaN(o)&&(o=i.valueStart);var r=i.baseDataOffset,n=[];return n[r]=t.get(i.baseDim,a),n[1-r]=o,e.dataToPoint(n)}function ce(i,e){var t=[];return e.diff(i).add(function(a){t.push({cmd:"+",idx:a})}).update(function(a,o){t.push({cmd:"=",idx:o,idx1:a})}).remove(function(a){t.push({cmd:"-",idx:a})}).execute(),t}function ge(i,e,t,a,o,r,n,l){for(var u=ce(i,e),s=[],h=[],v=[],c=[],f=[],g=[],m=[],d=Mt(o,e,n),y=i.getLayout("points")||[],p=e.getLayout("points")||[],b=0;b<u.length;b++){var P=u[b],S=!0,_=void 0,D=void 0;switch(P.cmd){case"=":_=P.idx*2,D=P.idx1*2;var L=y[_],w=y[_+1],A=p[D],k=p[D+1];(isNaN(L)||isNaN(w))&&(L=A,w=k),s.push(L,w),h.push(A,k),v.push(t[_],t[_+1]),c.push(a[D],a[D+1]),m.push(e.getRawIndex(P.idx1));break;case"+":var I=P.idx,T=d.dataDimsForPoint,M=o.dataToPoint([e.get(T[0],I),e.get(T[1],I)]);D=I*2,s.push(M[0],M[1]),h.push(p[D],p[D+1]);var C=Gt(d,o,e,I);v.push(C[0],C[1]),c.push(a[D],a[D+1]),m.push(e.getRawIndex(I));break;case"-":S=!1}S&&(f.push(P),g.push(g.length))}g.sort(function(G,et){return m[G]-m[et]});for(var O=s.length,V=j(O),E=j(O),N=j(O),F=j(O),H=[],b=0;b<g.length;b++){var q=g[b],z=b*2,R=q*2;V[z]=s[R],V[z+1]=s[R+1],E[z]=h[R],E[z+1]=h[R+1],N[z]=v[R],N[z+1]=v[R+1],F[z]=c[R],F[z+1]=c[R+1],H[b]=f[q]}return{current:V,next:E,stackedOnCurrent:N,stackedOnNext:F,status:H}}var B=Math.min,U=Math.max;function X(i,e){return isNaN(i)||isNaN(e)}function lt(i,e,t,a,o,r,n,l,u){for(var s,h,v,c,f,g,m=t,d=0;d<a;d++){var y=e[m*2],p=e[m*2+1];if(m>=o||m<0)break;if(X(y,p)){if(u){m+=r;continue}break}if(m===t)i[r>0?"moveTo":"lineTo"](y,p),v=y,c=p;else{var b=y-s,P=p-h;if(b*b+P*P<.5){m+=r;continue}if(n>0){for(var S=m+r,_=e[S*2],D=e[S*2+1];_===y&&D===p&&d<a;)d++,S+=r,m+=r,_=e[S*2],D=e[S*2+1],y=e[m*2],p=e[m*2+1],b=y-s,P=p-h;var L=d+1;if(u)for(;X(_,D)&&L<a;)L++,S+=r,_=e[S*2],D=e[S*2+1];var w=.5,A=0,k=0,I=void 0,T=void 0;if(L>=a||X(_,D))f=y,g=p;else{A=_-s,k=D-h;var M=y-s,C=_-y,O=p-h,V=D-p,E=void 0,N=void 0;if(l==="x"){E=Math.abs(M),N=Math.abs(C);var F=A>0?1:-1;f=y-F*E*n,g=p,I=y+F*N*n,T=p}else if(l==="y"){E=Math.abs(O),N=Math.abs(V);var H=k>0?1:-1;f=y,g=p-H*E*n,I=y,T=p+H*N*n}else E=Math.sqrt(M*M+O*O),N=Math.sqrt(C*C+V*V),w=N/(N+E),f=y-A*n*(1-w),g=p-k*n*(1-w),I=y+A*n*w,T=p+k*n*w,I=B(I,U(_,y)),T=B(T,U(D,p)),I=U(I,B(_,y)),T=U(T,B(D,p)),A=I-y,k=T-p,f=y-A*E/N,g=p-k*E/N,f=B(f,U(s,y)),g=B(g,U(h,p)),f=U(f,B(s,y)),g=U(g,B(h,p)),A=y-f,k=p-g,I=y+A*N/E,T=p+k*N/E}i.bezierCurveTo(v,c,f,g,y,p),v=I,c=T}else i.lineTo(y,p)}s=y,h=p,m+=r}return d}var zt=function(){function i(){this.smooth=0,this.smoothConstraint=!0}return i}(),me=function(i){Z(e,i);function e(t){var a=i.call(this,t)||this;return a.type="ec-polyline",a}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new zt},e.prototype.buildPath=function(t,a){var o=a.points,r=0,n=o.length/2;if(a.connectNulls){for(;n>0&&X(o[n*2-2],o[n*2-1]);n--);for(;r<n&&X(o[r*2],o[r*2+1]);r++);}for(;r<n;)r+=lt(t,o,r,n,n,1,a.smooth,a.smoothMonotone,a.connectNulls)+1},e.prototype.getPointOn=function(t,a){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var o=this.path,r=o.data,n=Qt.CMD,l,u,s=a==="x",h=[],v=0;v<r.length;){var c=r[v++],f=void 0,g=void 0,m=void 0,d=void 0,y=void 0,p=void 0,b=void 0;switch(c){case n.M:l=r[v++],u=r[v++];break;case n.L:if(f=r[v++],g=r[v++],b=s?(t-l)/(f-l):(t-u)/(g-u),b<=1&&b>=0){var P=s?(g-u)*b+u:(f-l)*b+l;return s?[t,P]:[P,t]}l=f,u=g;break;case n.C:f=r[v++],g=r[v++],m=r[v++],d=r[v++],y=r[v++],p=r[v++];var S=s?mt(l,f,m,y,t,h):mt(u,g,d,p,t,h);if(S>0)for(var _=0;_<S;_++){var D=h[_];if(D<=1&&D>=0){var P=s?pt(u,g,d,p,D):pt(l,f,m,y,D);return s?[t,P]:[P,t]}}l=y,u=p;break}}},e}(vt),pe=function(i){Z(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e}(zt),ye=function(i){Z(e,i);function e(t){var a=i.call(this,t)||this;return a.type="ec-polygon",a}return e.prototype.getDefaultShape=function(){return new pe},e.prototype.buildPath=function(t,a){var o=a.points,r=a.stackedOnPoints,n=0,l=o.length/2,u=a.smoothMonotone;if(a.connectNulls){for(;l>0&&X(o[l*2-2],o[l*2-1]);l--);for(;n<l&&X(o[n*2],o[n*2+1]);n++);}for(;n<l;){var s=lt(t,o,n,l,l,1,a.smooth,u,a.connectNulls);lt(t,r,n+s-1,s,l,-1,a.stackedOnSmooth,u,a.connectNulls),n+=s+1,t.closePath()}},e}(vt);function Rt(i,e,t,a,o){var r=i.getArea(),n=r.x,l=r.y,u=r.width,s=r.height,h=t.get(["lineStyle","width"])||0;n-=h/2,l-=h/2,u+=h,s+=h,u=Math.ceil(u),n!==Math.floor(n)&&(n=Math.floor(n),u++);var v=new xt({shape:{x:n,y:l,width:u,height:s}});if(e){var c=i.getBaseAxis(),f=c.isHorizontal(),g=c.inverse;f?(g&&(v.shape.x+=u),v.shape.width=0):(g||(v.shape.y+=s),v.shape.height=0);var m=J(o)?function(d){o(d,v)}:null;tt(v,{shape:{width:u,height:s,x:n,y:l}},t,null,a,m)}return v}function Vt(i,e,t){var a=i.getArea(),o=$(a.r0,1),r=$(a.r,1),n=new Yt({shape:{cx:$(i.cx,1),cy:$(i.cy,1),r0:o,r,startAngle:a.startAngle,endAngle:a.endAngle,clockwise:a.clockwise}});if(e){var l=i.getBaseAxis().dim==="angle";l?n.shape.endAngle=a.startAngle:n.shape.r=o,tt(n,{shape:{endAngle:a.endAngle,r}},t)}return n}function Ge(i,e,t,a,o){if(i){if(i.type==="polar")return Vt(i,e,t);if(i.type==="cartesian2d")return Rt(i,e,t,a,o)}else return null;return null}function be(i,e){return i.type===e}function It(i,e){if(i.length===e.length){for(var t=0;t<i.length;t++)if(i[t]!==e[t])return;return!0}}function wt(i){for(var e=1/0,t=1/0,a=-1/0,o=-1/0,r=0;r<i.length;){var n=i[r++],l=i[r++];isNaN(n)||(e=Math.min(n,e),a=Math.max(n,a)),isNaN(l)||(t=Math.min(l,t),o=Math.max(l,o))}return[[e,t],[a,o]]}function kt(i,e){var t=wt(i),a=t[0],o=t[1],r=wt(e),n=r[0],l=r[1];return Math.max(Math.abs(a[0]-n[0]),Math.abs(a[1]-n[1]),Math.abs(o[0]-l[0]),Math.abs(o[1]-l[1]))}function Lt(i){return Nt(i)?i:i?.5:0}function de(i,e,t){if(!t.valueDim)return[];for(var a=e.count(),o=j(a*2),r=0;r<a;r++){var n=Gt(t,i,e,r);o[r*2]=n[0],o[r*2+1]=n[1]}return o}function W(i,e,t,a,o){var r=t.getBaseAxis(),n=r.dim==="x"||r.dim==="radius"?0:1,l=[],u=0,s=[],h=[],v=[],c=[];if(o){for(u=0;u<i.length;u+=2){var f=e||i;!isNaN(f[u])&&!isNaN(f[u+1])&&c.push(i[u],i[u+1])}i=c}for(u=0;u<i.length-2;u+=2)switch(v[0]=i[u+2],v[1]=i[u+3],h[0]=i[u],h[1]=i[u+1],l.push(h[0],h[1]),a){case"end":s[n]=v[n],s[1-n]=h[1-n],l.push(s[0],s[1]);break;case"middle":var g=(h[n]+v[n])/2,m=[];s[n]=m[n]=g,s[1-n]=h[1-n],m[1-n]=v[1-n],l.push(s[0],s[1]),l.push(m[0],m[1]);break;default:s[n]=h[n],s[1-n]=v[1-n],l.push(s[0],s[1])}return l.push(i[u++],i[u++]),l}function Se(i,e){var t=[],a=i.length,o,r;function n(h,v,c){var f=h.coord,g=(c-f)/(v.coord-f),m=ne(g,[h.color,v.color]);return{coord:c,color:m}}for(var l=0;l<a;l++){var u=i[l],s=u.coord;if(s<0)o=u;else if(s>e){r?t.push(n(r,u,e)):o&&t.push(n(o,u,0),n(o,u,e));break}else o&&(t.push(n(o,u,0)),o=null),t.push(u),r=u}return t}function _e(i,e,t){var a=i.getVisual("visualMeta");if(!(!a||!a.length||!i.count())&&e.type==="cartesian2d"){for(var o,r,n=a.length-1;n>=0;n--){var l=i.getDimensionInfo(a[n].dimension);if(o=l&&l.coordDim,o==="x"||o==="y"){r=a[n];break}}if(r){var u=e.getAxis(o),s=ut(r.stops,function(b){return{coord:u.toGlobalCoord(u.dataToCoord(b.value)),color:b.color}}),h=s.length,v=r.outerColors.slice();h&&s[0].coord>s[h-1].coord&&(s.reverse(),v.reverse());var c=Se(s,o==="x"?t.getWidth():t.getHeight()),f=c.length;if(!f&&h)return s[0].coord<0?v[1]?v[1]:s[h-1].color:v[0]?v[0]:s[0].color;var g=10,m=c[0].coord-g,d=c[f-1].coord+g,y=d-m;if(y<.001)return"transparent";Ct(c,function(b){b.offset=(b.coord-m)/y}),c.push({offset:f?c[f-1].offset:.5,color:v[1]||"transparent"}),c.unshift({offset:f?c[0].offset:.5,color:v[0]||"transparent"});var p=new ie(0,0,0,0,c,!0);return p[o]=m,p[o+"2"]=d,p}}}function De(i,e,t){var a=i.get("showAllSymbol"),o=a==="auto";if(!(a&&!o)){var r=t.getAxesByScale("ordinal")[0];if(r&&!(o&&Pe(r,e))){var n=e.mapDimension(r.dim),l={};return Ct(r.getViewLabels(),function(u){var s=r.scale.getRawOrdinalNumber(u.tickValue);l[s]=1}),function(u){return!l.hasOwnProperty(e.get(n,u))}}}}function Pe(i,e){var t=i.getExtent(),a=Math.abs(t[1]-t[0])/i.scale.count();isNaN(a)&&(a=0);for(var o=e.count(),r=Math.max(1,Math.round(o/5)),n=0;n<o;n+=r)if(ht.getSymbolSize(e,n)[i.isHorizontal()?1:0]*1.5>a)return!1;return!0}function Ae(i,e){return isNaN(i)||isNaN(e)}function Ie(i){for(var e=i.length/2;e>0&&Ae(i[e*2-2],i[e*2-1]);e--);return e-1}function Tt(i,e){return[i[e*2],i[e*2+1]]}function we(i,e,t){for(var a=i.length/2,o=t==="x"?0:1,r,n,l=0,u=-1,s=0;s<a;s++)if(n=i[s*2+o],!(isNaN(n)||isNaN(i[s*2+1-o]))){if(s===0){r=n;continue}if(r<=e&&n>=e||r>=e&&n<=e){u=s;break}l=s,r=n}return{range:[l,u],t:(e-r)/(n-r)}}function Ft(i){if(i.get(["endLabel","show"]))return!0;for(var e=0;e<Dt.length;e++)if(i.get([Dt[e],"endLabel","show"]))return!0;return!1}function it(i,e,t,a){if(be(e,"cartesian2d")){var o=a.getModel("endLabel"),r=o.get("valueAnimation"),n=a.getData(),l={lastFrameIndex:0},u=Ft(a)?function(f,g){i._endLabelOnDuring(f,g,n,l,r,o,e)}:null,s=e.getBaseAxis().isHorizontal(),h=Rt(e,t,a,function(){var f=i._endLabel;f&&t&&l.originalX!=null&&f.attr({x:l.originalX,y:l.originalY})},u);if(!a.get("clip",!0)){var v=h.shape,c=Math.max(v.width,v.height);s?(v.y-=c,v.height+=c*2):(v.x-=c,v.width+=c*2)}return u&&u(1,h),h}else return Vt(e,t,a)}function ke(i,e){var t=e.getBaseAxis(),a=t.isHorizontal(),o=t.inverse,r=a?o?"right":"left":"center",n=a?"middle":o?"top":"bottom";return{normal:{align:i.get("align")||r,verticalAlign:i.get("verticalAlign")||n}}}var Le=function(i){Z(e,i);function e(){return i!==null&&i.apply(this,arguments)||this}return e.prototype.init=function(){var t=new x,a=new he;this.group.add(a.group),this._symbolDraw=a,this._lineGroup=t,this._changePolyState=te(this._changePolyState,this)},e.prototype.render=function(t,a,o){var r=t.coordinateSystem,n=this.group,l=t.getData(),u=t.getModel("lineStyle"),s=t.getModel("areaStyle"),h=l.getLayout("points")||[],v=r.type==="polar",c=this._coordSys,f=this._symbolDraw,g=this._polyline,m=this._polygon,d=this._lineGroup,y=!a.ssr&&t.get("animation"),p=!s.isEmpty(),b=s.get("origin"),P=Mt(r,l,b),S=p&&de(r,l,P),_=t.get("showSymbol"),D=t.get("connectNulls"),L=_&&!v&&De(t,l,r),w=this._data;w&&w.eachItemGraphicEl(function(G,et){G.__temp&&(n.remove(G),w.setItemGraphicEl(et,null))}),_||f.remove(),n.add(d);var A=v?!1:t.get("step"),k;r&&r.getArea&&t.get("clip",!0)&&(k=r.getArea(),k.width!=null?(k.x-=.1,k.y-=.1,k.width+=.2,k.height+=.2):k.r0&&(k.r0-=.5,k.r+=.5)),this._clipShapeForSymbol=k;var I=_e(l,r,o)||l.getVisual("style")[l.getVisual("drawType")];if(!(g&&c.type===r.type&&A===this._step))_&&f.updateData(l,{isIgnore:L,clipShape:k,disableAnimation:!0,getSymbolPoint:function(G){return[h[G*2],h[G*2+1]]}}),y&&this._initSymbolLabelAnimation(l,r,k),A&&(S&&(S=W(S,h,r,A,D)),h=W(h,null,r,A,D)),g=this._newPolyline(h),p?m=this._newPolygon(h,S):m&&(d.remove(m),m=this._polygon=null),v||this._initOrUpdateEndLabel(t,r,yt(I)),d.setClipPath(it(this,r,!0,t));else{p&&!m?m=this._newPolygon(h,S):m&&!p&&(d.remove(m),m=this._polygon=null),v||this._initOrUpdateEndLabel(t,r,yt(I));var T=d.getClipPath();if(T){var M=it(this,r,!1,t);tt(T,{shape:M.shape},t)}else d.setClipPath(it(this,r,!0,t));_&&f.updateData(l,{isIgnore:L,clipShape:k,disableAnimation:!0,getSymbolPoint:function(G){return[h[G*2],h[G*2+1]]}}),(!It(this._stackedOnPoints,S)||!It(this._points,h))&&(y?this._doUpdateAnimation(l,S,r,o,A,b,D):(A&&(S&&(S=W(S,h,r,A,D)),h=W(h,null,r,A,D)),g.setShape({points:h}),m&&m.setShape({points:h,stackedOnPoints:S})))}var C=t.getModel("emphasis"),O=C.get("focus"),V=C.get("blurScope"),E=C.get("disabled");if(g.useStyle(bt(u.getLineStyle(),{fill:"none",stroke:I,lineJoin:"bevel"})),dt(g,t,"lineStyle"),g.style.lineWidth>0&&t.get(["emphasis","lineStyle","width"])==="bolder"){var N=g.getState("emphasis").style;N.lineWidth=+g.style.lineWidth+1}K(g).seriesIndex=t.seriesIndex,ot(g,O,V,E);var F=Lt(t.get("smooth")),H=t.get("smoothMonotone");if(g.setShape({smooth:F,smoothMonotone:H,connectNulls:D}),m){var q=l.getCalculationInfo("stackedOnSeries"),z=0;m.useStyle(bt(s.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel",decal:l.getVisual("style").decal})),q&&(z=Lt(q.get("smooth"))),m.setShape({smooth:F,stackedOnSmooth:z,smoothMonotone:H,connectNulls:D}),dt(m,t,"areaStyle"),K(m).seriesIndex=t.seriesIndex,ot(m,O,V,E)}var R=this._changePolyState;l.eachItemGraphicEl(function(G){G&&(G.onHoverStateChange=R)}),this._polyline.onHoverStateChange=R,this._data=l,this._coordSys=r,this._stackedOnPoints=S,this._points=h,this._step=A,this._valueOrigin=b,t.get("triggerLineEvent")&&(this.packEventData(t,g),m&&this.packEventData(t,m))},e.prototype.packEventData=function(t,a){K(a).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,a,o,r){var n=t.getData(),l=St(n,r);if(this._changePolyState("emphasis"),!(l instanceof Array)&&l!=null&&l>=0){var u=n.getLayout("points"),s=n.getItemGraphicEl(l);if(!s){var h=u[l*2],v=u[l*2+1];if(isNaN(h)||isNaN(v)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(h,v))return;var c=t.get("zlevel")||0,f=t.get("z")||0;s=new ht(n,l),s.x=h,s.y=v,s.setZ(c,f);var g=s.getSymbolPath().getTextContent();g&&(g.zlevel=c,g.z=f,g.z2=this._polyline.z2+1),s.__temp=!0,n.setItemGraphicEl(l,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else at.prototype.highlight.call(this,t,a,o,r)},e.prototype.downplay=function(t,a,o,r){var n=t.getData(),l=St(n,r);if(this._changePolyState("normal"),l!=null&&l>=0){var u=n.getItemGraphicEl(l);u&&(u.__temp?(n.setItemGraphicEl(l,null),this.group.remove(u)):u.downplay())}else at.prototype.downplay.call(this,t,a,o,r)},e.prototype._changePolyState=function(t){var a=this._polygon;_t(this._polyline,t),a&&_t(a,t)},e.prototype._newPolyline=function(t){var a=this._polyline;return a&&this._lineGroup.remove(a),a=new me({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(a),this._polyline=a,a},e.prototype._newPolygon=function(t,a){var o=this._polygon;return o&&this._lineGroup.remove(o),o=new ye({shape:{points:t,stackedOnPoints:a},segmentIgnoreThreshold:2}),this._lineGroup.add(o),this._polygon=o,o},e.prototype._initSymbolLabelAnimation=function(t,a,o){var r,n,l=a.getBaseAxis(),u=l.inverse;a.type==="cartesian2d"?(r=l.isHorizontal(),n=!1):a.type==="polar"&&(r=l.dim==="angle",n=!0);var s=t.hostModel,h=s.get("animationDuration");J(h)&&(h=h(null));var v=s.get("animationDelay")||0,c=J(v)?v(null):v;t.eachItemGraphicEl(function(f,g){var m=f;if(m){var d=[f.x,f.y],y=void 0,p=void 0,b=void 0;if(o)if(n){var P=o,S=a.pointToCoord(d);r?(y=P.startAngle,p=P.endAngle,b=-S[1]/180*Math.PI):(y=P.r0,p=P.r,b=S[0])}else{var _=o;r?(y=_.x,p=_.x+_.width,b=f.x):(y=_.y+_.height,p=_.y,b=f.y)}var D=p===y?0:(b-y)/(p-y);u&&(D=1-D);var L=J(v)?v(g):h*D+c,w=m.getSymbolPath(),A=w.getTextContent();m.attr({scaleX:0,scaleY:0}),m.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:L}),A&&A.animateFrom({style:{opacity:0}},{duration:300,delay:L}),w.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,a,o){var r=t.getModel("endLabel");if(Ft(t)){var n=t.getData(),l=this._polyline,u=n.getLayout("points");if(!u){l.removeTextContent(),this._endLabel=null;return}var s=this._endLabel;s||(s=this._endLabel=new ee({z2:200}),s.ignoreClip=!0,l.setTextContent(this._endLabel),l.disableLabelAnimation=!0);var h=Ie(u);h>=0&&(Et(l,st(t,"endLabel"),{inheritColor:o,labelFetcher:t,labelDataIndex:h,defaultText:function(v,c,f){return f!=null?ue(n,f):Ot(n,v)},enableTextSetter:!0},ke(r,a)),l.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,a,o,r,n,l,u){var s=this._endLabel,h=this._polyline;if(s){t<1&&r.originalX==null&&(r.originalX=s.x,r.originalY=s.y);var v=o.getLayout("points"),c=o.hostModel,f=c.get("connectNulls"),g=l.get("precision"),m=l.get("distance")||0,d=u.getBaseAxis(),y=d.isHorizontal(),p=d.inverse,b=a.shape,P=p?y?b.x:b.y+b.height:y?b.x+b.width:b.y,S=(y?m:0)*(p?-1:1),_=(y?0:-m)*(p?-1:1),D=y?"x":"y",L=we(v,P,D),w=L.range,A=w[1]-w[0],k=void 0;if(A>=1){if(A>1&&!f){var I=Tt(v,w[0]);s.attr({x:I[0]+S,y:I[1]+_}),n&&(k=c.getRawValue(w[0]))}else{var I=h.getPointOn(P,D);I&&s.attr({x:I[0]+S,y:I[1]+_});var T=c.getRawValue(w[0]),M=c.getRawValue(w[1]);n&&(k=ae(o,g,T,M,L.t))}r.lastFrameIndex=w[0]}else{var C=t===1||r.lastFrameIndex>0?w[0]:0,I=Tt(v,C);n&&(k=c.getRawValue(C)),s.attr({x:I[0]+S,y:I[1]+_})}if(n){var O=re(s);typeof O.setLabelText=="function"&&O.setLabelText(k)}}},e.prototype._doUpdateAnimation=function(t,a,o,r,n,l,u){var s=this._polyline,h=this._polygon,v=t.hostModel,c=ge(this._data,t,this._stackedOnPoints,a,this._coordSys,o,this._valueOrigin),f=c.current,g=c.stackedOnCurrent,m=c.next,d=c.stackedOnNext;if(n&&(g=W(c.stackedOnCurrent,c.current,o,n,u),f=W(c.current,null,o,n,u),d=W(c.stackedOnNext,c.next,o,n,u),m=W(c.next,null,o,n,u)),kt(f,m)>3e3||h&&kt(g,d)>3e3){s.stopAnimation(),s.setShape({points:m}),h&&(h.stopAnimation(),h.setShape({points:m,stackedOnPoints:d}));return}s.shape.__points=c.current,s.shape.points=f;var y={shape:{points:m}};c.current!==f&&(y.shape.__points=c.next),s.stopAnimation(),Q(s,y,v),h&&(h.setShape({points:f,stackedOnPoints:g}),h.stopAnimation(),Q(h,{shape:{stackedOnPoints:d}},v),s.shape.points!==h.shape.points&&(h.shape.points=s.shape.points));for(var p=[],b=c.status,P=0;P<b.length;P++){var S=b[P].cmd;if(S==="="){var _=t.getItemGraphicEl(b[P].idx1);_&&p.push({el:_,ptIdx:P})}}s.animators&&s.animators.length&&s.animators[0].during(function(){h&&h.dirtyShape();for(var D=s.shape.__points,L=0;L<p.length;L++){var w=p[L].el,A=p[L].ptIdx*2;w.x=D[A],w.y=D[A+1],w.markRedraw()}})},e.prototype.remove=function(t){var a=this.group,o=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),o&&o.eachItemGraphicEl(function(r,n){r.__temp&&(a.remove(r),o.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(at);function Te(i,e){return{seriesType:i,plan:oe(),reset:function(t){var a=t.getData(),o=t.coordinateSystem,r=t.pipelineContext,n=e||r.large;if(o){var l=ut(o.dimensions,function(f){return a.mapDimension(f)}).slice(0,2),u=l.length,s=a.getCalculationInfo("stackResultDimension");Y(a,l[0])&&(l[0]=s),Y(a,l[1])&&(l[1]=s);var h=a.getStore(),v=a.getDimensionIndex(l[0]),c=a.getDimensionIndex(l[1]);return u&&{progress:function(f,g){for(var m=f.end-f.start,d=n&&j(m*u),y=[],p=[],b=f.start,P=0;b<f.end;b++){var S=void 0;if(u===1){var _=h.get(v,b);S=o.dataToPoint(_,null,p)}else y[0]=h.get(v,b),y[1]=h.get(c,b),S=o.dataToPoint(y,null,p);n?(d[P++]=S[0],d[P++]=S[1]):g.setItemLayout(b,S.slice())}n&&g.setLayout("points",d)}}}}}}var Ee={average:function(i){for(var e=0,t=0,a=0;a<i.length;a++)isNaN(i[a])||(e+=i[a],t++);return t===0?NaN:e/t},sum:function(i){for(var e=0,t=0;t<i.length;t++)e+=i[t]||0;return e},max:function(i){for(var e=-1/0,t=0;t<i.length;t++)i[t]>e&&(e=i[t]);return isFinite(e)?e:NaN},min:function(i){for(var e=1/0,t=0;t<i.length;t++)i[t]<e&&(e=i[t]);return isFinite(e)?e:NaN},nearest:function(i){return i[0]}},Ne=function(i){return Math.round(i.length/2)};function Ce(i){return{seriesType:i,reset:function(e,t,a){var o=e.getData(),r=e.get("sampling"),n=e.coordinateSystem,l=o.count();if(l>10&&n.type==="cartesian2d"&&r){var u=n.getBaseAxis(),s=n.getOtherAxis(u),h=u.getExtent(),v=a.getDevicePixelRatio(),c=Math.abs(h[1]-h[0])*(v||1),f=Math.round(l/c);if(isFinite(f)&&f>1){r==="lttb"?e.setData(o.lttbDownSample(o.mapDimension(s.dim),1/f)):r==="minmax"&&e.setData(o.minmaxDownSample(o.mapDimension(s.dim),1/f));var g=void 0;le(r)?g=Ee[r]:J(r)&&(g=r),g&&e.setData(o.downSample(o.mapDimension(s.dim),1/f,g,Ne))}}}}}function ze(i){i.registerChartView(Le),i.registerSeriesModel(se),i.registerLayout(Te("line",!0)),i.registerVisual({seriesType:"line",reset:function(e){var t=e.getData(),a=e.getModel("lineStyle").getLineStyle();a&&!a.stroke&&(a.stroke=t.getVisual("style").fill),t.setVisual("legendLineStyle",a)}}),i.registerProcessor(i.PRIORITY.PROCESSOR.STATISTIC,Ce("line"))}var Oe=function(){function i(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return i}(),Re=function(i){Z(e,i);function e(t){var a=i.call(this,t)||this;return a.type="sausage",a}return e.prototype.getDefaultShape=function(){return new Oe},e.prototype.buildPath=function(t,a){var o=a.cx,r=a.cy,n=Math.max(a.r0||0,0),l=Math.max(a.r,0),u=(l-n)*.5,s=n+u,h=a.startAngle,v=a.endAngle,c=a.clockwise,f=Math.PI*2,g=c?v-h<f:h-v<f;g||(h=v-(c?f:-f));var m=Math.cos(h),d=Math.sin(h),y=Math.cos(v),p=Math.sin(v);g?(t.moveTo(m*n+o,d*n+r),t.arc(m*s+o,d*s+r,u,-Math.PI+h,h,!c)):t.moveTo(m*l+o,d*l+r),t.arc(o,r,l,h,v,!c),t.arc(y*s+o,p*s+r,u,v-Math.PI*2,v-Math.PI,!c),n!==0&&t.arc(o,r,n,v,h,c)},e}(vt);export{Re as S,he as a,be as b,Ge as c,ue as d,Ce as e,Ot as g,ze as i,Te as p};
