import{u as o,j as e,P as x,S as n,T as A,l as z,x as F,d as y,aa as se,ab as oe,a6 as re,v as J,aC as ae,a as H,o as ie,B as d,aD as ne,h as b,m as M,ar as R,G as w,r as m,J as B,Q as f,O as E,H as U,C as L,R as K,w as V,aE as le,aF as ce,aG as de,aB as W,L as Ae,y as me,aH as pe,c as xe,I as he}from"./index-CP4gzJXp.js";import{D as Z}from"./DashboardSelectMenu-vvl18_ia.js";import{S as v}from"./SectionHeader-Ca8JdlU3.js";import{R as j,q as k,v as P,y as C,w as Q,x as S,z as Y,A as ue}from"./ReactEchart-C_a4bTea.js";import{i as N}from"./install-B142rSJ8.js";import{i as q}from"./install-CF1L2l3s.js";import{i as ge,a as fe,b as be,w as ve}from"./world-DKceKtjW.js";import{Z as ye,a as we}from"./ZoomOutIcon-DEVtuhJG.js";import{B as je,b as ke}from"./ButtonGroup-DAwIKI8W.js";import{u as Pe}from"./useToggleChartLegends-DlEATUMq.js";import{i as $}from"./sausage-DWu_h7Kw.js";import{D}from"./DashboardMenu-DNfhkcQ5.js";import{i as Ce}from"./install-ObVOtkOI.js";import{T as Qe,a as Se,b as De,c as He,d as Be,e as Ge,t as ze}from"./TimelineSeparator-2ha_Ns4x.js";import{G as Re,D as Le}from"./DataGrid-C1It07i0.js";import{u as Ye}from"./useGridApiRef-Bw2DeI6n.js";import"./StyledFormControl-S1BH4GL9.js";import"./sectorHelper-BreWr0VT.js";import"./createSeriesDataSimply-DIBZSMqc.js";import"./getValidReactChildren-BILH45Ct.js";import"./LegendVisualProvider-CgY2uWFN.js";import"./index-YydR91fc.js";import"./Skeleton-WoLt_I4y.js";import"./LinearProgress-CUbfr40f.js";const Te=[{icon:"material-symbols-light:ads-click-rounded",value:"2,110",subtitle:"Visitors"},{icon:"material-symbols-light:request-quote-outline-rounded",value:"$8.2M",subtitle:"Earnings"},{icon:"material-symbols-light:shopping-cart-checkout-rounded",value:"1,124",subtitle:"Orders"}],Fe=[{title:"Collab with Tintin",time:"1:30pm - 2:30pm",attendants:[o[9],o[0]]},{title:"Meeting about shipping",time:"2:40pm - 4:30pm",attendants:[o[3],o[4],o[6],o[10]]},{title:"Greetings for marketing",time:"9:45am - 11:30am",attendants:[o[5],o[7],o[12]]},{title:"Sales pipeline review",time:"5:40pm - 6:30pm",attendants:[o[1],o[2],o[7],o[12],o[13]]}],Ue=({stats:s,meetingSchedules:i})=>e.jsxs(x,{background:1,component:n,direction:"column",divider:e.jsx(ie,{flexItem:!0}),sx:{gap:3,p:{xs:3,md:5},height:1,overflow:"hidden",display:"flex",flexDirection:"column"},children:[e.jsxs(n,{direction:"column",spacing:1,children:[e.jsx(A,{variant:"subtitle1",sx:{color:"text.secondary",fontWeight:500},children:z(new Date).format("dddd, MMM DD, YYYY")}),e.jsxs(A,{variant:"h6",display:"flex",columnGap:1,flexWrap:"wrap",children:["Good morning,",e.jsx("span",{children:"Captain!"})]})]}),e.jsxs("div",{children:[e.jsx(A,{variant:"subtitle2",color:"text.secondary",fontWeight:400,mb:2,children:"Updates from yesterday."}),e.jsx(n,{direction:{xs:"column",sm:"row",md:"column"},sx:{gap:2,justifyContent:"space-between"},children:s.map(({icon:t,subtitle:a,value:l})=>e.jsxs(n,{direction:{xs:"row",sm:"column",md:"row"},sx:{gap:1,flexWrap:"wrap",alignItems:{xs:"center",sm:"start",md:"center"},flex:1,px:{sm:3,md:0},borderLeft:{sm:1,md:"none"},borderColor:{sm:"divider"}},children:[e.jsx(F,{sx:{color:"primary.main",bgcolor:"primary.lighter"},children:e.jsx(y,{icon:t,sx:{fontSize:24}})}),e.jsxs(n,{direction:{xs:"row",sm:"column",md:"row"},sx:{gap:.5,flexWrap:"wrap",alignItems:"baseline"},children:[e.jsx(A,{variant:"h4",sx:{fontWeight:700},children:l}),e.jsx(A,{variant:"body2",sx:{fontWeight:700,color:"text.secondary"},children:a})]})]},a))})]}),e.jsxs(n,{direction:"column",gap:2,sx:{flex:1},children:[e.jsx(A,{variant:"subtitle2",sx:{color:"text.secondary",fontWeight:400},children:"You have 3 meetings today."}),e.jsx(se,{sx:{maxHeight:{xs:"auto",md:354},height:"min-content"},children:e.jsx(oe,{disablePadding:!0,component:n,gap:1,direction:{xs:"column",sm:"row",md:"column"},children:i.map(({title:t,time:a,attendants:l})=>e.jsxs(re,{sx:{flexDirection:"column",alignItems:"stretch",minWidth:{xs:0,sm:254,md:0},p:2,bgcolor:"background.elevation2",borderRadius:2,gap:1,flex:1,"&:hover":{backgroundColor:"background.elevation3"}},children:[e.jsx(A,{variant:"body1",sx:{fontWeight:700,color:"text.primary",lineClamp:1},children:t}),e.jsxs(n,{direction:{xs:"row",sm:"column",md:"row"},sx:{flex:1,columnGap:.5,rowGap:1,flexWrap:"wrap",alignItems:{xs:"flex-end",sm:"start",md:"flex-end"},justifyContent:"space-between"},children:[e.jsx(A,{variant:"caption",color:"text.secondary",fontWeight:600,children:a}),e.jsx(J,{max:4,sx:{mr:1,[`& .${ae.avatar}`]:{mr:-1.5,width:24,height:24,fontSize:"0.6rem","&:first-of-type":{backgroundColor:"primary.main"}}},children:l.map(r=>e.jsx(F,{alt:r.name,src:r.avatar},r.name))})]})]},t))})}),e.jsx(H,{variant:"text",color:"primary",size:"small",endIcon:e.jsx(y,{icon:"material-symbols:open-in-new-rounded",sx:{height:18,width:18}}),sx:{alignSelf:"flex-end"},children:"Open Schedule"})]})]}),Ze="/assets/9-dark-DB2NqLAk.webp",Ee="/assets/9-light-DbT2-ra7.webp",We=({title:s,subtitle:i})=>e.jsx(x,{sx:{p:{xs:3,md:5},height:1},children:e.jsx(d,{sx:{width:1,height:1,minHeight:{xs:300,md:260},borderRadius:3,overflow:"hidden",background:"linear-gradient(90deg, rgba(153, 220, 196, 0.50) 0%, rgba(153, 220, 196, 0.00) 33.33%, rgba(153, 220, 196, 0.50) 66.67%, rgba(153, 220, 196, 0.00) 100%);",backgroundSize:"300% 100%",animation:"linearLeftToRight 9s linear infinite"},children:e.jsx(n,{direction:"column",sx:t=>({justifyContent:"flex-start",rowGap:2,backgroundImage:`url(${Ee})`,backgroundSize:"calc(max(100%, 904px)) 100%",backgroundRepeat:"no-repeat",backgroundPosition:({direction:a})=>a==="rtl"?"right 14px":"left 14px",padding:4,height:1,...t.applyStyles("dark",{backgroundImage:`url(${Ze})`})}),children:e.jsxs("div",{children:[e.jsx(A,{sx:{color:"success.dark",mb:.5,typography:{xs:"h6",lg:"h5"}},children:s}),e.jsx(A,{sx:{color:"success.dark",typography:{xs:"h3",lg:"h2",xl:"h1"}},children:i})]})})})}),Ie="data:image/webp;base64,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",Oe="data:image/webp;base64,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",Xe="data:image/webp;base64,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",Je="data:image/webp;base64,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",Me="data:image/webp;base64,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",Ke="data:image/webp;base64,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",Ve="data:image/webp;base64,UklGRngGAABXRUJQVlA4WAoAAAAQAAAAXwAAYAAAQUxQSK0BAAABkINt27E3T2zbdjbbtp1Mtlnb+lF76qrJtm3bdvs+W/pq7dGImAD473+ZmwQll9XW11WXpkZ6asjGPH/h/ncEx/th7/RoeRiV7vmONMmFDDlYjT5F6mSDjXg6nS+R6TkX0cJuIOu7PkLpLP6F7J/4c6ChZ2hkYmxiYqDxN8FnkcunYQw0vFtWbTty/saDpy9evnz56smVPdNCNMbR+g45fTloSKvnLkGKlxckGQKARtRu5PjZDDsqDkj9y7F1Kw8T5Pu5OQ04QE3Ih9pU6mQyBFSNHsnjiwcdmCeP7UDZ4bs0kmjBTlnc0KRWLYs5QF37qRyIAz1YKofdwDBUDg0sNC7K4KMJC5glg83ANPCXeCSFjdYR8a5qs4F+8SYBY9e3on3xYQVJl4lI5EYVcOhQ3Lkbd6pO392yZvVKlUKhUKpXrFQoVq5ULl66eP6cWbNmzV6kUiiWLVUuWzx7ytRJoyOjv6aO9L/4XJNqD7y2YhnUlgG/Gls1AS7fBI4rMAv4v3ycp1hMEeDMDp7cMU6AQ2t4ssRQAfZN50nve6AAe1t50phpJUCqK0//VAUAVlA4IKQEAAAwGACdASpgAGEAAAAAJaQDWaaE8P/G8WJOdFJOY223mA87z0AbxR6AHlj+yF+3eCS/gB35/zb8RP1u7Y1Gf90/IDRM/1P5Vcxv/fPx48UnjMP8BqJv7D+VX+A+S3/A84X0T/ffcD/j38s/uH9m/Z7+5/+rlEf1ATT5k8abQ9be32K8n+7VJCBx/4RJPeLJ7PZpg0u6ywmIPPr4PpL+MivHrwGTy6e7X6RAll+e1BvE+P6krhX//nzrgTZeqL5dnZuMkEerktivtosgAP7/327UAAEn/89EZm4WSq3tviS0IexKweDQ0dyrgqYbJJb/0unzvXd/IbWfRmJ/5tU0ZXMusZNl51d8pRlKWV44mtgVCevf6N5ZHTcNbgRXLeWsfMW+bX9/KUcyn+ujzOmvFS7DcamT7ofzh+JYHMS0UlZxb7git3cjDgFClpYX/rsCkdYsJ5P3b1p+nNFgYaoGn9NjHnxIGiO+Uc/7ZyZH/hNo8fcpzy2IUL/nSSzmf+qq5+/+uldX+7/iwMr0rNpvbtn2K0haZLg5oS3zybRk8FO4DGV6T3qWt87FOF/TVwrQx6oIqSdHoh052atLEnkS/Ayhtg3rYES/NRh5H64pBcgmnYLWGXObbHYFDN5/GD4sR0rSX5T3+KxXdXAeRb/jxsGB/8NHvPrbdEv4Q6D/zpx/8X7t9/j9o9PsMxCjnvg3pcSONnooFgbkLxYsAezVGin60//+ftW2KiJV2Swdov7A7sxxczaQWkWhvGU/OLbBvoqz3YjFZeowopWu6MYhpTPhMj2wK56mLu/cVUYXZp8Z9LepzyZB22YrZiwqK41YkM/n9v//3MoEaB30Omm2Qw+Rxb/f8uD2+sSodv5nlEsZ/k6FlwNlM6gcPVcMeOtHGGGxVNyQyhVdCeD6nzoccddCmxCl9Fn/tE/i7EVP3SUe6ocdm87KR9AhTNK37mjB6KYTqSuRSsri46motzo/rSfWCHOlC5Ge41vysM8OY1la3JeEh6fgThiZ0YyDk6cE+4ldj4u5i+Js9vKIW9RzMBueImLl6DWbnnf6/8Cv9U25D4jAdf/AJsp8Piswz9+Tyv+H78rZR0wvJmDH7D4Eb35s8Jx0ebD1q5vHAAM1PZVLJ+Z6NrGMOGOAgkHif+/+wIJ7MyS0KPn+WVt74nQAROFW8fe4tU+O6Je54cvu2FSgGsqmdYIo+G76SWk1r/x2lsNDzxu6T+/dpcWpnV4sO8vF2dbzYGAWXFirXGFPlqVai5BHzAQFB7IdFQkYzZT23YfdcLMs/dU1+U4FvqR9oi5D7av/ZY5lgWPldNSQXRWDKVAHp/QUMoWB84fKPA/QopiMuoEU20XY07qB/CgUFmIP082hPKERZlI+GdgmBKvIveNxqQrt0qIKrFwfpsgkws1FMZ+q3dsNcqCousizjr2qpC7vnPm73V4209cr96/xiieP+ofkdZliZca7Zo/KEPM7UMn+e4nXpLK5LwRKSuc63ohPx5WZlXXVubgYSLoSfEFnCh18+/8S+H9l93z+tiQeL8sIgv05f/n22wdh7GXJdzbuQGETt1/55n7XK9hAAAAAAAA=",Ne="data:image/webp;base64,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",qe="data:image/webp;base64,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",$e="data:image/webp;base64,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",_e="data:image/webp;base64,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",et=[{id:1,product:{name:"Shanty Cotton Seat",image:Ie},vendors:[o[2],o[7],o[15]],margin:981,sold:29536,stock:"In Stock"},{id:2,product:{name:"Practical Soft Couch",image:Oe},vendors:[o[6],o[11],o[7],o[13]],margin:199,sold:27700,stock:"In Stock"},{id:3,product:{name:"Rustic Rubber Chair",image:ne},vendors:[o[4],o[3],o[5],o[14],o[1],o[2]],margin:609,sold:21778,stock:"Low Stock"},{id:4,product:{name:"Ergonomic Frozen Bacon",image:Xe},vendors:[o[6],o[5],o[11],o[15]],margin:923,sold:20272,stock:"In Stock"},{id:5,product:{name:"Unbranded Metal Sofa",image:Je},vendors:[o[12],o[1]],margin:119,sold:17374,stock:"In Stock"},{id:6,product:{name:"Intelligent Soft Sofa",image:Me},vendors:[o[1],o[2],o[3]],margin:595,sold:14374,stock:"Low Stock"},{id:7,product:{name:"Handmade Cotton Chair",image:Ke},vendors:[o[2],o[3],o[13],o[14],o[15],o[12]],margin:472,sold:12084,stock:"Stockout"},{id:8,product:{name:"Fantastic Rubber Chair",image:Ve},vendors:[o[2],o[7],o[15]],margin:98,sold:48604,stock:"In Stock"},{id:9,product:{name:"Generic Steel Divan",image:Ne},vendors:[o[10],o[13],o[5],o[11],o[12],o[13]],margin:931,sold:2329,stock:"In Stock"},{id:10,product:{name:"Handmade Beanbag",image:qe},vendors:[o[10],o[2]],margin:5300,sold:70946,stock:"Low Stock"},{id:11,product:{name:"Practical Metal Sofa",image:$e},vendors:[o[4],o[3],o[13],o[14],o[15],o[11]],margin:282,sold:57682,stock:"In Stock"},{id:12,product:{name:"Advanced Soft Couch",image:_e},vendors:[o[15],o[10],o[7],o[9]],margin:427,sold:32587,stock:"Low Stock"}],tt={currentYear:[2e5,12e4,16e4,14e4,26e4,16e4,175e3,18e4,11e4,13e4,8e4,16e4,16e4,15e4,9e4],lastYear:[1e5,15e4,95e3,95e3,98e3,14e4,13e4,15e4,15e4,16e4,255e3,14e4,14e4,16e4,16e4]},st=[{label:"Bed",value:20},{label:"Table",value:30},{label:"Couch",value:40},{label:"Unoccupied",value:10}],I=[{name:"Japan",value:44e3},{name:"Greenland",value:41e3},{name:"India",value:38e3},{name:"Egypt",value:27e3},{name:"Mexico",value:19e3},{name:"Angola",value:13e3},{name:"Colombia",value:11e3},{name:"Finland",value:7e3}],ot={currentYear:[600,400,530,210,300,400,600],lastYear:[500,480,200,250,250,280,280]},rt={currentYear:[0,400,250,300,80,600],lastYear:[100,250,150,200,400,250]};P([N,C,Q,q,S,Y]);const at=({data:s,sx:i})=>{const{vars:t,typography:a}=b(),{up:l,currentBreakpoint:r}=M(),c=l("md"),{numberFormat:p}=R(),{getThemeColor:h}=w(),G=m.useMemo(()=>({tooltip:{trigger:"axis",formatter:u=>B(u)},xAxis:{type:"category",data:s.map(u=>u.name),axisLabel:{color:h(t.palette.text.secondary),fontSize:12,fontFamily:a.fontFamily,interval:r==="md"?"auto":0,rotate:c?0:70},min:"dataMin",max:"dataMax",axisLine:{lineStyle:{color:h(t.palette.chGrey[300])}},axisTick:{show:!1}},yAxis:{type:"value",axisTick:{show:!1},splitLine:{lineStyle:{color:h(t.palette.chGrey[200])}},axisLabel:{show:!1},axisLine:{show:!1}},series:[{type:"bar",data:s.map(u=>u.value),itemStyle:{borderRadius:[2,2,0,0],color:h(t.palette.chBlue[300])},barWidth:r==="md"?8:24,label:{show:!0,position:"outside",formatter:u=>p(Number(u.value),{notation:"compact"}),color:h(t.palette.chBlue[500]),fontWeight:700,fontSize:12}}],grid:{containLabel:!0,right:0,left:0,bottom:2,top:15}}),[r,t.palette,h,s,c,p]);return e.jsx(j,{echarts:k,option:G,sx:i})};P([N,C,Q,ge,S,fe,Y,be]);ue("world",{geoJSON:ve});const it=({data:s,sx:i})=>{const t=m.useRef(null),{vars:a}=b(),{currentBreakpoint:l}=M(),{getThemeColor:r}=w(),[c,p]=m.useState(1),[h]=m.useState(5),[G]=m.useState(1),u=m.useMemo(()=>({tooltip:{trigger:"item",formatter:g=>`${g.name} : ${isNaN(Number(g.value))?0:g.value}`},toolbox:{show:!1,feature:{restore:{}}},visualMap:{show:!1,inRange:{color:[r(a.palette.chBlue[500])]}},series:[{type:"map",map:"world",data:s,selectedMode:!1,zoom:c,center:l==="xs"?["20%","5%"]:l==="sm"?["10%","5%"]:[0,0],roam:"move",scaleLimit:{min:1},left:0,right:0,label:{show:!1},itemStyle:{borderColor:"transparent",areaColor:r(a.palette.chGrey[200])},emphasis:{disabled:!0}}]}),[l,c,a.palette,r,s]),ee=()=>{var g;c<h&&p(c+1),(g=t.current)==null||g.getEchartsInstance().setOption({series:{zoom:c+1}})},te=()=>{var g;c>G&&p(c-1),(g=t.current)==null||g.getEchartsInstance().setOption({series:{zoom:c-1}})};return m.useEffect(()=>{switch(l){case"xs":p(3);break;case"sm":p(2);break;default:p(1);break}},[l]),e.jsxs(d,{sx:{position:"relative",height:1},children:[e.jsx(j,{className:"echart-map",ref:t,echarts:k,option:u,sx:i}),e.jsxs(je,{orientation:"vertical","aria-label":"vertical outlined button group",sx:{position:"absolute",bottom:0,left:0,[`& .${ke.grouped}`]:{minWidth:36}},children:[e.jsx(H,{variant:"soft",color:"neutral",shape:"square",sx:{fontSize:20,border:1,borderColor:"background.elevation2"},onClick:ee,disabled:h===c,children:e.jsx(ye,{})},"one"),e.jsx(H,{variant:"soft",color:"neutral",shape:"square",sx:{fontSize:20,border:1,borderColor:"background.elevation2"},onClick:te,disabled:G===c,children:e.jsx(we,{})},"two")]})]})},nt=()=>e.jsxs(x,{sx:{p:{xs:3,md:5},height:"100%",display:"flex",flexDirection:"column"},background:1,children:[e.jsx(v,{title:"Most clients",subTitle:"Our client number based on their primary location",actionComponent:e.jsx(Z,{defaultValue:1})}),e.jsxs(f,{container:!0,spacing:3,children:[e.jsx(f,{size:{xs:12,md:6,xl:12},children:e.jsx(it,{data:I,sx:{width:"100%",borderRadius:2,overflow:"hidden",height:{md:"260px !important",xl:"450px !important"}}})}),e.jsx(f,{size:{xs:12,md:6,xl:12},children:e.jsx(at,{data:I,sx:{height:{md:"260px !important"},width:"100%"}})})]})]});P([C,Q,$,S,Y]);const lt=({sx:s,data:i,ref:t})=>{const{vars:a,typography:l}=b(),{getThemeColor:r}=w(),c=m.useMemo(()=>({tooltip:{trigger:"axis",axisPointer:{type:"line",lineStyle:{color:r(a.palette.chGrey[300]),type:"solid"},z:1},formatter:p=>B(p,!0)},legend:{data:["lastYear","thisYear"],show:!1},xAxis:{type:"category",data:U(15).map(p=>z(p).format("MMM DD")),boundaryGap:!1,show:!0,axisLine:{show:!1},splitLine:{show:!0,interval:0,lineStyle:{color:r(a.palette.chGrey[200])}},axisTick:{show:!1},axisLabel:{show:!0,fontFamily:l.fontFamily,color:r(a.palette.text.secondary)}},yAxis:{show:!1,type:"value",boundaryGap:!1},series:[{name:"This year",type:"line",data:i.currentYear,showSymbol:!1,symbolSize:8,symbol:"circle",zlevel:1,lineStyle:{width:3,color:r(a.palette.chBlue[500])},emphasis:{lineStyle:{color:r(a.palette.chBlue[500])},itemStyle:{borderWidth:16,borderColor:E(r(a.palette.chBlue["500Channel"]),.2),color:r(a.palette.chBlue[900])}},itemStyle:{color:r(a.palette.chBlue[500])}},{type:"line",name:"Last year",data:i.lastYear,showSymbol:!0,symbolSize:8,symbol:"circle",lineStyle:{width:3,color:r(a.palette.chGrey[200])},emphasis:{lineStyle:{color:r(a.palette.chGrey[300])},itemStyle:{borderWidth:16,borderColor:E(r(a.palette.text.primaryChannel),.2),color:r(a.palette.text.primary)}},itemStyle:{color:r(a.palette.chGrey[500])}}],grid:{left:20,right:20,top:0,bottom:25}}),[i,a.palette,r,l.fontFamily]);return e.jsx(j,{ref:t,echarts:k,option:c,sx:s})},ct=()=>{const s=m.useRef(null),{legendState:i,handleLegendToggle:t}=Pe(s);return e.jsx(x,{sx:{p:{xs:3,md:5},height:1},children:e.jsxs(n,{direction:"column",sx:{rowGap:4,height:"100%"},children:[e.jsxs(f,{container:!0,spacing:2,sx:{alignItems:{lg:"flex-end"},justifyContent:"space-between"},children:[e.jsxs(f,{size:{xs:"grow",lg:"auto"},children:[e.jsx(A,{variant:"h6",sx:{mb:1},children:"Revenue Generated"}),e.jsx(A,{variant:"body2",sx:{color:"text.secondary",textWrap:"pretty"},children:"Amount of revenue in this month comparing to last year"})]}),e.jsx(f,{sx:{ml:{sm:"auto",md:0},order:{lg:1}},children:e.jsx(Z,{defaultValue:1})}),e.jsx(f,{size:{xs:12,lg:"auto"},children:e.jsxs(n,{sx:{gap:4},children:[e.jsx(H,{variant:"text",disableRipple:!0,size:"small",sx:{p:0,color:"text.secondary",fontWeight:400,opacity:i["Last year"]?.5:1,"&:hover":{backgroundColor:"unset !important"}},startIcon:e.jsx(y,{icon:"material-symbols:square-rounded",sx:{height:16,width:16,color:"chGrey.300"}}),onClick:()=>t("Last year"),children:"Last year"}),e.jsxs("div",{children:[e.jsx(H,{variant:"text",disableRipple:!0,size:"small",sx:{mr:1,p:0,color:"text.secondary",fontWeight:400,opacity:i["This year"]?.5:1,"&:hover":{backgroundColor:"unset !important"}},startIcon:e.jsx(y,{icon:"material-symbols:square-rounded",sx:{height:16,width:16,color:"primary.main"}}),onClick:()=>t("This year"),children:"This year"}),e.jsx(L,{label:"+6.19%",color:"success"})]})]})})]}),e.jsx(d,{sx:{flex:1,"& .echarts-for-react":{height:"100% !important"}},children:e.jsx(lt,{data:tt,sx:{minHeight:"200px",width:"100%"},ref:s})})]})})},dt="data:image/webp;base64,UklGRh4BAABXRUJQVlA4WAoAAAAQAAAAFwAADwAAQUxQSIsAAAABcEjbdh09hIdQhCA8hCDUYIpQg9YgNQjCHYNXg4sQhP7/AhExAfJtu0FDneDtFf05SeYoFys4hj6qXFWUZKLBwkZF1KpN/DOnDIBIHeCYYJvGHXCioExO5DrKbu8j6WQpE4GmMttriWlKjnGapqkUx9DVGwFzmkB4dm80mNW6M+QgYtPUy70xyLcBAFZQOCBsAAAAsAMAnQEqGAAQAD5tLJJFpCKhmAQAQAbEtgAPaM9gr9pfR0BNHQAA/ueKf/8+q6hKrHSOP/05tt28Nq86WH/vkt/OF0+TRDyHG1/k7HPW4Ig9fMf9m01v+89z80fVAGXfOceMjSqOpK4zOgAA",At="data:image/webp;base64,UklGRt4AAABXRUJQVlA4WAoAAAAQAAAAFwAADwAAQUxQSFQAAAABYE5tb50PIQhBKEIQhlCEGrQIMSjCEIIwhCAUYff79j8iJgA/SHxVwWGS2NFG1gOSzQTbYdzi1LemjH0bXThWH0cvhKPjrglODqumAadZrSbCDwNWUDggZAAAAFADAJ0BKhgAEAA+bSySRaQioZgEAEAGxLYATocQBvB4JR1AAP7wnZ7pA1CtpzK+3LA0bvv7/yd/i0ROjv7kPk1F/k1iS8zQuSP/r1L//9fLijcpE+HsgeX7vL7fT/iTL2ngAAA=",mt="data:image/webp;base64,UklGRggBAABXRUJQVlA4WAoAAAAQAAAAFwAADwAAQUxQSIMAAAABcE7bdh4dhCI8hCA8hCDEYIoQg4nBQ+gYBKEIDyEI91fnGyAiJoB/Wp2jg0/jlk8FLofUZprPWBAJRYvQeCbMtMPIaafqEzbvp9I45qPtsqspSVkoI3uMxbWlu+pRdtdZ53wmGGmHBUpWf6I5GW3VYPVN9Ylgk0OtFCuyJ4DGdeOHAQBWUDggXgAAALADAJ0BKhgAEAA+bS6SRiQioaEwCACADYlsAJ0TIB+oFm3Aik8AAP7u08+Iz3WFrQJ7/zRbiXX/cJx/y4ar/6S/z7ut15L1qvxX/+lDvDD7u/lHvPNJe//VDiQQAAA=",pt="data:image/webp;base64,UklGRhgBAABXRUJQVlA4WAoAAAAQAAAAFwAADwAAQUxQSHQAAAABYBDbVhQiEIEIRCCKEWigDbABEYhglB/hR7huLC4BIkJh27aNstPdM8wbYnMcY1bUjlAA0gjuAt/FJ7NcbL39E0xWAUJ7lQJq44W0l2wAS7VTY3R24UKrnaoQhGJWgHIPufVdodrBtZZEEZFy3ynXO/1JDFZQOCB+AAAAsAMAnQEqGAAQAD5tLJFFpCKhmAQAQAbEtgBOjsAz0DJAN4yBCmwA/us28u/zeMmdv+5cgApRix/r/VTP4nWfTDX8nFfxwykoB/q7oj739AhNBjOtOlJ6Ew0maGfEb9rexn+LSu6Sj/6wG0mzO5RPA5J9bMzzKP/iAmb88AAA",O=[{id:"alligator",icon:dt,brand:"Alligator",revenue:29.7,growth:6.01},{id:"check_mark",icon:At,brand:"CheckMark",revenue:31.9,growth:4.12},{id:"stripes",icon:pt,brand:"Stripes",revenue:23,growth:-3.91},{id:"head_mead",icon:mt,brand:"Head & Mead",revenue:14.4,growth:.01}];P([C,Ce,S,Q,Y]);const xt=({data:s,bgColorMap:i,sx:t})=>{const{getThemeColor:a}=w(),l=m.useMemo(()=>({color:Object.values(i).map(r=>a(r)),tooltip:{trigger:"item",formatter:r=>B(r)},legend:{show:!1},series:[{type:"pie",padAngle:2,radius:["100%","94%"],avoidLabelOverlap:!1,emphasis:{scale:!1,itemStyle:{color:"inherit"}},itemStyle:{borderColor:"transparent"},label:{show:!1},data:s.map(r=>({name:r.brand,value:r.revenue}))}],grid:{containLabel:!0}}),[s,i,a]);return e.jsx(j,{echarts:k,option:l,sx:t})},ht=s=>s>1?{color:"success"}:s<0?{color:"error"}:{color:"warning"},ut=({shares:s,bgColorMap:i})=>e.jsx(n,{direction:"column",sx:{gap:2,flex:1,alignSelf:"stretch"},children:s.map((t,a)=>{var l;return e.jsxs(n,{spacing:{xs:2,sm:3},sx:{alignItems:"stretch",pt:{xs:2,sm:a===0?0:2,md:2},borderTop:{xs:1,sm:a===0?0:1,md:1},borderColor:{xs:"divider",sm:"divider",md:"divider"}},children:[e.jsx(d,{sx:{height:{xs:24,sm:44,lg:24},width:8,borderRadius:2,background:i[t.id]}}),e.jsxs(n,{direction:{xs:"row",sm:"column",lg:"row"},sx:{justifyContent:"space-between",flex:1},children:[e.jsxs(n,{sx:{alignItems:"center",gap:1,flex:1},children:[e.jsx(K,{src:t.icon,alt:t.brand,height:16}),e.jsx(A,{variant:"body2",sx:{flex:1,color:"text.secondary"},children:t.brand})]}),e.jsxs(n,{sx:{justifyContent:"space-between",flex:1},children:[e.jsxs(A,{variant:"body1",sx:{fontWeight:"bold"},children:[t.revenue,"%"]}),e.jsx(L,{label:`${t.growth}%`,color:(l=ht(t.growth))==null?void 0:l.color,variant:"soft"})]})]})]},t.id)})}),gt=()=>{const{vars:s}=b(),{currencyFormat:i}=R(),t=m.useMemo(()=>({alligator:s.palette.chBlue[300],check_mark:s.palette.chGrey[300],stripes:s.palette.chGrey[500],head_mead:s.palette.chBlue[500]}),[s.palette]);return e.jsxs(x,{sx:{p:{xs:3,md:5},height:"100%"},background:1,children:[e.jsx(v,{title:"Market Share",subTitle:"Amount of revenue in one month",actionComponent:e.jsx(D,{})}),e.jsxs(n,{direction:{xs:"column",sm:"row",md:"column"},sx:{gap:4,alignItems:"center"},children:[e.jsx(n,{sx:{justifyContent:"center",flex:1},children:e.jsxs(d,{sx:{width:"fit-content",position:"relative"},children:[e.jsx(xt,{data:O,bgColorMap:t,sx:{height:"230px !important",width:"230px"}}),e.jsxs(d,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%,-50%)"},children:[e.jsx(A,{variant:"h3",sx:{mb:1},children:i(6322.32)}),e.jsx(A,{variant:"subtitle2",align:"center",sx:{fontWeight:"regular",color:"text.secondary"},children:"Total transactions"})]})]})}),e.jsx(ut,{shares:O,bgColorMap:t})]})]})},_=({amount:s,increment:i,chart:t})=>{const{currencyFormat:a}=R();return e.jsxs(n,{sx:{gap:2,alignItems:"end",justifyContent:"space-between"},children:[e.jsxs(d,{children:[e.jsx(A,{sx:{color:"text.secondary",typography:{xs:"h5",lg:"h4",xl:"h3"},mb:1},children:a(s,{minimumFractionDigits:0})}),e.jsx(L,{label:`${i>0?`+${i}`:i}%`,color:i>0?"success":"warning"}),e.jsx(A,{variant:"body2",sx:{whiteSpace:"nowrap",color:"text.secondary",ml:.5,display:"inline"},children:"vs last month"})]}),t]})};P([C,Q,$,S]);const ft=({data:s,sx:i})=>{const{vars:t}=b(),{getThemeColor:a}=w(),l=m.useMemo(()=>({tooltip:{trigger:"axis",formatter:r=>B(r,!0)},xAxis:{type:"category",data:U(7).map(r=>z(r).format("MMM DD")),show:!1,boundaryGap:!1,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},yAxis:{show:!1,type:"value",boundaryGap:!1},series:[{name:"Current year",type:"line",data:s.currentYear,showSymbol:!1,symbol:"circle",zlevel:1,lineStyle:{width:3,color:a(t.palette.chBlue[500])},emphasis:{lineStyle:{color:a(t.palette.chBlue[500])}},itemStyle:{color:a(t.palette.chBlue[500])}},{name:"Last year",type:"line",data:s.lastYear,showSymbol:!1,symbol:"circle",lineStyle:{width:1,color:a(t.palette.chGrey[300])},emphasis:{lineStyle:{color:a(t.palette.chGrey[300])}},itemStyle:{color:a(t.palette.chGrey[300])}}],grid:{left:5,right:"-15%",top:5,bottom:"5%"}}),[t.palette,a,s]);return e.jsx(j,{echarts:k,option:l,sx:i})},bt=()=>e.jsx(x,{sx:{p:{xs:3,md:5},flex:1},children:e.jsxs(n,{direction:"column",sx:{rowGap:2,height:"100%",justifyContent:"space-between"},children:[e.jsx(v,{title:"Monthly Net Profit",subTitle:" Total profit gained",actionComponent:e.jsx(D,{}),sx:{mb:0}}),e.jsx(_,{amount:25049,increment:4.33,chart:e.jsx(ft,{data:rt,sx:{height:"100% !important",width:"50%"}})})]})}),T=[{id:1,title:"An item was sold!",description:"See, track and monitor product purchase details based on user visits, navigation, and engagement on-site.",time:"2s ago",icon:"material-symbols:attach-money-rounded"},{id:2,title:"Product out on the Amazon Market",description:"Organize your inventory, track and monitor the availability of products on your site as well as in the Amazon Marketplace to reach out better.",time:"5m ago",icon:"material-symbols:storefront-outline-rounded"},{id:3,title:"You responded to a support ticket from Jonah Simson",description:"Get updates on resolved and unresolved support tickets all at once for easier customer service and communications.",time:"2 hr ago",icon:"material-symbols:help-outline-rounded"},{id:4,title:"Sale on the summer collection has started",description:"Monitor all your sales products for a better overview on how your seasonal sales campaigns perform all over.",time:"2 hr ago",icon:"material-symbols:sell-outline"},{id:5,title:"A distributer sold an item",description:"Keep track of redistributed products for a concise view of your revenue growth and your suppliers.",time:"1 day ago",icon:"material-symbols:attach-money-rounded"},{id:6,title:"A new Supplier Added",description:"Keep track of all the suppliers and relevant communication at a click’s length for enhanced production and sustainable supply.",time:"1 day ago",icon:"material-symbols:box-add-outline-rounded"},{id:7,title:"A new product was launched",description:"Find all your new released products and services at the same place for simple monitoring and tracking for sustainable growth.",time:"2 days ago",icon:"material-symbols:rocket-launch-outline-rounded"},{id:8,title:"You got a new recommendation",description:"Track and monitor how your customers behave across the site to improve the user engagement on your business website.",time:"3 days ago",icon:"material-symbols:live-help-outline-rounded"}],vt=()=>e.jsx(x,{sx:{p:{xs:3,md:5},height:1},children:e.jsxs(n,{direction:"column",sx:{height:1},children:[e.jsx(v,{title:"Recent activities",subTitle:"Details on shopping composition",actionComponent:e.jsx(Z,{defaultValue:1})}),e.jsx(d,{sx:{flexGrow:1,flexBasis:{md:0,xl:"100%"},height:"100%",overflowY:{md:"scroll"},maxHeight:{xl:540}},children:e.jsx(Qe,{sx:{p:0,m:0,[`& .${ze.root}:before`]:{flex:0,padding:0}},children:T.map((s,i)=>e.jsxs(Se,{sx:{mb:1},children:[e.jsxs(De,{children:[e.jsx(He,{sx:{mt:0,mb:1,boxShadow:"none",border:0,p:1,bgcolor:"primary.lighter"},children:e.jsx(y,{icon:s.icon,sx:{fontSize:16,color:"primary.dark"}})}),i!==T.length-1&&e.jsx(Be,{sx:{bgcolor:"divider",width:"1px"}})]}),e.jsxs(Ge,{sx:{pb:i!==T.length-1?{xs:3,xl:5}:0,pt:0},children:[e.jsx(A,{variant:"body1",sx:{fontWeight:"bold",mb:.5,mt:"2px"},children:s.title}),e.jsxs(n,{spacing:2,sx:{justifyContent:"space-between",flexDirection:{xs:"column",sm:"row"},alignItems:"flex-start"},children:[e.jsx(A,{variant:"body2",sx:{color:"text.secondary",lineClamp:2},children:s.description}),e.jsx(A,{variant:"body2",noWrap:!0,sx:{fontWeight:"medium",color:"text.disabled",flexShrink:0},children:s.time})]})]})]},s.id))})})]})}),X=(s,i)=>{let t=s.primary.main;return i<=10?t=s.chBlue[500]:i<=20&&i>10?t=s.chBlue[200]:i<=30&&i>20?t=s.chGrey[300]:i<=40&&i>30?t=s.chGrey[200]:i<=50&&i>40&&(t=s.chBlue[300]),t},yt=({storages:s})=>{const i=b();return e.jsxs("div",{children:[e.jsx(n,{sx:{alignItems:"center",height:8,mb:2},children:s.map((t,a)=>e.jsx(V,{title:t.label,arrow:!0,children:e.jsx(d,{sx:{width:`${t.value+8}%`,background:i.vars.palette.background.default,p:.5,mx:-.5,"&:hover":{borderRadius:2,zIndex:1,boxShadow:i.vars.shadows[1],[`& .${le.root}`]:{borderRadius:2,height:10}}},children:e.jsx(d,{sx:[{height:8},{width:1,background:l=>X(l.vars.palette,t.value)},a===0&&{borderRadius:"8px 0 0 8px"},a===s.length-1&&{borderRadius:"0 8px 8px 0"}]})})},t.label))}),e.jsx(n,{spacing:3,children:s.map(t=>e.jsxs(n,{spacing:1,sx:{alignItems:"center"},children:[e.jsx(d,{sx:{width:8,height:8,background:a=>X(a.vars.palette,t.value),borderRadius:.5}}),e.jsx(A,{variant:"caption",sx:{fontWeight:500,color:"text.secondary"},children:ce(t.label)})]},t.label))})]})},wt=()=>e.jsxs(x,{sx:{p:{xs:3,md:5}},children:[e.jsx(v,{title:"Storage Usage",subTitle:" Product categories occupying warehouse space",actionComponent:e.jsx(D,{})}),e.jsx(yt,{storages:st})]}),jt=s=>{switch(s){case"In Stock":return{color:"success",icon:"ic:round-check"};case"Low Stock":return{color:"warning",icon:"material-symbols:warning-outline-rounded"};case"Stockout":return{color:"error",icon:"ic:round-do-not-disturb-alt"};default:return{color:"primary",icon:"material-symbols:check-small-rounded"}}},kt=({apiRef:s})=>{const{currencyFormat:i,numberFormat:t}=R(),a=de(),l=m.useMemo(()=>[{...Re,width:64},{field:"product",headerName:"Product",headerClassName:"product-header",width:300,valueGetter:({name:r})=>r,renderCell:r=>e.jsxs(n,{spacing:1.25,sx:{alignItems:"center"},children:[e.jsx(K,{src:r.row.product.image,alt:r.row.product.name,onClick:()=>a(W.productDetails(String(r.row.id))),sx:{cursor:"pointer"},height:48,width:48}),e.jsx(Ae,{href:W.productDetails(String(r.row.id)),sx:{color:"text.secondary"},children:r.row.product.name})]})},{field:"vendor",headerName:"Vendors",minWidth:150,flex:.35,sortable:!1,renderCell:r=>e.jsx(J,{max:5,color:"primary",sx:{display:"inline-flex",[`& .${me.root}`]:{width:28,height:28,fontSize:12.8,fontWeight:"medium",backgroundColor:"primary.main"}},children:r.row.vendors.map(c=>e.jsx(V,{title:c.name,children:e.jsx(F,{alt:c.name,src:c.avatar})},c.name))})},{field:"margin",headerName:"Margin",flex:.2,minWidth:120,align:"right",headerAlign:"right",headerClassName:"margin",cellClassName:"margin",renderCell:r=>i(r.row.margin)},{field:"sold",headerName:"Sold",minWidth:110,flex:.2,renderCell:r=>t(r.row.sold)},{field:"stock",headerName:"Stock",minWidth:120,flex:.25,align:"center",headerAlign:"right",renderCell:r=>{var c;return e.jsx(L,{label:r.row.stock,color:(c=jt(r.row.stock))==null?void 0:c.color,variant:"soft",size:"small",sx:{width:"100%",maxWidth:118}})}},{field:"action",headerName:"",sortable:!1,width:60,align:"right",headerAlign:"right",renderCell:()=>e.jsx(D,{})}],[i,t]);return e.jsx(n,{direction:"column",sx:{width:"100%"},children:e.jsx(Le,{rowHeight:64,rows:et,apiRef:s,columns:l,initialState:{pagination:{paginationModel:{pageSize:6}}},pageSizeOptions:[6],checkboxSelection:!0,slots:{basePagination:r=>e.jsx(pe,{showAllHref:"#!",...r})},sx:{"& .margin":{pr:5}}})})},Pt=()=>{const s=Ye(),i=m.useCallback(t=>{var a;(a=s.current)==null||a.setQuickFilterValues([t.target.value])},[s]);return e.jsxs(x,{sx:{px:{xs:3,md:5},py:{xs:3,md:5},height:"100%"},children:[e.jsx(v,{title:"Top products",subTitle:"Detailed information about the products",sx:{flexWrap:{xs:"wrap",sm:"nowrap"},columnGap:1,rowGap:3,mb:3},actionComponent:e.jsxs(e.Fragment,{children:[e.jsx(xe,{id:"search-box",type:"search",placeholder:"Search",size:"small",sx:()=>({maxWidth:{sm:180,md:260},width:1,ml:"auto",order:{xs:1,sm:0},flexBasis:{xs:"100%"}}),onChange:i,slotProps:{input:{startAdornment:e.jsx(he,{position:"start",children:e.jsx(y,{icon:"material-symbols:search-rounded"})})}}}),e.jsx(D,{size:"small"})]})}),e.jsx(kt,{apiRef:s})]})};P([C,Q,q,S]);const Ct=({data:s,sx:i})=>{const{vars:t}=b(),{getThemeColor:a}=w(),l=m.useMemo(()=>({tooltip:{trigger:"axis",formatter:r=>B(r,!0)},xAxis:{type:"category",data:U(8).map(r=>z(r).format("MMM DD")),show:!1,boundaryGap:!1,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},yAxis:{show:!1,type:"value",boundaryGap:!1},series:[{name:"Current year",type:"bar",data:s.currentYear,barWidth:"4px",barGap:1,label:{show:!1},itemStyle:{borderRadius:[10,10,0,0],color:a(t.palette.chBlue[500])}},{name:"Last year",type:"bar",data:s.lastYear,barWidth:"4px",barGap:"100%",label:{show:!1},itemStyle:{borderRadius:[10,10,0,0],color:a(t.palette.chGrey[300])}}],grid:{left:20,right:"-10%",top:0,bottom:"5%"}}),[t.palette,a,s]);return e.jsx(j,{echarts:k,option:l,sx:i})},Qt=()=>e.jsx(x,{sx:{p:{xs:3,md:5},flex:1},children:e.jsxs(n,{direction:"column",sx:{rowGap:2,height:"100%",justifyContent:"space-between"},children:[e.jsx(v,{title:"Revenue per visitor",subTitle:"Average income per visitors in your website",actionComponent:e.jsx(D,{}),sx:{mb:0}}),e.jsx(_,{amount:63.02,increment:-1.03,chart:e.jsx(Ct,{data:ot,sx:{height:"100% !important",width:"50%"}})})]})}),$t=()=>e.jsxs(e.Fragment,{children:[e.jsxs(d,{sx:{display:"grid",gridTemplateColumns:"repeat(12, 1fr)",gridTemplateRows:"repeat(3, auto)"},children:[e.jsx(d,{sx:{gridColumn:{xs:"span 12",md:"span 6",lg:"span 5",xl:"span 3"},gridRow:"span 3"},children:e.jsx(Ue,{stats:Te,meetingSchedules:Fe})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",md:"span 6",lg:"span 7",xl:"span 5"},gridRow:{md:"span 2"},order:{xl:1}},children:e.jsx(We,{title:"Boost your USD balance",subtitle:"by 2.5%"})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",md:"span 6",lg:"span 7",xl:"span 4"},gridRow:{md:"span 1",xl:"span 2"}},children:e.jsxs(n,{direction:{xs:"column",sm:"row",md:"column"},sx:{height:1},children:[e.jsx(bt,{}),e.jsx(Qt,{})]})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",xl:"span 9"},gridRow:{lg:"span 1"},order:1},children:e.jsx(ct,{})})]}),e.jsxs(d,{sx:{display:"grid",gridTemplateColumns:"repeat(12, 1fr)",gridTemplateRows:"repeat(3, auto)"},children:[e.jsx(d,{sx:{gridColumn:{xs:"span 12",xl:"span 8"},gridRow:"span 1"},children:e.jsx(Pt,{})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",xl:"span 6"},gridRow:"span 1",order:{xl:1}},children:e.jsx(wt,{})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",md:"span 6",xl:"span 4"},gridRow:"span 1"},children:e.jsx(gt,{})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",md:"span 6"},gridRow:"span 1",order:{xl:1}},children:e.jsx(vt,{})}),e.jsx(d,{sx:{gridColumn:{xs:"span 12",xl:"span 6"},gridRow:"span 2"},children:e.jsx(nt,{})})]})]});export{$t as default};
