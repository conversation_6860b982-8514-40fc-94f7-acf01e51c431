<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Lấy ngôn ngữ từ session hoặc mặc định
        $locale = session('locale', config('app.locale'));

        // Kiểm tra nếu có tham số locale trong URL
        if ($request->has('locale')) {
            $requestedLocale = $request->get('locale');
            if (in_array($requestedLocale, ['vi', 'en', 'zh'])) {
                $locale = $requestedLocale;
                session(['locale' => $locale]);
            }
        }

        // Thiết lập ngôn ngữ cho ứng dụng
        app()->setLocale($locale);

        return $next($request);
    }
}
