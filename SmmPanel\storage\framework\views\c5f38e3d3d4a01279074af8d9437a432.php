<?php $__env->startSection('title', __('auth.register') . ' - SMM Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-header">
    <h1><?php echo e(__('auth.register')); ?></h1>
    <p><?php echo e(__('common.welcome')); ?> SMM Panel</p>
</div>

<?php if($errors->any()): ?>
    <div class="alert alert-error">
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div><?php echo e($error); ?></div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php endif; ?>

<form method="POST" action="<?php echo e(route('register')); ?>">
    <?php echo csrf_field(); ?>
    
    <div class="form-group">
        <label for="name" class="form-label"><?php echo e(__('auth.name')); ?></label>
        <input 
            type="text" 
            id="name" 
            name="name" 
            class="form-input" 
            value="<?php echo e(old('name')); ?>" 
            required 
            autocomplete="name"
            placeholder="<?php echo e(__('auth.name')); ?>"
        >
    </div>

    <div class="form-group">
        <label for="email" class="form-label"><?php echo e(__('auth.email')); ?></label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="<?php echo e(old('email')); ?>" 
            required 
            autocomplete="email"
            placeholder="<?php echo e(__('auth.email')); ?>"
        >
    </div>

    <div class="form-group">
        <label for="password" class="form-label"><?php echo e(__('auth.password')); ?></label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            required 
            autocomplete="new-password"
            placeholder="<?php echo e(__('auth.password')); ?>"
        >
    </div>

    <div class="form-group">
        <label for="password_confirmation" class="form-label"><?php echo e(__('auth.confirm_password')); ?></label>
        <input 
            type="password" 
            id="password_confirmation" 
            name="password_confirmation" 
            class="form-input" 
            required 
            autocomplete="new-password"
            placeholder="<?php echo e(__('auth.confirm_password')); ?>"
        >
    </div>

    <button type="submit" class="btn-primary">
        <?php echo e(__('auth.register')); ?>

    </button>
</form>

<div class="auth-links">
    <div>
        <?php echo e(__('auth.already_have_account')); ?> 
        <a href="<?php echo e(route('login')); ?>"><?php echo e(__('auth.sign_in_here')); ?></a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('auth.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Làm\SmmPanel\resources\views/auth/register.blade.php ENDPATH**/ ?>