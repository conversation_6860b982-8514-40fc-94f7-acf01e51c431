import{_ as T,ai as A,aj as B,Z as w,c as S,ak as k,E as R,al as E,K as V,am as O}from"./ReactEchart-C_a4bTea.js";var D=function(r){T(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.layoutMode={type:"box",ignoreSize:!0},t}return e.type="title",e.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},e}(A),I=function(r){T(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,W,p){if(this.group.removeAll(),!!t.get("show")){var a=this.group,d=t.getModel("textStyle"),v=t.getModel("subtextStyle"),g=t.get("textAlign"),i=B(t.get("textBaseline"),t.get("textVerticalAlign")),l=new w({style:S(d,{text:t.get("text"),fill:d.getTextColor()},{disableBox:!0}),z2:10}),C=l.getBoundingRect(),f=t.get("subtext"),u=new w({style:S(v,{text:f,fill:v.getTextColor(),y:C.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),c=t.get("link"),h=t.get("sublink"),x=t.get("triggerEvent",!0);l.silent=!c&&!x,u.silent=!h&&!x,c&&l.on("click",function(){k(c,"_"+t.get("target"))}),h&&u.on("click",function(){k(h,"_"+t.get("subtarget"))}),R(l).eventData=R(u).eventData=x?{componentType:"title",componentIndex:t.componentIndex}:null,a.add(l),f&&a.add(u);var o=a.getBoundingRect(),y=t.getBoxLayoutParams();y.width=o.width,y.height=o.height;var n=E(y,{width:p.getWidth(),height:p.getHeight()},t.get("padding"));g||(g=t.get("left")||t.get("right"),g==="middle"&&(g="center"),g==="right"?n.x+=n.width:g==="center"&&(n.x+=n.width/2)),i||(i=t.get("top")||t.get("bottom"),i==="center"&&(i="middle"),i==="bottom"?n.y+=n.height:i==="middle"&&(n.y+=n.height/2),i=i||"top"),a.x=n.x,a.y=n.y,a.markRedraw();var b={align:g,verticalAlign:i};l.setStyle(b),u.setStyle(b),o=a.getBoundingRect();var s=n.margin,m=t.getItemStyle(["color","opacity"]);m.fill=t.get("backgroundColor");var z=new V({shape:{x:o.x-s[3],y:o.y-s[0],width:o.width+s[1]+s[3],height:o.height+s[0]+s[2],r:t.get("borderRadius")},style:m,subPixelOptimize:!0,silent:!0});a.add(z)}},e.type="title",e}(O);function G(r){r.registerComponentModel(D),r.registerComponentView(I)}export{G as i};
