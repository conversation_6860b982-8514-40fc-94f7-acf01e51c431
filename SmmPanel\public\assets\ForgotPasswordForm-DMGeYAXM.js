import{j as e,D as T,n as z,d as b,T as a,B as n,S as f,R as W,p as B,L as d,bo as E,r as g,a2 as A,Q as s,bi as F,a0 as $,a as I}from"./index-CP4gzJXp.js";import{a as L}from"./index.esm-Bw9oClnr.js";import{o as q}from"./yup-Bdh5_3QG.js";import{u as P}from"./useCountdown-BEaApQg6.js";import{c as R,a as V}from"./index.esm-CVsSWzb0.js";import{D as G}from"./DialogContent-C824DSl1.js";import{A as O}from"./Alert-BWvPB4gW.js";import{D as J}from"./DialogTitle-jz6gg1km.js";import{D as M}from"./DialogContentText-saOMJNKn.js";import{V as N}from"./ViewOnlyAlert-CkXljFy_.js";const Q="/assets/8-dark-BnbDkJnY.webp",Y="/assets/8-light-BBntymCk.webp",H=({open:o,handleClose:r,email:l,time:t,handleSendAgain:c})=>e.jsxs(T,{open:o,onClose:r,fullWidth:!0,sx:{[`& .${E.root}`]:{borderRadius:4,maxWidth:515}},children:[e.jsxs(G,{sx:{textAlign:"center",p:5},children:[e.jsx(z,{"aria-label":"close",onClick:r,sx:{position:"absolute",right:32,top:24},children:e.jsx(b,{icon:"material-symbols:close-rounded",sx:{fontSize:20}})}),e.jsx(O,{severity:"info",icon:e.jsx(b,{icon:"material-symbols:info-outline-rounded"}),sx:{mb:6,mt:5},children:e.jsxs(a,{children:[e.jsx(n,{component:"span",sx:{fontWeight:700},children:"Note:"})," ","This is a demo feature."]})}),e.jsx(f,{sx:{justifyContent:"center",mb:3},children:e.jsx(W,{src:{light:Y,dark:Q},alt:"",width:320,height:240})}),e.jsx(J,{sx:{typography:"h5",fontWeight:500},children:"Check your mailbox!"}),e.jsxs(M,{sx:{typography:"body1",color:"text.primary"},children:["An email containing a password reset link has been sent to your email address"," ",e.jsx(n,{component:"span",sx:{fontWeight:500},children:l})]})]}),e.jsx(B,{sx:{justifyContent:"center",p:5,pt:0},children:e.jsxs(a,{variant:"caption",sx:{fontWeight:500,color:"text.secondary"},children:["Didn’t receive the code?"," ",e.jsxs(d,{href:"#!",onClick:c,sx:[t>0&&{pointerEvents:"none"}],children:["Send again",t>0?` in ${t} s`:""]})]})})]}),K=R({email:V().email("Email must be a valid email").required("This field is required")}).required(),ne=({provider:o="jwt",handleSendResetLink:r})=>{var j;const[l,t]=g.useState(!1),[c,x]=g.useState(!1),{enqueueSnackbar:y}=A(),{time:m,startTimer:k}=P(),{register:S,handleSubmit:w,watch:h,setError:C,formState:{errors:u,isSubmitting:D}}=L({resolver:q(K)}),p=async v=>{try{const i=await r({email:v.email});t(!0),x(!0),i!=null&&i.message&&y(i.message,{variant:"success"}),k(30,()=>{t(!1)})}catch(i){C("email",{type:"manual",message:i.message})}};return e.jsxs(f,{direction:"column",sx:{flex:1,height:1,alignItems:"center",justifyContent:"space-between",pt:{md:10},pb:10},children:[e.jsx(n,{sx:{display:{xs:"none",md:"block"}}}),e.jsxs(s,{container:!0,sx:{maxWidth:"35rem",rowGap:6,p:{xs:3,sm:5},mb:5},children:[o==="firebase"&&!0&&e.jsx(s,{size:12,sx:{mb:1},children:e.jsx(N,{docLink:`${F.authentication}#firebase`})}),e.jsxs(s,{size:12,children:[e.jsx(a,{variant:"h4",sx:{mb:2},children:"Forgot Password?"}),e.jsx(a,{variant:"body1",children:"Please enter your email address and an email with a link to reset your password will be sent."})]}),e.jsx(s,{size:12,children:e.jsx(n,{component:"form",noValidate:!0,onSubmit:w(p),children:e.jsxs(s,{container:!0,children:[e.jsx(s,{sx:{mb:4},size:12,children:e.jsx($,{fullWidth:!0,size:"large",id:"email",type:"email",label:"Email",variant:"filled",error:!!u.email,helperText:e.jsx(e.Fragment,{children:(j=u.email)==null?void 0:j.message}),...S("email")})}),e.jsx(s,{sx:{mb:2},size:12,children:e.jsxs(I,{type:"submit",loading:D,fullWidth:!0,size:"large",variant:"contained",disabled:l,children:["Send Reset Link ",m>0?` in ${m} s`:""]})}),e.jsx(s,{sx:{mb:6},size:12,children:e.jsxs(a,{variant:"subtitle2",sx:{color:"text.secondary"},children:["Don't have access to that email?",e.jsx(d,{href:"#!",sx:{ml:1},children:"Try alternate methods"})]})})]})})})]}),e.jsx(d,{href:"#!",variant:"subtitle2",children:"Trouble signing in?"}),e.jsx(H,{open:c,handleClose:()=>x(!1),email:h("email"),time:m,handleSendAgain:()=>p({email:h("email")})})]})};export{ne as F};
