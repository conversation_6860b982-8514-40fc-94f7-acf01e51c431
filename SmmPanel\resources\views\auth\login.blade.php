@extends('auth.layout')

@section('title', __('auth.login') . ' - SMM Panel')

@section('content')
<div class="auth-header">
    <h1>{{ __('auth.login') }}</h1>
    <p>{{ __('common.welcome') }} SMM Panel</p>
</div>

@if ($errors->any())
    <div class="alert alert-error">
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    </div>
@endif

@if (session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<form method="POST" action="{{ route('login') }}">
    @csrf
    
    <div class="form-group">
        <label for="email" class="form-label">{{ __('auth.email') }}</label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="{{ old('email') }}" 
            required 
            autocomplete="email"
            placeholder="{{ __('auth.email') }}"
        >
    </div>

    <div class="form-group">
        <label for="password" class="form-label">{{ __('auth.password') }}</label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            required 
            autocomplete="current-password"
            placeholder="{{ __('auth.password') }}"
        >
    </div>

    <div class="checkbox-group">
        <input type="checkbox" id="remember" name="remember" {{ old('remember') ? 'checked' : '' }}>
        <label for="remember">{{ __('auth.remember_me') }}</label>
    </div>

    <button type="submit" class="btn-primary">
        {{ __('auth.login') }}
    </button>
</form>

<div class="auth-links">
    <div style="margin-bottom: 0.5rem;">
        <a href="{{ route('forgot-password') }}">{{ __('auth.forgot_password') }}</a>
    </div>
    <div>
        {{ __('auth.dont_have_account') }} 
        <a href="{{ route('register') }}">{{ __('auth.sign_up_here') }}</a>
    </div>
</div>
@endsection
