{"name": "illuminate/bus", "description": "The Illuminate Bus package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/collections": "^12.0", "illuminate/contracts": "^12.0", "illuminate/pipeline": "^12.0", "illuminate/support": "^12.0"}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^12.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}