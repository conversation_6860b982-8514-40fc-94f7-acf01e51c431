import{j as e,S as i,T as t}from"./index-CP4gzJXp.js";const x=({title:a,subTitle:r,actionComponent:n,...s})=>e.jsxs(i,{spacing:2,...s,sx:[{justifyContent:"space-between",alignItems:"flex-start",mb:4},...Array.isArray(s.sx)?s.sx:[s.sx]],children:[e.jsxs("div",{children:[e.jsx(t,{variant:"h6",sx:{mb:1,whiteSpace:"nowrap"},children:a}),e.jsx(t,{variant:"subtitle2",component:"p",fontWeight:"regular",color:"text.secondary",children:r})]}),n]});export{x as S};
