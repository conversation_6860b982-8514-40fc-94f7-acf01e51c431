<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('common.dashboard') }} - SMM Panel</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&family=Spline+Sans+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Aurora Theme CSS -->
    <link rel="stylesheet" href="{{ asset('assets/index-JIMBpuqK.css') }}">
    
    <style>
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 0;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a202c;
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #374151;
        }
        
        .btn-logout {
            background: #dc2626;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-logout:hover {
            background: #b91c1c;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .welcome-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            text-align: center;
        }
        
        .welcome-card h1 {
            color: #1a202c;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .welcome-card p {
            color: #718096;
            font-size: 1rem;
        }
        
        .language-selector {
            margin-left: 1rem;
        }
        
        .language-selector select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-brand">
            SMM Panel
        </div>
        <div class="navbar-nav">
            <div class="user-info">
                {{ __('common.welcome') }}, {{ Auth::user()->name }}
            </div>
            
            <div class="language-selector">
                <select onchange="changeLanguage(this.value)">
                    <option value="vi" {{ app()->getLocale() == 'vi' ? 'selected' : '' }}>Tiếng Việt</option>
                    <option value="en" {{ app()->getLocale() == 'en' ? 'selected' : '' }}>English</option>
                    <option value="zh" {{ app()->getLocale() == 'zh' ? 'selected' : '' }}>中文</option>
                </select>
            </div>
            
            <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                @csrf
                <button type="submit" class="btn-logout">{{ __('auth.logout') }}</button>
            </form>
        </div>
    </nav>

    <div class="container">
        @if (session('success'))
            <div style="background: #f0fdf4; border: 1px solid #bbf7d0; color: #16a34a; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                {{ session('success') }}
            </div>
        @endif

        <div class="welcome-card">
            <h1>{{ __('common.welcome') }} {{ __('common.dashboard') }}</h1>
            <p>{{ __('common.welcome') }} {{ Auth::user()->name }}! Đây là trang dashboard của SMM Panel.</p>
            <p>Hệ thống authentication đã hoạt động thành công với đa ngôn ngữ.</p>
        </div>
    </div>

    <script>
        function changeLanguage(locale) {
            const url = new URL(window.location);
            url.searchParams.set('locale', locale);
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
