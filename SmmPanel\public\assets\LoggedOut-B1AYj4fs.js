import{j as e,S as s,B as n,T as a,a as t,b as i,L as o}from"./index-CP4gzJXp.js";const l=()=>e.jsxs(s,{direction:"column",sx:{flex:1,height:1,alignItems:"center",justifyContent:"space-between",p:{xs:4,md:10}},children:[e.jsx(n,{sx:{display:{xs:"none",md:"block"}}}),e.jsxs(n,{sx:{maxWidth:370},children:[e.jsx(a,{variant:"h4",children:"You have been logged out."}),e.jsx(a,{variant:"h2",sx:{mb:2},children:"See you soon!"}),e.jsx(a,{sx:{mb:6},children:"We are sad to see you go away but hey, you can log back in anytime you want!"}),e.jsx(t,{variant:"contained",href:i.login,color:"primary",fullWidth:!0,children:"Log back in"})]}),e.jsx(o,{href:"#!",variant:"subtitle2",children:"Trouble signing in?"})]});export{l as default};
