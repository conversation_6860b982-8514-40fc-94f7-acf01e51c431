import{aG as l,j as A,Q as o,a,bj as e,b9 as c,R as t,bk as f,bl as h,bf as i,bm as g}from"./index-CP4gzJXp.js";const w="data:image/webp;base64,UklGRi4DAABXRUJQVlA4WAoAAAAQAAAAHwAAHwAAQUxQSL0AAAABgFTbbhzpQzAEQSgIghAIZtJhUGHgMEgxEARDMARB+At56mkfEROAf1ZeKvvSYU6SbnnP0TjZ8obCxfdS5eq98ma/lsfCjcXM7pUAQG7eWG3BFUPFambM2G+h4Bs9aE9nZSAk6ehztgw02I7PXN1hAwm+4zOAk6T2zq6Fa2Th6fWfkEdH4DEjjDJCC64jacEweQSySEhfzqgzuDqkPaU6uxemU+3N1jQHXCuWsJzbjJ/Yms07diZsF1XBPwsAVlA4IEoCAACwDQCdASogACAAAAAAJbA2wBDlWHcF9A/Dn9Sf5Lz5+gXZD9iMpZ4d/tf4ubot/Rvww4AH6cfsRwgH6V+oB6gHoAfqX6KX6x/AZ+tf/Y/1XwAfxv+d/fwVdT/Y+X3Ms/y3AAHVqofD7CXS7uH+1pvUZZo0OFE8AP766tUMAP9TnPdXCty//zS61/Jqg/5cztf/+SxEfq6M/mKkvhW+9sv2CZ2dTV491aXwwShfQTr2VBKMb5q7zZPf/iuf+V/m68hxho8vRtYQxolQm2bA2mq7q3VUYVeJJoTt1mzo2Us/yfksga9E+FDK/6700pqDSqaThowrlZHa/esbujMKig8L9/hOUSrQyfyDuPloekpQuQffF///3bqmrB0OYGqq/7al6Nf/kI8JfR08GkZlvufqXtBNJP+8CJNvyKHIm4rrKJP3sP+4eNkeFiQsYkLK9PirJtBNRhUcCJ/f1Prm3lkwoMlNxNyef9A5hiYhu9qzmGPL/FBYZqsEXi3h4cRfKy2su4fEjX/6W+9aVQsmdbuPk3+NYb7v+BjN3jkuadaQl8y1oT93shXSokqTsT9D5wEWCALdr2+of/37GPOT9fGQ2hAI4zV7nK5/1GvFcol34//ziU8qNcAK5oqGGxbTiH4ZVR+2RLmOPDGKJPQYLTz/bTDZnw5Ic8CT9v8CIbf1Hfk3Z4YhWOGb3jzi2gpU2AOtuAdAFxM0T+Zlx7OmKhMsPCsXV04E9JhIdT//GaQWkTheIBCpTm1zezX3eoqT/lcJ8rRHqcjMAAAA",d="data:image/webp;base64,UklGRrIBAABXRUJQVlA4WAoAAAAQAAAAHwAAHwAAQUxQSGIAAAABZ0CQbRu6wYuwiIhROAU2tbYtec+f4J90/Fc0AhGYmJ0wFNEEloBDK/c5ov+K3LZtwnNm+wo8zx8WRrfktQDCeWFOS974S+E1C1UueQUAqhdErxzf7RfDzRcsXfKCB74NAFZQOCAqAQAAUAoAnQEqIAAgAAAAACWwAnTKEcDeLfgB+l/9G5wzQLs7+oH9AxQL5B+JPsA/gH4QdQB/IPwl4AH6AfyD2wP4B/AOqA/gHogdYB+gHoKf2D/HdYSDYAFoODrGAAD+yt0PZ/3OZYiw//wdMtYNaYgoe5wwstaaaTIFLuGD/9h+Hn941blQTCsIacSqMLwMk5Hs8KU0fxiJIXkl4i+C7aqqHnsoqTqnYfo2YU+QS9LSAwnf7+T1JRcJ8q+GyM6z5Qc7p9hpe9UcnY0KxzfYd89ziqg2zwb3gbpORGLvuY+4+iAAvRqbGInoPPR+1J+wFRNoKLp0sViT//30b1Y1UJYFdHXAwyTqvbIzj8LKJEiPaXpO4oZaWgeX1DeonGOH6WmrK/xrhKkx+4YAAA==",x=()=>{const n=l();return A.jsxs(o,{container:!0,spacing:2,sx:{alignItems:"center"},children:[A.jsx(o,{size:{xs:12,lg:6},children:A.jsx(a,{fullWidth:!0,variant:"contained",color:"neutral",size:"large",sx:{flex:1,whiteSpace:"nowrap"},startIcon:A.jsx(t,{src:w,height:21,width:21,alt:"icon"}),onClick:async()=>{await e(i,g),n(c.root)},children:"Sign in with google"})}),A.jsx(o,{size:{xs:12,lg:6},children:A.jsx(a,{fullWidth:!0,variant:"contained",color:"neutral",size:"large",sx:{flex:1,whiteSpace:"nowrap"},startIcon:A.jsx(t,{src:d,height:21,width:21,alt:"icon"}),onClick:async()=>{const s=await e(i,h);console.log({res:s});const r=f.credentialFromResult(s);console.log({credential:r})},children:"Sign in with Microsoft"})})]})};export{x as S};
