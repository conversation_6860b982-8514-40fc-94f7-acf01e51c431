@extends('auth.layout')

@section('title', __('auth.register') . ' - SMM Panel')

@section('content')
<div class="auth-header">
    <h1>{{ __('auth.register') }}</h1>
    <p>{{ __('common.welcome') }} SMM Panel</p>
</div>

@if ($errors->any())
    <div class="alert alert-error">
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    </div>
@endif

<form method="POST" action="{{ route('register') }}">
    @csrf
    
    <div class="form-group">
        <label for="name" class="form-label">{{ __('auth.name') }}</label>
        <input 
            type="text" 
            id="name" 
            name="name" 
            class="form-input" 
            value="{{ old('name') }}" 
            required 
            autocomplete="name"
            placeholder="{{ __('auth.name') }}"
        >
    </div>

    <div class="form-group">
        <label for="email" class="form-label">{{ __('auth.email') }}</label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="{{ old('email') }}" 
            required 
            autocomplete="email"
            placeholder="{{ __('auth.email') }}"
        >
    </div>

    <div class="form-group">
        <label for="password" class="form-label">{{ __('auth.password') }}</label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            required 
            autocomplete="new-password"
            placeholder="{{ __('auth.password') }}"
        >
    </div>

    <div class="form-group">
        <label for="password_confirmation" class="form-label">{{ __('auth.confirm_password') }}</label>
        <input 
            type="password" 
            id="password_confirmation" 
            name="password_confirmation" 
            class="form-input" 
            required 
            autocomplete="new-password"
            placeholder="{{ __('auth.confirm_password') }}"
        >
    </div>

    <button type="submit" class="btn-primary">
        {{ __('auth.register') }}
    </button>
</form>

<div class="auth-links">
    <div>
        {{ __('auth.already_have_account') }} 
        <a href="{{ route('login') }}">{{ __('auth.sign_in_here') }}</a>
    </div>
</div>
@endsection
