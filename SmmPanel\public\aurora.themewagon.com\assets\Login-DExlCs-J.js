import{b6 as u,aG as g,b7 as m,j as p,b8 as d,aB as o,b9 as c}from"./index-CP4gzJXp.js";import{L as f}from"./LoginForm-0FAfm8GS.js";import"./index.esm-Bw9oClnr.js";import"./yup-Bdh5_3QG.js";import"./index.esm-CVsSWzb0.js";import"./PasswordTextField-Bu7nMFM0.js";import"./Alert-BWvPB4gW.js";import"./ViewOnlyAlert-CkXljFy_.js";import"./SocialAuth-DUKTxlfk.js";const P=()=>{const{setSession:s}=u(),a=g(),{trigger:r}=m(),e=async i=>{const t=await r(i).catch(n=>{throw new Error(n.data.message)});t&&(s(t.user,t.authToken),a(c.root))};return p.jsx(f,{handleLogin:e,signUpLink:o.defaultJwtSignup,forgotPasswordLink:o.defaultJwtForgotPassword,defaultCredential:d})};export{P as default};
