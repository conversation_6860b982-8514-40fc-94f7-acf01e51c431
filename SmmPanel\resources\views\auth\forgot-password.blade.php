@extends('auth.layout')

@section('title', __('auth.forgot_password') . ' - SMM Panel')

@section('content')
<div class="auth-header">
    <h1>{{ __('auth.reset_password') }}</h1>
    <p>{{ __('auth.send_reset_link') }}</p>
</div>

@if ($errors->any())
    <div class="alert alert-error">
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    </div>
@endif

@if (session('status'))
    <div class="alert alert-success">
        {{ session('status') }}
    </div>
@endif

<form method="POST" action="{{ route('password.email') }}">
    @csrf
    
    <div class="form-group">
        <label for="email" class="form-label">{{ __('auth.email') }}</label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="{{ old('email') }}" 
            required 
            autocomplete="email"
            placeholder="{{ __('auth.email') }}"
        >
    </div>

    <button type="submit" class="btn-primary">
        {{ __('auth.send_reset_link') }}
    </button>
</form>

<div class="auth-links">
    <div>
        <a href="{{ route('login') }}">{{ __('auth.sign_in_here') }}</a>
    </div>
</div>
@endsection
