<?php $__env->startSection('title', __('auth.login') . ' - SMM Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-header">
    <h1><?php echo e(__('auth.login')); ?></h1>
    <p><?php echo e(__('common.welcome')); ?> SMM Panel</p>
</div>

<?php if($errors->any()): ?>
    <div class="alert alert-error">
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div><?php echo e($error); ?></div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php endif; ?>

<?php if(session('success')): ?>
    <div class="alert alert-success">
        <?php echo e(session('success')); ?>

    </div>
<?php endif; ?>

<form method="POST" action="<?php echo e(route('login')); ?>">
    <?php echo csrf_field(); ?>
    
    <div class="form-group">
        <label for="email" class="form-label"><?php echo e(__('auth.email')); ?></label>
        <input 
            type="email" 
            id="email" 
            name="email" 
            class="form-input" 
            value="<?php echo e(old('email')); ?>" 
            required 
            autocomplete="email"
            placeholder="<?php echo e(__('auth.email')); ?>"
        >
    </div>

    <div class="form-group">
        <label for="password" class="form-label"><?php echo e(__('auth.password')); ?></label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            required 
            autocomplete="current-password"
            placeholder="<?php echo e(__('auth.password')); ?>"
        >
    </div>

    <div class="checkbox-group">
        <input type="checkbox" id="remember" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
        <label for="remember"><?php echo e(__('auth.remember_me')); ?></label>
    </div>

    <button type="submit" class="btn-primary">
        <?php echo e(__('auth.login')); ?>

    </button>
</form>

<div class="auth-links">
    <div style="margin-bottom: 0.5rem;">
        <a href="<?php echo e(route('forgot-password')); ?>"><?php echo e(__('auth.forgot_password')); ?></a>
    </div>
    <div>
        <?php echo e(__('auth.dont_have_account')); ?> 
        <a href="<?php echo e(route('register')); ?>"><?php echo e(__('auth.sign_up_here')); ?></a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('auth.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Làm\SmmPanel\resources\views/auth/login.blade.php ENDPATH**/ ?>