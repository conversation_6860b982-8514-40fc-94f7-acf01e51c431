import{j as e,T as t,B as o,b8 as b,d as q,S as a,Q as s,L as n,o as I,a0 as P,i as V,a9 as k,a as E}from"./index-CP4gzJXp.js";import{a as B}from"./index.esm-Bw9oClnr.js";import{o as D}from"./yup-Bdh5_3QG.js";import{c as G,a as g}from"./index.esm-CVsSWzb0.js";import{P as R}from"./PasswordTextField-Bu7nMFM0.js";import{A as f}from"./Alert-BWvPB4gW.js";import{V as $}from"./ViewOnlyAlert-CkXljFy_.js";import{S as J}from"./SocialAuth-DUKTxlfk.js";const O=()=>e.jsxs(f,{severity:"info",sx:{mb:4},icon:e.jsx(q,{icon:"material-symbols:info-outline-rounded"}),children:[e.jsxs(t,{variant:"body2",children:["Use Email :"," ",e.jsx(o,{component:"span",sx:{fontWeight:700},children:b.email})]}),e.jsxs(t,{variant:"body2",children:["Password :"," ",e.jsx(o,{component:"span",sx:{fontWeight:700},children:b.password})]})]}),Q=G({email:g().email("Please provide a valid email address.").required("This field is required"),password:g().required("This field is required")}).required(),_=({provider:w="jwt",handleLogin:y,signUpLink:v,forgotPasswordLink:l,socialAuth:z=!0,rememberDevice:S=!0,defaultCredential:r})=>{var m,x,d,h,j,u;const{register:c,handleSubmit:F,setError:T,formState:{errors:i,isSubmitting:L}}=B({resolver:D(Q)}),A=async W=>{await y(W).catch(p=>{p&&T("root.credential",{type:"manual",message:p.message})})};return e.jsxs(a,{direction:"column",sx:{height:1,alignItems:"center",justifyContent:"space-between",pt:{md:10},pb:10},children:[e.jsx("div",{}),e.jsxs(s,{container:!0,sx:{maxWidth:"35rem",rowGap:4,p:{xs:3,sm:5},mb:5},children:[w==="firebase"&&!0&&e.jsx(s,{size:12,sx:{mb:1},children:e.jsx($,{docLink:"https://aurora.themewagon.com/documentation/authentication#firebase"})}),e.jsx(s,{size:12,children:e.jsxs(a,{direction:{xs:"column",sm:"row"},spacing:1,sx:{justifyContent:"space-between",alignItems:{xs:"flex-start",sm:"flex-end"}},children:[e.jsx(t,{variant:"h4",children:"Log in"}),e.jsxs(t,{variant:"subtitle2",sx:{color:"text.secondary"},children:["Don't have an account?",e.jsx(n,{href:v,sx:{ml:1},children:"Sign up"})]})]})}),z&&e.jsxs(e.Fragment,{children:[e.jsx(s,{size:12,children:e.jsx(J,{})}),e.jsx(s,{size:12,children:e.jsx(I,{sx:{color:"text.secondary"},children:"or use email"})})]}),e.jsx(s,{size:12,children:e.jsxs(o,{component:"form",noValidate:!0,onSubmit:F(A),children:[((x=(m=i.root)==null?void 0:m.credential)==null?void 0:x.message)&&e.jsx(f,{severity:"error",sx:{mb:3},children:(h=(d=i.root)==null?void 0:d.credential)==null?void 0:h.message}),r&&e.jsx(O,{}),e.jsxs(s,{container:!0,children:[e.jsx(s,{sx:{mb:3},size:12,children:e.jsx(P,{fullWidth:!0,size:"large",id:"email",type:"email",label:"Email",defaultValue:r==null?void 0:r.email,error:!!i.email,helperText:e.jsx(e.Fragment,{children:(j=i.email)==null?void 0:j.message}),...c("email")})}),e.jsx(s,{sx:{mb:2.5},size:12,children:e.jsx(R,{fullWidth:!0,size:"large",id:"password",label:"Password",defaultValue:r==null?void 0:r.password,error:!!i.password,helperText:e.jsx(e.Fragment,{children:(u=i.password)==null?void 0:u.message}),...c("password")})}),e.jsx(s,{sx:{mb:6},size:12,children:e.jsxs(a,{spacing:1,sx:{justifyContent:"space-between",alignItems:"center"},children:[S&&e.jsx(V,{control:e.jsx(k,{name:"checked",color:"primary",size:"small"}),label:e.jsx(t,{variant:"subtitle2",sx:{color:"text.secondary"},children:"Remember this device"})}),l&&e.jsx(n,{href:l,variant:"subtitle2",children:"Forgot Password?"})]})}),e.jsx(s,{size:12,children:e.jsx(E,{fullWidth:!0,type:"submit",size:"large",variant:"contained",loading:L,children:"Log in"})})]})]})})]}),e.jsx(n,{href:"#!",variant:"subtitle2",children:"Trouble signing in?"})]})};export{_ as L};
